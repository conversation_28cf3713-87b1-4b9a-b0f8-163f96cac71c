﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="btnSave" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="AucuneStructureSelectionnee" xml:space="preserve">
    <value>keine Struktur ausgewählt</value>
  </data>
  <data name="AfficherLEtage" xml:space="preserve">
    <value>Anzeige Etage</value>
  </data>
  <data name="AfficherLEtageModeNormal" xml:space="preserve">
    <value>Anzeige Etage wenn vorhanden</value>
  </data>
  <data name="AfficherLEtageModezaperLEtage" xml:space="preserve">
    <value>Auf Etage verzichten</value>
  </data>
  <data name="AfficherLEtageModezapperLEtage" xml:space="preserve">
    <value>Auf Etage verzichten</value>
  </data>
  <data name="AttribuerUneImageSurUneDesSéancesDeCetteManifestation" xml:space="preserve">
    <value>Ein Bild einem der Spieltage für diese Veranstaltung zuweisen</value>
  </data>
  <data name="CodeLangue" xml:space="preserve">
    <value>Sprachcode</value>
  </data>
  <data name="Commentaire" xml:space="preserve">
    <value>Kommentar</value>
  </data>
  <data name="CommentaireBasDePage" xml:space="preserve">
    <value>Kommentar Seite unten</value>
  </data>
  <data name="CommentaireHautDePage" xml:space="preserve">
    <value>Kommentar Seite oben</value>
  </data>
  <data name="Copier" xml:space="preserve">
    <value>Kopieren</value>
  </data>
  <data name="CopierLeCommentaire" xml:space="preserve">
    <value>Kommentar kopieren</value>
  </data>
  <data name="DateDeLaSéance" xml:space="preserve">
    <value>Datum Spieltag</value>
  </data>
  <data name="delete" xml:space="preserve">
    <value>löschen</value>
  </data>
  <data name="Editer" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="EffacerCommentaire" xml:space="preserve">
    <value>Kommentar löschen</value>
  </data>
  <data name="ErrorPlusieursRegles" xml:space="preserve">
    <value>Achtung !! Es gibt mehrere Hauptregeln</value>
  </data>
  <data name="fichiervide" xml:space="preserve">
    <value>Datensatz ist leer</value>
  </data>
  <data name="FileUploadedSuccessfully" xml:space="preserve">
    <value>Datensatz erfolgreich hochgeladen</value>
  </data>
  <data name="go" xml:space="preserve">
    <value>ok</value>
  </data>
  <data name="HtmlAttendu" xml:space="preserve">
    <value>.html ist erforderlich</value>
  </data>
  <data name="IdDeLaSéance" xml:space="preserve">
    <value>Id Spieltag</value>
  </data>
  <data name="InvalidFileContent" xml:space="preserve">
    <value>Inhalt Datensatz ungültig</value>
  </data>
  <data name="InvalidFilenameSupplied" xml:space="preserve">
    <value>Name Datensatz ungültig</value>
  </data>
  <data name="LangueParDefaut" xml:space="preserve">
    <value>Sprache Standard</value>
  </data>
  <data name="LeFichierExiste" xml:space="preserve">
    <value>Datensatz existiert</value>
  </data>
  <data name="Manifestations" xml:space="preserve">
    <value>Veranstaltungen</value>
  </data>
  <data name="MiseAJourProperties" xml:space="preserve">
    <value>Aktualisierung</value>
  </data>
  <data name="ModeCalendrier" xml:space="preserve">
    <value>Modus Kalender</value>
  </data>
  <data name="ModeNormal" xml:space="preserve">
    <value>Modus Normal </value>
  </data>
  <data name="NomDeLaManifestation" xml:space="preserve">
    <value>Veranstaltung</value>
  </data>
  <data name="PathNotFound" xml:space="preserve">
    <value>Dateipfad nicht gefunden</value>
  </data>
  <data name="PermissionToUploadFileDenied" xml:space="preserve">
    <value>Zugrriffsrecht für Upload verweigert</value>
  </data>
  <data name="ReserveAucune" xml:space="preserve">
    <value>Keine Reserve</value>
  </data>
  <data name="ResultCashStatement" xml:space="preserve">
    <value>Kein Auftrag nach diesen Kriterien</value>
  </data>
  <data name="SeancesDeLaManif" xml:space="preserve">
    <value>Spieltage der Veranstaltung</value>
  </data>
  <data name="TypeDeChoixSéance" xml:space="preserve">
    <value>Art der Spieltageauswahl</value>
  </data>
  <data name="TypeLocalResource" xml:space="preserve">
    <value>Art</value>
  </data>
  <data name="UnableToUploadFileExceedsMaximumLimit" xml:space="preserve">
    <value>Kein Datenupload möglich, maximale Größe überschritten</value>
  </data>
  <data name="UpLoad" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="UploaderUnFichierHtmlDepuisVotreOrdinateur" xml:space="preserve">
    <value>Upload html-Datei von Ihrem Arbeitsplatz</value>
  </data>
  <data name="UploadLocalResource" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="Vider" xml:space="preserve">
    <value>leeren</value>
  </data>
  <data name="Visualiser" xml:space="preserve">
    <value>Anzeigen</value>
  </data>
  <data name="VisualiserEtModifierLeCommentaire" xml:space="preserve">
    <value>Anzeigen und Bearbeiten Kommentar</value>
  </data>
  <data name="VisualiserLocalResource" xml:space="preserve">
    <value>Anzeigen</value>
  </data>
  <data name="VosManifestations" xml:space="preserve">
    <value>Ihre Veranstaltungen</value>
  </data>
  <data name="VosSeancesSur" xml:space="preserve">
    <value>Ihre Spieltage für</value>
  </data>
  <data name="Valider" xml:space="preserve">
    <value>Bestätigen</value>
  </data>
  <data name="DeleteRuleSuccess" xml:space="preserve">
    <value>La règle a bien été supprimée</value>
  </data>
  <data name="DeleteRulesSuccess" xml:space="preserve">
    <value>Die Verkaufsregel wurden erfolgreich gelöscht</value>
  </data>
  <data name="ErrorDeleteRule" xml:space="preserve">
    <value>Während des Löschvorgangs ist ein Fehler aufgetreten</value>
  </data>
  <data name="ErrorDeleteRules" xml:space="preserve">
    <value>Die Verkaufsregeln wurden erfolgreich gelöscht</value>
  </data>
  <data name="lblActions" xml:space="preserve">
    <value>Aktionen</value>
    <comment>label des datatable Action</comment>
  </data>
  <data name="lblDeleteRulesSelected" xml:space="preserve">
    <value>Löschen der Regeln für alle ausgewählten Spieltage</value>
  </data>
  <data name="lblEndDateOfValidity" xml:space="preserve">
    <value>bis (vorher)</value>
  </data>
  <data name="lblEndDateOfValidityInt" xml:space="preserve">
    <value>bis (vorher)</value>
  </data>
  <data name="lblGPID" xml:space="preserve">
    <value>Id</value>
  </data>
  <data name="lblIsValide" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="lblMax" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="lblMin" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="NbDispo" xml:space="preserve">
    <value>Verfügb</value>
  </data>
  <data name="lblOperationDate" xml:space="preserve">
    <value>Datum OPeration</value>
  </data>
  <data name="lblStartDateOfValidityInt" xml:space="preserve">
    <value>von (vorher)</value>
  </data>
  <data name="lblStartDateOfValidity" xml:space="preserve">
    <value>von (vorher)</value>
  </data>
  <data name="lblTooltipUpdateOffer" xml:space="preserve">
    <value>Angebot aktualisieren</value>
  </data>
  <data name="lblTooltipUpdateProfilAcheteur" xml:space="preserve">
    <value>Käuferprofil aktualisieren</value>
  </data>
  <data name="CopyRulesSuccess" xml:space="preserve">
    <value>Die Verkaufsregeln wurden erfolgreich kopiert</value>
  </data>
  <data name="DeleteRuleError" xml:space="preserve">
    <value>Während des Löschvorgangs ist ein Fehler aufgetreten</value>
  </data>
  <data name="SuccessUpdateRules" xml:space="preserve">
    <value>Die Verkaufsregeln wurden erfolgreich kopiert</value>
  </data>
  <data name="deleteContrainte" xml:space="preserve">
    <value>Die Bedingung wurde erfolgreich gelöscht</value>
  </data>
  <data name="errorInsertContrainte" xml:space="preserve">
    <value>Während der Erfassung der Bedingung ist ein Fehler aufgetreten</value>
  </data>
  <data name="successInsertContrainte" xml:space="preserve">
    <value>Die Bedingung wurde erfolgreich hinzugefügt</value>
  </data>
  <data name="lblContrainteName" xml:space="preserve">
    <value>Name der Bedingung</value>
  </data>
  <data name="lblEndDate" xml:space="preserve">
    <value>Datum Ende</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="lblOfferName" xml:space="preserve">
    <value>Name Angebot</value>
  </data>
  <data name="lblSQL" xml:space="preserve">
    <value>SQL</value>
  </data>
  <data name="lblStartDate" xml:space="preserve">
    <value>Datum Beginn</value>
  </data>
  <data name="successUpdateOffer" xml:space="preserve">
    <value>Das Angebot wurde erfolgreich aktualisiert</value>
  </data>
  <data name="btnSaveProfilAcheteur" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="ErrorDeleteOffer" xml:space="preserve">
    <value>Während des Löschvorgangs ist ein Fehler aufgetreten</value>
  </data>
  <data name="ErrorUpdateProfilAcheteurForOffer" xml:space="preserve">
    <value>Während der Aktualsierung des Käuferprofils für dieses Angebot ist ein Fehler aufgetreten</value>
  </data>
  <data name="lblProfilAcheteurID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="lblProfilAcheteurName" xml:space="preserve">
    <value>Name Käuferprofil</value>
  </data>
  <data name="SuccessDeleteOffer" xml:space="preserve">
    <value>Das Angebot wurde erfolgreich gelöscht</value>
  </data>
  <data name="SuccessInsertOffer" xml:space="preserve">
    <value>Das Angebot wurde erfolgreich hinzugefügt</value>
  </data>
  <data name="SuccessUpdateProfilAcheteur" xml:space="preserve">
    <value>Die Aktualisierung des Käuferprofils wurde erfolgreich beendet</value>
  </data>
  <data name="SuccessUpdateProfilAcheteurForOffer" xml:space="preserve">
    <value>Die Aktualisierung des Käuferprofils für dieses Angebot wurde erfolgreich beendet</value>
  </data>
  <data name="AddRulesSelected" xml:space="preserve">
    <value>Regeln für alle ausgewählten Spieltage auswählen</value>
  </data>
  <data name="Annuler" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Kategorie</value>
  </data>
  <data name="CompteClient" xml:space="preserve">
    <value>Kundenkonto (für Rechnung)</value>
  </data>
  <data name="ConsommateurObligatoire" xml:space="preserve">
    <value>Verwender verpflichtend</value>
  </data>
  <data name="ErrorDeleteProfilAcheteur" xml:space="preserve">
    <value>Während des Löschvorgangs ist ein Fehler aufgetreten</value>
  </data>
  <data name="ErrorInsertProfilAcheteur" xml:space="preserve">
    <value>Während der Erfassung des Käuferprofils ist ein Fehler aufgetreten</value>
  </data>
  <data name="ErrorUpdateContrainteForOffer" xml:space="preserve">
    <value>Fehler während der Aktualisierung des Angebots mit dieser Bedingung</value>
  </data>
  <data name="ErrorUpdateProfilAcheteur" xml:space="preserve">
    <value>Während der Aktualisierung des Käuferprofils ist ein Fehler aufgetreten</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Vorname</value>
  </data>
  <data name="Groupe" xml:space="preserve">
    <value>Veranstaltungsgruppen</value>
  </data>
  <data name="Libelle" xml:space="preserve">
    <value>Bezeichnung</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value> Bitte haben Sie Geduld, der Vorgang ist in Bearbeitung</value>
  </data>
  <data name="Non" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="OffreAucune" xml:space="preserve">
    <value>Keine</value>
  </data>
  <data name="OperateurName" xml:space="preserve">
    <value>Anwender</value>
  </data>
  <data name="Oui" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="PaiementMode" xml:space="preserve">
    <value>Zahlungsweise</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Kennwort</value>
  </data>
  <data name="Place" xml:space="preserve">
    <value>Spielort</value>
  </data>
  <data name="Reserve" xml:space="preserve">
    <value>Reserve</value>
  </data>
  <data name="Resume" xml:space="preserve">
    <value>Zusammenfassung</value>
  </data>
  <data name="SessionDateTime" xml:space="preserve">
    <value>Spieltag</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="successDeleteProfilAcheteur" xml:space="preserve">
    <value>Das Käuferprofil wurde erfolgreich gelöscht</value>
  </data>
  <data name="SuccessInsertProfilAcheteur" xml:space="preserve">
    <value>Das Käuferprofil wurde erfolgreich hinzugefügt</value>
  </data>
  <data name="SuccessRuleInsert" xml:space="preserve">
    <value>Die Verkaufsregel wurde erfolgreich diesem Spieltag hinzugefügt</value>
  </data>
  <data name="SuccessRulesInsert" xml:space="preserve">
    <value>Die Verkaufsregeln wurden erfolgreich den ausgewählten Spieltagen hinzugefügt</value>
  </data>
  <data name="SuccessUpdateContrainteForOffer" xml:space="preserve">
    <value>Die Bedingung für dieses Angebot wurde erfolgreich aktualisiert</value>
  </data>
  <data name="TitleDate" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="TitleModeObtention" xml:space="preserve">
    <value>Zustellungsart</value>
  </data>
  <data name="TitleReserves" xml:space="preserve">
    <value>Reserven</value>
  </data>
  <data name="TitleSeances" xml:space="preserve">
    <value>Auswahl Spieltage</value>
  </data>
  <data name="TitleTarifs" xml:space="preserve">
    <value>Auswahl Rabattstufen</value>
  </data>
  <data name="TypeEnvoi" xml:space="preserve">
    <value>Zustellungsart</value>
  </data>
  <data name="TypeTarif" xml:space="preserve">
    <value>Rabattstufe</value>
  </data>
  <data name="NomReserve" xml:space="preserve">
    <value>Name Reserve</value>
  </data>
  <data name="Activer" xml:space="preserve">
    <value>Aktivieren</value>
  </data>
  <data name="Automatique" xml:space="preserve">
    <value>Automatisch</value>
  </data>
  <data name="Desactiver" xml:space="preserve">
    <value>Deaktivieren</value>
  </data>
  <data name="Heures" xml:space="preserve">
    <value>Stunden</value>
  </data>
  <data name="Jours" xml:space="preserve">
    <value>Tage</value>
  </data>
  <data name="LegendEnd" xml:space="preserve">
    <value>Ende</value>
  </data>
  <data name="LegendNbPlace" xml:space="preserve">
    <value>Platzanzahl</value>
  </data>
  <data name="LegendOptions" xml:space="preserve">
    <value>Optionen</value>
  </data>
  <data name="LegendStart" xml:space="preserve">
    <value>Anfang</value>
  </data>
  <data name="MemePlace" xml:space="preserve">
    <value>Derselbe Platz</value>
  </data>
  <data name="ModePrisesPlace" xml:space="preserve">
    <value>Art der Platzauswahl</value>
  </data>
  <data name="PlacementLibre" xml:space="preserve">
    <value>Freie Platzwahl</value>
  </data>
  <data name="Precedent" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="Suivant" xml:space="preserve">
    <value>Weiter</value>
  </data>
  <data name="SurPlan" xml:space="preserve">
    <value>Zum Sitzplan</value>
  </data>
  <data name="TypePlaces" xml:space="preserve">
    <value>Art der Platzauswahl</value>
  </data>
  <data name="VoirPlaces" xml:space="preserve">
    <value>Platzanzeige</value>
  </data>
  <data name="Produit" xml:space="preserve">
    <value>Name Produkt</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Art Lager</value>
  </data>
  <data name="Tester" xml:space="preserve">
    <value>Testen</value>
    <comment>indivUrlmanifs</comment>
  </data>
  <data name="UrlAccessDirect" xml:space="preserve">
    <value>Url für direkten Zugriff</value>
    <comment>indivUrlmanifs</comment>
  </data>
  <data name="ErrorDeleteProduct" xml:space="preserve">
    <value>Während des Löschvorgangs ist ein Fehler aufgetreten</value>
  </data>
  <data name="SuccessDeleteProduct" xml:space="preserve">
    <value>Das Internet Produkt wurde erfolgreich gelöscht</value>
  </data>
  <data name="BddName" xml:space="preserve">
    <value>Name Datenbank</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="ConnexionTypeProd" xml:space="preserve">
    <value>Prod</value>
  </data>
  <data name="ConnexionTypeTest" xml:space="preserve">
    <value>Test</value>
  </data>
  <data name="ErrorAddConnexion" xml:space="preserve">
    <value>Verbindung kann nicht hinzugefügt werden</value>
  </data>
  <data name="ErrorUpdateConnexion" xml:space="preserve">
    <value>Verbindung kann nicht bearbeitet werden</value>
  </data>
  <data name="ErrorUpdateGroupRights" xml:space="preserve">
    <value>Während der Aktualiserung Rechte der Anwendergruppen ist ein Fehler aufgetreten</value>
  </data>
  <data name="IPServeur" xml:space="preserve">
    <value>IPServer</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="SuccessAddConnexion" xml:space="preserve">
    <value>Verbindung erfolgreich hinzugefügt</value>
  </data>
  <data name="SuccessUpdateConnexion" xml:space="preserve">
    <value>Verbindung erfolgreich bearbeitet</value>
  </data>
  <data name="SuccessUpdateGroupRights" xml:space="preserve">
    <value>Die Aktualsierung der Anwendergruppenrechten wurde erfolgreich durchgeführt</value>
  </data>
  <data name="TestConnexion" xml:space="preserve">
    <value>Test Verbiindung</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="AucuneMaquette" xml:space="preserve">
    <value>Kein Modell</value>
  </data>
  <data name="AucunFichierSauvegarder" xml:space="preserve">
    <value>Keine Datei vor kurzem gespeichert</value>
  </data>
  <data name="GetLastFile" xml:space="preserve">
    <value>Wiederherstellen der zuletzt gespeicherten Datei</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>Voranzeige</value>
  </data>
  <data name="SuccessCssInsert" xml:space="preserve">
    <value> CSS Datei erfolgreich gespeichert</value>
  </data>
  <data name="FileNotFound" xml:space="preserve">
    <value>Keine Datei im Standard oder in der Struktur gefunden</value>
  </data>
  <data name="ErrorUpdatePropertiesEvents" xml:space="preserve">
    <value>Bei der Aktualisierung ist ein Fehler aufgetreten</value>
  </data>
  <data name="SuccessMailnsert" xml:space="preserve">
    <value>Mail Text wurde erfolgreich gespeichert</value>
  </data>
  <data name="SuccessUpdatePropertiesEvents" xml:space="preserve">
    <value>Die Aktualisierung wurde erfolgreich durchgeführt</value>
  </data>
  <data name="TabMailConfirmation" xml:space="preserve">
    <value>Bestätigung</value>
  </data>
  <data name="TabMailError" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="TabMailInscription" xml:space="preserve">
    <value>Anmeldung</value>
  </data>
  <data name="TabMailPassword" xml:space="preserve">
    <value>Kennwort</value>
  </data>
  <data name="NotSVGDirectoryFound" xml:space="preserve">
    <value>Kein Ordner SVG gefunden</value>
  </data>
  <data name="SuccessTagslnsert" xml:space="preserve">
    <value>Tags erfolgreich gespeichert</value>
  </data>
  <data name="ErrorDeleteSeanceFormule" xml:space="preserve">
    <value>Während des Löschvorgangs ist ein Fehler aufgetreten</value>
  </data>
  <data name="ErrorDeleteStructureOnUser" xml:space="preserve">
    <value>Während des Löschvorgangs der Anwendereinstellungen ist ein Fehler aufgetreten</value>
  </data>
  <data name="ErrorDeleteUser" xml:space="preserve">
    <value>Während des Löschens des Anwenders ist ein Fehler aufgetreten</value>
  </data>
  <data name="ErrorPasswordUser" xml:space="preserve">
    <value>Die Kennwörter sind nicht identisch</value>
  </data>
  <data name="ErrorInsertUser" xml:space="preserve">
    <value>Während der Anwendererstellung ist ein Fehler aufgetreten</value>
  </data>
  <data name="ErrorUpdateRule" xml:space="preserve">
    <value>Während der Aktualisierung ist ein Fehler aufgetreten</value>
  </data>
  <data name="ErrorUpdateUser" xml:space="preserve">
    <value>Während der Anwenderaktualisierung ist ein Fehler aufgetreten</value>
  </data>
  <data name="SuccessCommentaireInsert" xml:space="preserve">
    <value>Kommentar erfolgreich gespeichert</value>
  </data>
  <data name="SuccessDeleteSeanceFormule" xml:space="preserve">
    <value>Spieltag der Formel erfolgreich gelöscht</value>
  </data>
  <data name="SuccessDeleteUser" xml:space="preserve">
    <value>Anwender erfolgreich gelöscht</value>
  </data>
  <data name="SuccessInsertUser" xml:space="preserve">
    <value>Anwender erfolgreich erstellt</value>
  </data>
  <data name="SuccessUpdateRule" xml:space="preserve">
    <value>Regel erfolgreich aktualisert</value>
  </data>
  <data name="SuccessUpdateUser" xml:space="preserve">
    <value>Anwender erfolgreich aktualisiert</value>
  </data>
  <data name="UtilisateurExiste" xml:space="preserve">
    <value>Anwender existiert bereits</value>
  </data>
  <data name="ErrorDelierConnexion" xml:space="preserve">
    <value>Verbindung kann nicht getrennt werden</value>
  </data>
  <data name="ErrorInsertConnexion" xml:space="preserve">
    <value>Hinzufügen der Verbindung kann nicht erstellt werden</value>
  </data>
  <data name="ErrorInsertDataBase" xml:space="preserve">
    <value>Die Datenbank kann nicht erstellt werden</value>
  </data>
  <data name="ErrorInsertStructure" xml:space="preserve">
    <value>Während der Erfassung einer neuen Struktur ist ein Fehler aufgetreten</value>
  </data>
  <data name="ErrorLierConnexion" xml:space="preserve">
    <value>Verbindung kann nicht getrennt werden</value>
  </data>
  <data name="SuccessDelierConnexion" xml:space="preserve">
    <value>Verbindung erfolgreich getrennt</value>
  </data>
  <data name="SuccessInsertConnexion" xml:space="preserve">
    <value>Hinzufügen der Verbindung erfolgreich erstellt</value>
  </data>
  <data name="SuccessInsertDataBase" xml:space="preserve">
    <value>Datenbank erfolgreich erstellt</value>
  </data>
  <data name="SuccessInsertStructure" xml:space="preserve">
    <value>Neue Struktur erfolgreich gespeichert</value>
  </data>
  <data name="SuccessLierConnexion" xml:space="preserve">
    <value>Verbindung erfolgreich getrennt</value>
  </data>
  <data name="lblBtnShift" xml:space="preserve">
    <value>Mit der SHIFT-Taste können Sie mehrere Spieltage auswählen</value>
  </data>
  <data name="SelectAllManifs" xml:space="preserve">
    <value>Alle Veranstaltungen auswählen</value>
  </data>
  <data name="SelectAllProducts" xml:space="preserve">
    <value>Alle Produkte auswählen</value>
  </data>
  <data name="SelectAllTarifs" xml:space="preserve">
    <value>Alle Tarife auswählen</value>
  </data>
  <data name="TitleMaquettes" xml:space="preserve">
    <value>Modelle</value>
  </data>
  <data name="TitleSelectProducts" xml:space="preserve">
    <value>Ein oder mehrere Produkte auswählen</value>
  </data>
  <data name="lblTitleCreate" xml:space="preserve">
    <value>Verkaufsregel für [manifName] am [sessionDate] </value>
  </data>
  <data name="lblTitleDateChoice" xml:space="preserve">
    <value>Auswahl Spieltag</value>
  </data>
  <data name="lblTitleEdit" xml:space="preserve">
    <value>Bearbeiten  für [manifName]</value>
  </data>
  <data name="TooltipDeleteSeancesAbonnement" xml:space="preserve">
    <value>Löschen der Regeln für Spieltage des Abonnements</value>
  </data>
  <data name="ErrorSelectManifs" xml:space="preserve">
    <value>Wählen Sie mindestens einen Spieltag aus</value>
  </data>
  <data name="ExportExcel" xml:space="preserve">
    <value>Export nach Excel</value>
  </data>
  <data name="ExportPDF" xml:space="preserve">
    <value>PDF Export</value>
  </data>
  <data name="TitleCashStatment" xml:space="preserve">
    <value>Status Kasse</value>
  </data>
  <data name="KeyConfigNotFound" xml:space="preserve">
    <value>Der Schüssel in web.config existiert nicht</value>
  </data>
  <data name="SvgFileNotExist" xml:space="preserve">
    <value>Der Ordner svg existiert nicht</value>
  </data>
  <data name="FileNotFoundSvg" xml:space="preserve">
    <value>Keine Datei im Ordner SVG gefunden</value>
  </data>
  <data name="SeanceDejaSelectionne" xml:space="preserve">
    <value>Spieltag bereits für dieses Produkt</value>
  </data>
  <data name="InsertLogo" xml:space="preserve">
    <value>Logo erfolgreich eingefügt</value>
  </data>
  <data name="PlayJob" xml:space="preserve">
    <value>Veröffentlichung beschleunigen</value>
  </data>
  <data name="AccesSansBillet" xml:space="preserve">
    <value>Zugriff ohne Ticket</value>
  </data>
  <data name="GlobalPanier" xml:space="preserve">
    <value>Gesant Warenkorb</value>
  </data>
  <data name="TypeAcces" xml:space="preserve">
    <value>Art Zugriff</value>
  </data>
  <data name="lblApercu" xml:space="preserve">
    <value>Übersicht</value>
  </data>
  <data name="lblAucunCommentaire" xml:space="preserve">
    <value>Kein Kommentar definiert</value>
  </data>
  <data name="lblConfirmPassword" xml:space="preserve">
    <value>Kennwort bestätigen</value>
  </data>
  <data name="lblEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="lblEventGroups" xml:space="preserve">
    <value>Veranstaltungsgruppen zugelassen</value>
  </data>
  <data name="lblLogin" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="NewUser" xml:space="preserve">
    <value>Neuer Anwender</value>
  </data>
  <data name="lblPassword" xml:space="preserve">
    <value>Kennwort</value>
  </data>
  <data name="lblProfil" xml:space="preserve">
    <value>Profil</value>
  </data>
  <data name="ChoixSeance" xml:space="preserve">
    <value>Auswahl Spieltage und Rabattstufen</value>
  </data>
  <data name="CreationCompte" xml:space="preserve">
    <value>Kontoerstellung</value>
  </data>
  <data name="Identification" xml:space="preserve">
    <value>Anmeldung</value>
  </data>
  <data name="ListeManifs" xml:space="preserve">
    <value>Liste Veranstaltungen</value>
  </data>
  <data name="MonCompte" xml:space="preserve">
    <value>Mein Konto</value>
  </data>
  <data name="Panier" xml:space="preserve">
    <value>Warenkorb</value>
  </data>
  <data name="CldarSH" xml:space="preserve">
    <value>Spieltagauswahl über Kalender</value>
  </data>
  <data name="MaxBlockSz" xml:space="preserve">
    <value>Falls 1, zurück am + große Einheit der kontingentierten Plätze</value>
  </data>
  <data name="ZapperEtag" xml:space="preserve">
    <value>Auswahl Etage verbergen</value>
  </data>
  <data name="ZapperZES" xml:space="preserve">
    <value>falls 1 auf zone etag sektion verzichten</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>mich erinnern</value>
  </data>
  <data name="DeleteRulesSelected" xml:space="preserve">
    <value>Löschen der Regeln für alle ausgewählten Spieltage</value>
  </data>
  <data name="RegleAjouter" xml:space="preserve">
    <value>Die Verkaufsregel ist erfolgreich für den Spieltag hinzugefügt</value>
  </data>
  <data name="TarifAjouter" xml:space="preserve">
    <value>Der Rabatt erfolgreich hinzugefügt</value>
  </data>
  <data name="ActiverResumer" xml:space="preserve">
    <value>Zusammenfassung aktivieren</value>
  </data>
  <data name="AttentePaiement" xml:space="preserve">
    <value>Bezahlung in Wartestellung</value>
  </data>
  <data name="CancelPaiement" xml:space="preserve">
    <value>Abbruch Bezahlung</value>
  </data>
  <data name="ChoixCategories" xml:space="preserve">
    <value>Auswahl Kateg</value>
  </data>
  <data name="ChoixFormule" xml:space="preserve">
    <value>Auswahl Formel</value>
  </data>
  <data name="CommentaireFormule" xml:space="preserve">
    <value>Commentaire par formule</value>
  </data>
  <data name="CommentairePaiement" xml:space="preserve">
    <value>Kommentar Bezahlung</value>
  </data>
  <data name="ConfirmeProfil" xml:space="preserve">
    <value>Confirmation du profil</value>
  </data>
  <data name="CreationAbo" xml:space="preserve">
    <value>Création abo</value>
  </data>
  <data name="CreationProfil" xml:space="preserve">
    <value>Création du profil</value>
  </data>
  <data name="DetailCommande" xml:space="preserve">
    <value>Détail commande</value>
  </data>
  <data name="DetailComplet" xml:space="preserve">
    <value>Vollständige Details</value>
  </data>
  <data name="ReferenceUniqueBillet" xml:space="preserve">
    <value>Eindeutige Ticketreferenz</value>
  </data>
  <data name="DernierEtatPlace" xml:space="preserve">
    <value>Letzter Platzstatus im Zeitraum</value>
  </data>
  <data name="IdFiliereVente" xml:space="preserve">
    <value>Verkaufskanal-ID</value>
  </data>
  <data name="NomFiliereVente" xml:space="preserve">
    <value>Name des Ticket-Verkaufskanals</value>
  </data>
  <data name="IdManifestation" xml:space="preserve">
    <value>Veranstaltungs-ID</value>
  </data>
  <data name="NomManifestation" xml:space="preserve">
    <value>Name der Veranstaltung</value>
  </data>
  <data name="GroupeManifestations" xml:space="preserve">
    <value>Veranstaltungsgruppe</value>
  </data>
  <data name="GenreManifestation" xml:space="preserve">
    <value>Genre der Veranstaltung</value>
  </data>
  <data name="SousGenreManifestation" xml:space="preserve">
    <value>Unter-Genre der Veranstaltung</value>
  </data>
  <data name="CibleManifestation" xml:space="preserve">
    <value>Zielgruppe der Veranstaltung</value>
  </data>
  <data name="IdSeance" xml:space="preserve">
    <value>Sitzungs-ID</value>
  </data>
  <data name="DateDebutSeance" xml:space="preserve">
    <value>Startdatum der Sitzung</value>
  </data>
  <data name="IdCategorie" xml:space="preserve">
    <value>Kategorie-ID</value>
  </data>
  <data name="NomCategorie" xml:space="preserve">
    <value>Name der Kategorie</value>
  </data>
  <data name="IdTarif" xml:space="preserve">
    <value>Tarif-ID</value>
  </data>
  <data name="NomTarif" xml:space="preserve">
    <value>Name des Tarifs</value>
  </data>
  <data name="NumeroCommande" xml:space="preserve">
    <value>Bestellnummer</value>
  </data>
  <data name="ModePaiement" xml:space="preserve">
    <value>Zahlungsart</value>
  </data>
  <data name="DateOperation" xml:space="preserve">
    <value>Operationsdatum</value>
  </data>
  <data name="IdIdentite" xml:space="preserve">
    <value>Identitäts-ID</value>
  </data>
  <data name="NomIdentite" xml:space="preserve">
    <value>Name der Identität</value>
  </data>
  <data name="PrenomIdentite" xml:space="preserve">
    <value>Vorname der Identität</value>
  </data>
  <data name="Civilite" xml:space="preserve">
    <value>Anrede</value>
  </data>
  <data name="CodePostal" xml:space="preserve">
    <value>Postleitzahl</value>
  </data>
  <data name="Ville" xml:space="preserve">
    <value>Stadt</value>
  </data>
  <data name="Pays" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="DateNaissance" xml:space="preserve">
    <value>Geburtsdatum</value>
  </data>
  <data name="FiliereCreationIdentite" xml:space="preserve">
    <value>Identitätserstellungskanal</value>
  </data>
  <data name="TelephoneMobile" xml:space="preserve">
    <value>Mobiltelefon</value>
  </data>
  <data name="AdressePostale" xml:space="preserve">
    <value>Postanschrift</value>
  </data>
  <data name="OptIn" xml:space="preserve">
    <value>OPT-IN</value>
  </data>
  <data name="NumeroBillet" xml:space="preserve">
    <value>Ticketnummer</value>
  </data>
  <data name="ErrorPaiement" xml:space="preserve">
    <value>Fehler Bezahlung</value>
  </data>
  <data name="EventList" xml:space="preserve">
    <value>Liste Veranstaltungen</value>
  </data>
  <data name="ModificationTarif" xml:space="preserve">
    <value>Modification tarif</value>
  </data>
  <data name="Paiement" xml:space="preserve">
    <value>Paiement</value>
  </data>
  <data name="PanierEtIdentification" xml:space="preserve">
    <value>Warenkorb und Anmeldung</value>
  </data>
  <data name="PopIdentification" xml:space="preserve">
    <value>pop Anmeldung</value>
  </data>
  <data name="SessionId" xml:space="preserve">
    <value>Id séance</value>
  </data>
  <data name="SessionsList" xml:space="preserve">
    <value>Liste Spieltage</value>
  </data>
  <data name="Structures" xml:space="preserve">
    <value>Struktur </value>
  </data>
  <data name="TypeTarifId" xml:space="preserve">
    <value>Type tarif Id</value>
  </data>
  <data name="TypeTarifName" xml:space="preserve">
    <value>nom du tarif</value>
  </data>
  <data name="UpdateProfil" xml:space="preserve">
    <value>Mise à jour du profil</value>
  </data>
  <data name="ValidationAbo" xml:space="preserve">
    <value>Validation abonnement</value>
  </data>
</root>