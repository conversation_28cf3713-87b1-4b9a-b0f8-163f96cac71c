﻿/*Declare @pevent_id varchar(max)
set @pevent_id = '31,1,2'*/

declare @isDistancation int

--select Name from dbo.splitstring(@pevent_id,',')

 CREATE TABLE #DistanciationEvent 
        ( 
           is_distanciation  INT, 
           nb_distanciation_horizontal INT, 
           nb_distanciation_vertical INT 
        ) 


select @isDistancation = count(*) from manifestation m	inner join  proprietes_of_manifs ppm on ppm.manifestation_id = m.manifestation_id
inner join proprietes_references_of_manifs ppref on ppref.propriete_ref_id = ppm.propriete_ref_id
where upper(code)='DISTCV' and m.manifestation_id in (
	select Name from dbo.splitstring(@pevent_id,','))
	

	
INSERT INTO #DistanciationEvent	
	--SELECT CASE WHEN @isDistancation = 1 THEN 'true' ELSE 'false' END, 
	SELECT  @isDistancation,
	 ISNULL((select preference_valeur FROM structure_prefs WHERE preference_cle='DISTANCIATION_HORIZ'),0), 
	 ISNULL((select preference_valeur FROM structure_prefs WHERE preference_cle='DISTANCIATION_VERTI'),0)
		
select * from #DistanciationEvent


drop table #DistanciationEvent