﻿/* remonter les produits de type "questionnaire" 
doit être internet =1 , stock ok, date_valid
*/

SELECT DISTINCT
		case when infocomp_id =0 then 'CHXB' 
		else 'RADB' end as typeQuestion,
		0 as groupNum,
             p.produit_id as ProductID
            , produit_nom as ProductName
            , produit_code as ProductCode
            , 1 as NbProduct
            , jauge as Capacity
            , restant as <PERSON><PERSON><PERSON>
            , (ps.montant1 + ps.montant2)*100 as TotalAmount
            ,  ps.montant2*100 as Charge
			,0 as formulaId
			,infocomp_id as groupId
			,produit_descrip as description
			,pref_affichage
				--,ROW_NUMBER ( ) over (PARTITION BY  p.infocomp_id  ORDER BY p.infocomp_id asc) as occurence 
into #temp
from --abonnement_produit ap 
--inner join 
produit  p 
inner join produit_stock ps on ps.produit_id = p.produit_id
inner join produit_internet pin on pin.produit_id = ps.produit_id
where groupe_id = 0 -- definir ici le groupe quand sera fait dans rodrigue
--and p.internet=1
and date_deb_validite<getdate() and date_fin_validite>getdate()

select* from #temp 
where convert(varchar,productid) like '347%' or convert(varchar,productid) like '565%' /* à enlever une fois parametrage rodrigue ok */
order by typeQuestion, groupId, pref_affichage

drop table #temp