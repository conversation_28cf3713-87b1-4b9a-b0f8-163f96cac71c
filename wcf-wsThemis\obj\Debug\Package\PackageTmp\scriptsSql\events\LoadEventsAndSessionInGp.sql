﻿
SELECT  distinct lieu_nom as placeName, lieu.lieu_id as placeId, manifestation_groupe.manif_groupe_nom as eventGroupName ,manifestation_groupe.manif_groupe_id as eventGroupId,
m.manifestation_nom as eventName ,m.manifestation_id as eventId, s.seance_date_deb as sessionStartDate,s.seance_id as sessionId
,'GP' = CASE WHEN gp.manif_id is NULL THEN 'NON' ELSE 'OUI' END,  
'manifsislock' = CASE 
				WHEN gpm.islock is NULL THEN 'false' 
				WHEN gpm.islock = 0 THEN 'false' 
				ELSE 'true' END, 
'seancesislock' = CASE 
				WHEN gps.islock is NULL THEN 'false' 
				WHEN gps.islock =0 THEN 'false' 
				ELSE 'true' END,
	c.categ_nom as categNom, c.categ_id as categId, c.pref_affichage as categPrefAffichage
 FROM manifestation m
 INNER JOIN manifestation_groupe ON m.manifestation_groupe_id = manifestation_groupe.manif_groupe_id 
INNER JOIN seance s on s.manifestation_id = m.manifestation_id
INNER JOIN lieu ON lieu.lieu_id = s.lieu_id
INNER JOIN categorie c on c.lieu_id = lieu.lieu_id
LEFT OUTER JOIN GP_seance gps on  gps.seance_id = s.seance_Id
LEFT OUTER JOIN GP_manifestation gpm on gpm.manifestation_id = m.manifestation_id
INNER JOIN gestion_place_DispoAbosZES gp on gp.manif_id = m.manifestation_id
--INNER JOIN offre_gestion_place ogp on ogp.gestion_place_id = gp.gestion_place_id
WHERE --isvalide=1 AND 
--dispo>0 AND 
seance_date_deb > getdate() AND seance_cloturer<>'O' AND seance_masquer<>'O' AND seance_verrouiller<>'O'  AND OPTIONS <>'O'
AND m.manifestation_id in ([LISTEMANIFSID])
ORDER BY GP desc,placeName,eventGroupName,eventName,sessionStartDate;