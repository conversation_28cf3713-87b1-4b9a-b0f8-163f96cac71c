using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

using System.Xml;
using System.Web.Services;
using System.Collections.Generic;

namespace customerArea
{
  


    public partial class wctrlHistoUser : basewctrl
    {

        public static DataSet GetHistoriqueCommandesInternet(int structureId, int identiteId, int LangId, List<int> commandeIdList, List<int> formuleIdList, List<string> dossierEtatList)
        {
            //DataSet MyResponse = new DataSet();
           // customerArea.WsThemis.WSThemisAbo WsThemis = new customerArea.WsThemis.WSThemisAbo();
            wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
            DataSet dsHistoInDbWebTracing = new DataSet();
            //GetCustomerAllPurchaseHistory

            dsHistoInDbWebTracing = wcfThemis.GetCustomerAllPurchaseHistoryDetails(structureId.ToString(), identiteId, LangId, true);
            return dsHistoInDbWebTracing;
        }

        public static DataSet GetHistoriqueCommandes(int structureId, int identiteId, int LangId, List<int> commandeIdList, List<int> formuleIdList, List<string> dossierEtatList)
        {
            //customerArea.WsThemis.WSThemisAbo WsThemis = new customerArea.WsThemis.WSThemisAbo();

            wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
            DataSet dsHistoInDbClient = new DataSet();
            

           dsHistoInDbClient = wcfThemis.GetCustomerAllPurchaseHistoryDetails(structureId.ToString(), identiteId, LangId, false);

            return dsHistoInDbClient;
        }

        public static DataSet GetDetailCommande(string IdStructureString, int IdentiteId, int OrderId, string typeL, int EventId)
        {
            DataSet MyResponse = new DataSet();
            if (typeL == "DOS")
            {
                //espaceClient.WsThemis.WSThemisAbo WsThemis = new espaceClient.WsThemis.WSThemisAbo();
                DataSet myResponse = new DataSet();
                //myResponse = WsThemis.GetCustomerOrderDetail(IdStructureString, int.Parse(IdentiteId.ToString()), long.Parse(OrderId.ToString()), long.Parse(EventId.ToString()));
                return myResponse;
            }
            else
            {
                // TODO 
                return null;
            }
        }


        protected void Page_Load(object sender, EventArgs e)
        {

            int EventIdEnCours = 0;
            if (Request.QueryString["eventId"] != "" && Request.QueryString["eventId"] != null && int.TryParse(Request.QueryString["eventId"], out EventIdEnCours))
            {
                System.Web.HttpContext.Current.Session["eventId"] = EventIdEnCours;
            }
            else
            {
                if (System.Web.HttpContext.Current.Session["eventId"] != null && int.TryParse(System.Web.HttpContext.Current.Session["eventId"].ToString(), out EventIdEnCours))
                { }
            }
        }

        public DataSet GetDetailCommande(string structureId, int identiteId, int orderId, int eventId, string typeLigne)
        {
            DataSet detailCmd = new DataSet();
            detailCmd = GetDetailCommande(structureId, identiteId, orderId, typeLigne, eventId);

            return detailCmd;
        }
      
    }
}