﻿/*
declare @pIdentiteId int
declare @pNbDaysBefore int
set @pIdentiteId = 161694
set @pNbDaysBefore = 1095

*/


select distinct Date_Creation,  manifestation_nom, m.manifestation_id, s.seance_id , s.seance_date_deb,
Identite_ID, ID, Commentaire, Quantite, OKSepare, OKStrap 
from ListeAttente la 
inner join manifestation m on m.manifestation_id = la.Manifestation_ID 
left outer join seance s on s.seance_id = la.Seance_ID
where la.manifestation_id in (
		select manifestation_id from seance s1 where s1.seance_date_deb > GETDATE() 
		OR  s1.seance_date_deb > DATEADD(DAY, convert(int, -@pNbDaysBefore), GETDATE())
	)
and la.Supprime = 'N' and identite_ID = @pIdentiteId
order by seance_date_deb





/*select manifestation_nom, m.manifestation_id, s.seance_Id, s.seance_date_deb, Identite_ID, ID, Commentaire, Quantite, OKSepare, OKStrap 
from ListeAttente la
inner join manifestation m on la.Manifestation_ID = m.manifestation_id
left outer join seance s on la.Seance_ID = s.seance_Id
where la.Supprime = 'N' and identite_ID = [Identite_ID] */