{"format": 1, "restore": {"D:\\WORK\\VIEUXPROJETS\\ws_BLL\\ws_BLL.csproj": {}}, "projects": {"D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\OpenEntity2010.csproj": {"restore": {"projectUniqueName": "D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\OpenEntity2010.csproj", "projectName": "OpenEntity2010", "projectPath": "D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\OpenEntity2010.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net48": {"projectReferences": {"D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj"}}}}}, "frameworks": {"net48": {}}}, "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj": {"restore": {"projectUniqueName": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj", "projectName": "Themis.Libraries.DTO", "projectPath": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net48": {"projectReferences": {}}}}, "frameworks": {"net48": {}}}, "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj", "projectName": "Themis.Libraries.Utilities", "projectPath": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"BCrypt.Net": {"target": "Package", "version": "[0.1.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}, "log4net": {"target": "Package", "version": "[3.1.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj": {"restore": {"projectUniqueName": "D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj", "projectName": "utilitaires2010", "projectPath": "D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net48": {"projectReferences": {"D:\\WORK\\VIEUXPROJETS\\ws_DTO\\ws_DTO.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\ws_DTO.csproj"}}}}}, "frameworks": {"net48": {}}}, "D:\\WORK\\VIEUXPROJETS\\WebTracing2010\\WebTracing2010.csproj": {"restore": {"projectUniqueName": "D:\\WORK\\VIEUXPROJETS\\WebTracing2010\\WebTracing2010.csproj", "projectName": "WebTracing2010", "projectPath": "D:\\WORK\\VIEUXPROJETS\\WebTracing2010\\WebTracing2010.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net48": {"projectReferences": {"D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\OpenEntity2010.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\OpenEntity2010.csproj"}, "D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj"}}}}}, "frameworks": {"net48": {}}}, "D:\\WORK\\VIEUXPROJETS\\ws_BLL\\ws_BLL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\WORK\\VIEUXPROJETS\\ws_BLL\\ws_BLL.csproj", "projectName": "ws_bll", "projectPath": "D:\\WORK\\VIEUXPROJETS\\ws_BLL\\ws_BLL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\WORK\\VIEUXPROJETS\\ws_BLL\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {"D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj"}, "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj"}, "D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj"}, "D:\\WORK\\VIEUXPROJETS\\WebTracing2010\\WebTracing2010.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\WebTracing2010\\WebTracing2010.csproj"}, "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\ws_DTO.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\ws_DTO.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"EntityFramework": {"target": "Package", "version": "[6.1.3, )"}, "ISO3166": {"target": "Package", "version": "[1.0.4, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[7.0.3, )"}, "RestSharp": {"target": "Package", "version": "[106.15.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}, "log4net": {"target": "Package", "version": "[3.1.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\ws_DTO.csproj": {"restore": {"projectUniqueName": "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\ws_DTO.csproj", "projectName": "ws_DTO", "projectPath": "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\ws_DTO.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net48": {"projectReferences": {"D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj"}}}}}, "frameworks": {"net48": {}}}}}