﻿/* grille de tarif tout simple pour un evenement */

SELECT categ_id as categoryid, type_tarif_id as priceid, seance_id as sessionid,
vts.vts_id as RailingPriceID
,vts.vts_grille1 as AmountExceptTax
,vts.vts_grille2 as Charge 
,Tax= case when modecol4='TAXE' then vts.vts_grille4 else 0 END + case  when modecol5='TAXE' then vts.vts_grille5 else 0 END + case  when modecol6='TAXE' then vts.vts_grille6 else 0 END + case  when modecol7='TAXE' then vts.vts_grille7 else 0 END + case  when modecol8='TAXE' then vts.vts_grille8 else 0 END + case  when modecol9='TAXE' then vts.vts_grille9 else 0 END + case  when modecol10='TAXE' then vts.vts_grille10 else 0 END
,Discount=100*( case when modecol4='REMISE' then vts.vts_grille4 else 0 END + case  when modecol5='REMISE' then vts.vts_grille5 else 0 END + case  when modecol6='REMISE' then vts.vts_grille6 else 0 END + case  when modecol7='REMISE' then vts.vts_grille7 else 0 END + case  when modecol8='REMISE' then vts.vts_grille8 else 0 END + case  when modecol9='REMISE' then vts.vts_grille9 else 0 END + case  when modecol10='REMISE' then vts.vts_grille10 else 0 END)
,Commission= 100*(case when modecol4='COMMISSION' then vts.vts_grille4 else 0 END + case  when modecol5='COMMISSION' then vts.vts_grille5 else 0 END + case  when modecol6='COMMISSION' then vts.vts_grille6 else 0 END + case  when modecol7='COMMISSION' then vts.vts_grille7 else 0 END + case  when modecol8='COMMISSION' then vts.vts_grille8 else 0 END + case  when modecol9='COMMISSION' then vts.vts_grille9 else 0 END + case  when modecol10='COMMISSION' then vts.vts_grille10 else 0 END)
,TotalTax= 100*(case  when modecol4='REMISE'  then - vts.vts_grille4 when modecol4='TAXE' or modecol4='COMMISSION' then  vts.vts_grille4 else 0 END + case  when modecol5='REMISE' then - vts.vts_grille5 when modecol5='TAXE' or modecol5='COMMISSION' then vts.vts_grille5 else 0 END + case  when modecol6='REMISE' then - vts.vts_grille6 when modecol6='TAXE' or modecol6='COMMISSION' then vts.vts_grille6 else 0 END + case  when modecol7='REMISE' then - vts.vts_grille7 when modecol7='TAXE' or modecol7='COMMISSION' then vts.vts_grille7 else 0 END + case  when modecol8='REMISE' then - vts.vts_grille8 when modecol8='TAXE' or modecol8='COMMISSION' then vts.vts_grille8 else 0 END + case  when modecol9='REMISE' then - vts.vts_grille9 when modecol9='TAXE'or modecol9='COMMISSION' then vts.vts_grille9 else 0 END + case  when modecol10='REMISE' then - vts.vts_grille10 when modecol10='TAXE' or modecol10='COMMISSION' then  vts.vts_grille10 else 0 END	)
,TotalAmount= 100*(vts.vts_grille1+vts.vts_grille2+case when modecol4='REMISE' then - vts.vts_grille4  when modecol4='TAXE' or modecol4='COMMISSION' then vts.vts_grille4 else 0 END + case  when modecol5='REMISE' then - vts.vts_grille5 when modecol5='TAXE' or modecol5='COMMISSION' then vts.vts_grille5 else 0 END + case  when modecol6='REMISE' then - vts.vts_grille6 when modecol6='TAXE' or modecol6='COMMISSION' then vts.vts_grille6 else 0 END + case  when modecol7='REMISE' then - vts.vts_grille7 when modecol7='TAXE' or modecol7='COMMISSION' then vts.vts_grille7 else 0 END + case  when modecol8='REMISE' then - vts.vts_grille8 when modecol8='TAXE' or modecol8='COMMISSION' then vts.vts_grille8 else 0 END + case  when modecol9='REMISE' then - vts.vts_grille9 when modecol9='TAXE'or modecol9='COMMISSION' then vts.vts_grille9 else 0 END + case  when modecol10='REMISE' then - vts.vts_grille10 when modecol10='TAXE' or modecol10='COMMISSION' then vts.vts_grille10 else 0 END	 )
,TicketAmount=100 *vts.vts_grille3
FROM valeur_tarif_stock[eventID] vts
INNER JOIN structure st ON st.structure_id >0
WHERE vts_v =(	SELECT MAX(vts_v) FROM valeur_tarif_stock[eventID] vts2
					WHERE vts2.tarif_logique_id=vts.tarif_logique_id
					and vts2.seance_id=vts.seance_id
					and vts2.categ_id= vts.categ_id
					and vts2.type_tarif_id= vts.type_tarif_id
		) and vts.vts_grille1>=0

		and vts.seance_id in ([ListsessionID])

	ORDER BY categ_id, type_tarif_id
