﻿/*
IF (not EXISTS (SELECT * 
                 FROM INFORMATION_SCHEMA.TABLES 
                 WHERE
            TABLE_NAME = 'formule_contrainte'))
BEGIN
    --Do Stuff


CREATE TABLE [dbo].[formule_contrainte](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[formule_id] [int] NULL,
	[contrainte_id] [numeric](18, 0) NULL,
 CONSTRAINT [PK_formule_contrainte] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]


ALTER TABLE [dbo].[formule_contrainte]  WITH CHECK ADD  CONSTRAINT [FK_formule_contrainte_contrainte] FOREIGN KEY([contrainte_id])
REFERENCES [dbo].[contrainte] ([contrainte_id])


ALTER TABLE [dbo].[formule_contrainte] CHECK CONSTRAINT [FK_formule_contrainte_contrainte]


ALTER TABLE [dbo].[formule_contrainte]  WITH CHECK ADD  CONSTRAINT [FK_formule_contrainte_formule_abonnement] FOREIGN KEY([formule_id])
REFERENCES [dbo].[formule_abonnement] ([form_abon_id])


ALTER TABLE [dbo].[formule_contrainte] CHECK CONSTRAINT [FK_formule_contrainte_formule_abonnement]


END
*/
declare @identite_id int

set @identite_id = [identiteID]
		  
IF @identite_id!=0 
BEGIN
		/* identite != 0, on cherche les formules verifiant les contraintes (+ celles sans contraintes) */
		SELECT distinct(formule_id),checked,ok 
		INTO #tmp_offers
		FROM (
	 		SELECT distinct(formule_id) as formule_id,0 as checked,0 as ok 
			FROM formule_contrainte 
			UNION
			SELECT distinct(form_abon_id) as formule_id,1 as checked,1 as ok 

 			FROM formule_abonnement WHERE form_abon_id not in (select formule_id from formule_contrainte) and  masquer='N'
			) s
		ORDER BY formule_id


		--select * from #tmp_offers where checked=0

		--select *from #tmp_offers

 		DECLARE @formule_id int 
 		WHILE exists(SELECT * from #tmp_offers where checked=0) -- parcourt des offres disponibles 
 		BEGIN
			declare @thisFormuleid int
			DECLARE formule_cursor CURSOR SCROLL FOR 
				SELECT distinct formule_id FROM #tmp_offers where checked=0 

					

			OPEN formule_cursor 
			FETCH NEXT FROM formule_cursor INTO @formule_id

			WHILE @@FETCH_STATUS=0
 			BEGIN 

				--select @formule_id
 		   --set @formule_id =(SELECT top 1 formule_id FROM #tmp_offers where checked=0 order by 1)

		  -- /*
 				   DECLARE contrainte_cursor CURSOR SCROLL FOR 
 						SELECT sql_where FROM contrainte c, formule_contrainte fc
 						WHERE fc.contrainte_id =c.contrainte_id and formule_id=@formule_id

 				   declare @sql_where varchar(1000) 

 				   OPEN contrainte_cursor 
 		   
				   declare @AllContraintesOk bit 
 				   set @AllContraintesOk=1;

				   --IF (@@ROWCOUNT=0)
				   --print 'pas de contrainte sur cette form ' + @formule_id


 					   FETCH NEXT FROM contrainte_cursor INTO @sql_where --parcourt des contraintes de l'offre
 					   WHILE @@FETCH_STATUS=0
 					   BEGIN 
 						   DECLARE @sql_to_exec varchar(1000) 
 						   SET @sql_to_exec=REPLACE(@sql_where,'@identite_id',@identite_id) 
						   print @sql_to_exec
 						   CREATE TABLE #results (nrows int) 
 	
 							   INSERT #results exec (@sql_to_exec) -- execution de la requete dans la colonne sql_where de la table contrainte (insert dans tbl tempo ne sert qu'à ne pas afficher le resultat) 
 							   IF (@@ROWCOUNT=0) BEGIN  SET @AllContraintesOk=0 --contrainte pas remplie, on sort 
 								   DROP TABLE #results BREAK; 
 							   END
 
 							DROP TABLE #results
 							FETCH NEXT FROM contrainte_cursor INTO @sql_where 
 						END
				CLOSE contrainte_cursor
 				DEALLOCATE contrainte_cursor

				
				--END


 		
			--IF (@AllContraintesOk=1) 
			--BEGIN
			--	-- on sort à la 1ere contrainte remplie :
			--	--UPDATE #tmp_offers SET checked=1 

				--	*/
					UPDATE #tmp_offers SET checked=1,ok=@AllContraintesOk WHERE formule_id=@formule_id
			FETCH NEXT FROM formule_cursor INTO @formule_id
			END
	

					CLOSE formule_cursor
 		
		DEALLOCATE formule_cursor
	END

	--select *from #tmp_offers

 		--insert #myformules (formule_id)
 		--SELECT formule_id FROM #tmp_offers tmpo WHERE ok=1

	SELECT* from formule_abonnement fa
		WHERE fa.form_abon_id in (
			SELECT gp.formule_id from gestion_place  gp 
			INNER JOIN #tmp_offers tmp on tmp.formule_id=gp.formule_id 
			INNER JOIN formule_abonnement fa on fa.form_abon_id = gp.formule_id
			INNER JOIN abonnement_manifestation am on am.formule_id = gp.formule_id and am.manif_id = gp.manif_id and am.seance_id=gp.seance_id
			WHERE isvalide=1 and checked=1 and ok=1 and fa.masquer='N' 
		)
	order by  form_abon_code asc


	DROP TABLE #tmp_offers
	END

ELSE
BEGIN /* identite = 0 -> formules sans contraintes */

		SELECT * FROM formule_abonnement fa
		WHERE fa.form_abon_id in (
				SELECT gp.formule_id FROM gestion_place  gp
					INNER JOIN formule_abonnement fa on fa.form_abon_id = gp.formule_id
					INNER JOIN abonnement_manifestation am on am.formule_id = gp.formule_id and am.manif_id = gp.manif_id and am.seance_id=gp.seance_id
					
					WHERE isvalide=1 and fa.masquer='N'  )
		AND  form_abon_id NOT IN (SELECT formule_id FROM formule_contrainte) 
		order by  form_abon_code asc


END

