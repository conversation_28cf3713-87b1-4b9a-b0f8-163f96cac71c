{"version": 3, "targets": {".NETFramework,Version=v4.8": {"BCrypt.Net/0.1.0": {"type": "package", "compile": {"lib/net35/BCrypt.Net.dll": {"related": ".XML"}}, "runtime": {"lib/net35/BCrypt.Net.dll": {"related": ".XML"}}}, "EntityFramework/6.1.3": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}}, "ISO3166/1.0.4": {"type": "package", "compile": {"lib/net20/ISO3166.dll": {}}, "runtime": {"lib/net20/ISO3166.dll": {}}}, "log4net/3.1.0": {"type": "package", "frameworkAssemblies": ["System.Configuration", "System.Web"], "compile": {"lib/net462/log4net.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/log4net.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"type": "package", "compile": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.0.3", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3", "System.Memory": "4.5.5", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "RestSharp/106.15.0": {"type": "package", "frameworkAssemblies": ["System.Web"], "compile": {"lib/net452/RestSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net452/RestSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "compile": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.7.2": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.1", "System.Text.Encodings.Web": "4.7.1", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "OpenEntity2010/1.0.0": {"type": "project", "dependencies": {"utilitaires2010": "1.0.0"}, "compile": {"bin/placeholder/OpenEntity2010.dll": {}}, "runtime": {"bin/placeholder/OpenEntity2010.dll": {}}}, "Themis.Libraries.DTO/1.0.0": {"type": "project", "compile": {"bin/placeholder/Themis.Libraries.DTO.dll": {}}, "runtime": {"bin/placeholder/Themis.Libraries.DTO.dll": {}}}, "Themis.Libraries.Utilities/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"BCrypt.Net": "0.1.0", "Newtonsoft.Json": "13.0.3", "RestSharp": "112.1.0", "log4net": "3.1.0"}, "compile": {"bin/placeholder/Themis.Libraries.Utilities.dll": {}}, "runtime": {"bin/placeholder/Themis.Libraries.Utilities.dll": {}}}, "utilitaires2010/1.0.0": {"type": "project", "dependencies": {"ws_DTO": "1.0.0"}, "compile": {"bin/placeholder/utilitaires2010.dll": {}}, "runtime": {"bin/placeholder/utilitaires2010.dll": {}}}, "WebTracing2010/1.0.0": {"type": "project", "dependencies": {"OpenEntity2010": "1.0.0", "utilitaires2010": "1.0.0"}, "compile": {"bin/placeholder/WebTracing2010.dll": {}}, "runtime": {"bin/placeholder/WebTracing2010.dll": {}}}, "ws_DTO/1.0.0": {"type": "project", "dependencies": {"Themis.Libraries.DTO": "1.0.0"}, "compile": {"bin/placeholder/ws_DTO.dll": {}}, "runtime": {"bin/placeholder/ws_DTO.dll": {}}}}, ".NETFramework,Version=v4.8/win": {"BCrypt.Net/0.1.0": {"type": "package", "compile": {"lib/net35/BCrypt.Net.dll": {"related": ".XML"}}, "runtime": {"lib/net35/BCrypt.Net.dll": {"related": ".XML"}}}, "EntityFramework/6.1.3": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}}, "ISO3166/1.0.4": {"type": "package", "compile": {"lib/net20/ISO3166.dll": {}}, "runtime": {"lib/net20/ISO3166.dll": {}}}, "log4net/3.1.0": {"type": "package", "frameworkAssemblies": ["System.Configuration", "System.Web"], "compile": {"lib/net462/log4net.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/log4net.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"type": "package", "compile": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.0.3", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3", "System.Memory": "4.5.5", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "RestSharp/106.15.0": {"type": "package", "frameworkAssemblies": ["System.Web"], "compile": {"lib/net452/RestSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net452/RestSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "compile": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.7.2": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.1", "System.Text.Encodings.Web": "4.7.1", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "OpenEntity2010/1.0.0": {"type": "project", "dependencies": {"utilitaires2010": "1.0.0"}, "compile": {"bin/placeholder/OpenEntity2010.dll": {}}, "runtime": {"bin/placeholder/OpenEntity2010.dll": {}}}, "Themis.Libraries.DTO/1.0.0": {"type": "project", "compile": {"bin/placeholder/Themis.Libraries.DTO.dll": {}}, "runtime": {"bin/placeholder/Themis.Libraries.DTO.dll": {}}}, "Themis.Libraries.Utilities/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"BCrypt.Net": "0.1.0", "Newtonsoft.Json": "13.0.3", "RestSharp": "112.1.0", "log4net": "3.1.0"}, "compile": {"bin/placeholder/Themis.Libraries.Utilities.dll": {}}, "runtime": {"bin/placeholder/Themis.Libraries.Utilities.dll": {}}}, "utilitaires2010/1.0.0": {"type": "project", "dependencies": {"ws_DTO": "1.0.0"}, "compile": {"bin/placeholder/utilitaires2010.dll": {}}, "runtime": {"bin/placeholder/utilitaires2010.dll": {}}}, "WebTracing2010/1.0.0": {"type": "project", "dependencies": {"OpenEntity2010": "1.0.0", "utilitaires2010": "1.0.0"}, "compile": {"bin/placeholder/WebTracing2010.dll": {}}, "runtime": {"bin/placeholder/WebTracing2010.dll": {}}}, "ws_DTO/1.0.0": {"type": "project", "dependencies": {"Themis.Libraries.DTO": "1.0.0"}, "compile": {"bin/placeholder/ws_DTO.dll": {}}, "runtime": {"bin/placeholder/ws_DTO.dll": {}}}}, ".NETFramework,Version=v4.8/win-arm64": {"BCrypt.Net/0.1.0": {"type": "package", "compile": {"lib/net35/BCrypt.Net.dll": {"related": ".XML"}}, "runtime": {"lib/net35/BCrypt.Net.dll": {"related": ".XML"}}}, "EntityFramework/6.1.3": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}}, "ISO3166/1.0.4": {"type": "package", "compile": {"lib/net20/ISO3166.dll": {}}, "runtime": {"lib/net20/ISO3166.dll": {}}}, "log4net/3.1.0": {"type": "package", "frameworkAssemblies": ["System.Configuration", "System.Web"], "compile": {"lib/net462/log4net.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/log4net.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"type": "package", "compile": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.0.3", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3", "System.Memory": "4.5.5", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "RestSharp/106.15.0": {"type": "package", "frameworkAssemblies": ["System.Web"], "compile": {"lib/net452/RestSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net452/RestSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "compile": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.7.2": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.1", "System.Text.Encodings.Web": "4.7.1", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "OpenEntity2010/1.0.0": {"type": "project", "dependencies": {"utilitaires2010": "1.0.0"}, "compile": {"bin/placeholder/OpenEntity2010.dll": {}}, "runtime": {"bin/placeholder/OpenEntity2010.dll": {}}}, "Themis.Libraries.DTO/1.0.0": {"type": "project", "compile": {"bin/placeholder/Themis.Libraries.DTO.dll": {}}, "runtime": {"bin/placeholder/Themis.Libraries.DTO.dll": {}}}, "Themis.Libraries.Utilities/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"BCrypt.Net": "0.1.0", "Newtonsoft.Json": "13.0.3", "RestSharp": "112.1.0", "log4net": "3.1.0"}, "compile": {"bin/placeholder/Themis.Libraries.Utilities.dll": {}}, "runtime": {"bin/placeholder/Themis.Libraries.Utilities.dll": {}}}, "utilitaires2010/1.0.0": {"type": "project", "dependencies": {"ws_DTO": "1.0.0"}, "compile": {"bin/placeholder/utilitaires2010.dll": {}}, "runtime": {"bin/placeholder/utilitaires2010.dll": {}}}, "WebTracing2010/1.0.0": {"type": "project", "dependencies": {"OpenEntity2010": "1.0.0", "utilitaires2010": "1.0.0"}, "compile": {"bin/placeholder/WebTracing2010.dll": {}}, "runtime": {"bin/placeholder/WebTracing2010.dll": {}}}, "ws_DTO/1.0.0": {"type": "project", "dependencies": {"Themis.Libraries.DTO": "1.0.0"}, "compile": {"bin/placeholder/ws_DTO.dll": {}}, "runtime": {"bin/placeholder/ws_DTO.dll": {}}}}, ".NETFramework,Version=v4.8/win-x64": {"BCrypt.Net/0.1.0": {"type": "package", "compile": {"lib/net35/BCrypt.Net.dll": {"related": ".XML"}}, "runtime": {"lib/net35/BCrypt.Net.dll": {"related": ".XML"}}}, "EntityFramework/6.1.3": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}}, "ISO3166/1.0.4": {"type": "package", "compile": {"lib/net20/ISO3166.dll": {}}, "runtime": {"lib/net20/ISO3166.dll": {}}}, "log4net/3.1.0": {"type": "package", "frameworkAssemblies": ["System.Configuration", "System.Web"], "compile": {"lib/net462/log4net.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/log4net.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"type": "package", "compile": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.0.3", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3", "System.Memory": "4.5.5", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "RestSharp/106.15.0": {"type": "package", "frameworkAssemblies": ["System.Web"], "compile": {"lib/net452/RestSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net452/RestSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "compile": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.7.2": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.1", "System.Text.Encodings.Web": "4.7.1", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "OpenEntity2010/1.0.0": {"type": "project", "dependencies": {"utilitaires2010": "1.0.0"}, "compile": {"bin/placeholder/OpenEntity2010.dll": {}}, "runtime": {"bin/placeholder/OpenEntity2010.dll": {}}}, "Themis.Libraries.DTO/1.0.0": {"type": "project", "compile": {"bin/placeholder/Themis.Libraries.DTO.dll": {}}, "runtime": {"bin/placeholder/Themis.Libraries.DTO.dll": {}}}, "Themis.Libraries.Utilities/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"BCrypt.Net": "0.1.0", "Newtonsoft.Json": "13.0.3", "RestSharp": "112.1.0", "log4net": "3.1.0"}, "compile": {"bin/placeholder/Themis.Libraries.Utilities.dll": {}}, "runtime": {"bin/placeholder/Themis.Libraries.Utilities.dll": {}}}, "utilitaires2010/1.0.0": {"type": "project", "dependencies": {"ws_DTO": "1.0.0"}, "compile": {"bin/placeholder/utilitaires2010.dll": {}}, "runtime": {"bin/placeholder/utilitaires2010.dll": {}}}, "WebTracing2010/1.0.0": {"type": "project", "dependencies": {"OpenEntity2010": "1.0.0", "utilitaires2010": "1.0.0"}, "compile": {"bin/placeholder/WebTracing2010.dll": {}}, "runtime": {"bin/placeholder/WebTracing2010.dll": {}}}, "ws_DTO/1.0.0": {"type": "project", "dependencies": {"Themis.Libraries.DTO": "1.0.0"}, "compile": {"bin/placeholder/ws_DTO.dll": {}}, "runtime": {"bin/placeholder/ws_DTO.dll": {}}}}, ".NETFramework,Version=v4.8/win-x86": {"BCrypt.Net/0.1.0": {"type": "package", "compile": {"lib/net35/BCrypt.Net.dll": {"related": ".XML"}}, "runtime": {"lib/net35/BCrypt.Net.dll": {"related": ".XML"}}}, "EntityFramework/6.1.3": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}}, "ISO3166/1.0.4": {"type": "package", "compile": {"lib/net20/ISO3166.dll": {}}, "runtime": {"lib/net20/ISO3166.dll": {}}}, "log4net/3.1.0": {"type": "package", "frameworkAssemblies": ["System.Configuration", "System.Web"], "compile": {"lib/net462/log4net.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/log4net.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"type": "package", "compile": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.0.3", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3", "System.Memory": "4.5.5", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "RestSharp/106.15.0": {"type": "package", "frameworkAssemblies": ["System.Web"], "compile": {"lib/net452/RestSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net452/RestSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "compile": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.7.2": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.1", "System.Text.Encodings.Web": "4.7.1", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "OpenEntity2010/1.0.0": {"type": "project", "dependencies": {"utilitaires2010": "1.0.0"}, "compile": {"bin/placeholder/OpenEntity2010.dll": {}}, "runtime": {"bin/placeholder/OpenEntity2010.dll": {}}}, "Themis.Libraries.DTO/1.0.0": {"type": "project", "compile": {"bin/placeholder/Themis.Libraries.DTO.dll": {}}, "runtime": {"bin/placeholder/Themis.Libraries.DTO.dll": {}}}, "Themis.Libraries.Utilities/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"BCrypt.Net": "0.1.0", "Newtonsoft.Json": "13.0.3", "RestSharp": "112.1.0", "log4net": "3.1.0"}, "compile": {"bin/placeholder/Themis.Libraries.Utilities.dll": {}}, "runtime": {"bin/placeholder/Themis.Libraries.Utilities.dll": {}}}, "utilitaires2010/1.0.0": {"type": "project", "dependencies": {"ws_DTO": "1.0.0"}, "compile": {"bin/placeholder/utilitaires2010.dll": {}}, "runtime": {"bin/placeholder/utilitaires2010.dll": {}}}, "WebTracing2010/1.0.0": {"type": "project", "dependencies": {"OpenEntity2010": "1.0.0", "utilitaires2010": "1.0.0"}, "compile": {"bin/placeholder/WebTracing2010.dll": {}}, "runtime": {"bin/placeholder/WebTracing2010.dll": {}}}, "ws_DTO/1.0.0": {"type": "project", "dependencies": {"Themis.Libraries.DTO": "1.0.0"}, "compile": {"bin/placeholder/ws_DTO.dll": {}}, "runtime": {"bin/placeholder/ws_DTO.dll": {}}}}}, "libraries": {"BCrypt.Net/0.1.0": {"sha512": "sST2w361Dxt9GGMfpOTiK50wXGV64Ybb1hiX3xjEWnhVYZNF43NuySGwADJa7X1R+bA53NsFR+tuDxcYiJeIOA==", "type": "package", "path": "bcrypt.net/0.1.0", "files": [".nupkg.metadata", ".signature.p7s", "bcrypt.net.0.1.0.nupkg.sha512", "bcrypt.net.nuspec", "lib/net35/BCrypt.Net.XML", "lib/net35/BCrypt.Net.dll"]}, "EntityFramework/6.1.3": {"sha512": "2xzFHAF3VVdmTR3//MygtQpwfbDjeaq+nQwCcokLzen7MKHOojPaxNPQPa4diHY1QtcR7zlHAJHNRqPB7W2Syw==", "type": "package", "path": "entityframework/6.1.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "content/App.config.transform", "content/Web.config.transform", "entityframework.6.1.3.nupkg.sha512", "entityframework.nuspec", "lib/net40/EntityFramework.SqlServer.dll", "lib/net40/EntityFramework.SqlServer.xml", "lib/net40/EntityFramework.dll", "lib/net40/EntityFramework.xml", "lib/net45/EntityFramework.SqlServer.dll", "lib/net45/EntityFramework.SqlServer.xml", "lib/net45/EntityFramework.dll", "lib/net45/EntityFramework.xml", "tools/EntityFramework.PowerShell.Utility.dll", "tools/EntityFramework.PowerShell.dll", "tools/EntityFramework.psd1", "tools/EntityFramework.psm1", "tools/about_EntityFramework.help.txt", "tools/init.ps1", "tools/install.ps1", "tools/migrate.exe"]}, "ISO3166/1.0.4": {"sha512": "ApT7VLnfxfS0fnayegZS1QzjALTnuHTwjaN1fqsiMBEC3D+0qiwnNHi+oaJ5pS2/C9D06KV1bc+CF6t7Iqmu3g==", "type": "package", "path": "iso3166/1.0.4", "files": [".nupkg.metadata", ".signature.p7s", "iso3166.1.0.4.nupkg.sha512", "iso3166.nuspec", "lib/net20/ISO3166.dll", "lib/netstandard1.0/ISO3166.dll", "lib/netstandard2.0/ISO3166.dll"]}, "log4net/3.1.0": {"sha512": "GT7ZYyNcBNbMENUSSQsH8HkjrELe55UOwOkxMvUBfoSyq/K/c1SPi5aXCNHFqpyeCPlrq8nVx40z9pBVwnqkmA==", "type": "package", "path": "log4net/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/log4net.dll", "lib/net462/log4net.pdb", "lib/net462/log4net.xml", "lib/netstandard2.0/log4net.dll", "lib/netstandard2.0/log4net.pdb", "lib/netstandard2.0/log4net.xml", "log4net.3.1.0.nupkg.sha512", "log4net.nuspec", "package-icon.png"]}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"sha512": "1Am6l4Vpn3/K32daEqZI+FFr96OlZkgwK2LcT3pZ2zWubR5zTPW3/FkO1Rat9kb7oQOa4rxgl9LJHc5tspCWfg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.1.1.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "ref/net461/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"sha512": "cfPUWdjigLIRIJSKz3uaZxShgf86RVDXHC1VEEchj1gnY25akwPYpbrfSoIGDCqA9UmOMdlctq411+2pAViFow==", "type": "package", "path": "microsoft.identitymodel.abstractions/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.7.0.3.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"sha512": "vxjHVZbMKD3rVdbvKhzAW+7UiFrYToUVm3AGmYfKSOAwyhdLl/ELX1KZr+FaLyyS5VReIzWRWJfbOuHM9i6ywg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.7.0.3.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/7.0.3": {"sha512": "b6GbGO+2LOTBEccHhqoJsOsmemG4A/MY+8H0wK/ewRhiG+DCYwEnucog1cSArPIY55zcn+XdZl0YEiUHkpDISQ==", "type": "package", "path": "microsoft.identitymodel.logging/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.7.0.3.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Tokens/7.0.3": {"sha512": "wB+LlbDjhnJ98DULjmFepqf9eEMh/sDs6S6hFh68iNRHmwollwhxk+nbSSfpA5+j+FbRyNskoaY4JsY1iCOKCg==", "type": "package", "path": "microsoft.identitymodel.tokens/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.7.0.3.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "RestSharp/106.15.0": {"sha512": "HM7ohR6AscxedNenrrl93DSZG6hvdnA7O+NHyCl/6ZKTfn8psQ9rRmUpcb9eWr+140vmfxoVemql0bUoeGEW3A==", "type": "package", "path": "restsharp/106.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/RestSharp.dll", "lib/net452/RestSharp.xml", "lib/netstandard2.0/RestSharp.dll", "lib/netstandard2.0/RestSharp.xml", "restsharp.106.15.0.nupkg.sha512", "restsharp.nuspec", "restsharp.png"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"sha512": "caEe+OpQNYNiyZb+DJpUVROXoVySWBahko2ooNfUcllxa9ZQUM8CgM/mDjP6AoFn6cQU9xMmG+jivXWub8cbGg==", "type": "package", "path": "system.identitymodel.tokens.jwt/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.7.0.3.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {"sha512": "zOHkQmzPCn5zm/BH+cxC1XbUS3P4Yoi3xzW7eRgVpDR2tPGSzyMZ17Ig1iRkfJuY0nhxkQQde8pgePNiA7z7TQ==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.7.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.7.1.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/4.7.2": {"sha512": "iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "type": "package", "path": "system.text.encodings.web/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "lib/netstandard2.1/System.Text.Encodings.Web.dll", "lib/netstandard2.1/System.Text.Encodings.Web.xml", "system.text.encodings.web.4.7.2.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/4.7.2": {"sha512": "TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "type": "package", "path": "system.text.json/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/netcoreapp3.0/System.Text.Json.dll", "lib/netcoreapp3.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.4.7.2.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "OpenEntity2010/1.0.0": {"type": "project", "path": "../OpenEntity2010/OpenEntity2010.csproj", "msbuildProject": "../OpenEntity2010/OpenEntity2010.csproj"}, "Themis.Libraries.DTO/1.0.0": {"type": "project", "path": "../Themis.Libraries.DTO/Themis.Libraries.DTO.csproj", "msbuildProject": "../Themis.Libraries.DTO/Themis.Libraries.DTO.csproj"}, "Themis.Libraries.Utilities/1.0.0": {"type": "project", "path": "../Themis.Libraries.Utilities/Themis.Libraries.Utilities.csproj", "msbuildProject": "../Themis.Libraries.Utilities/Themis.Libraries.Utilities.csproj"}, "utilitaires2010/1.0.0": {"type": "project", "path": "../utilitaires/utilitaires2010.csproj", "msbuildProject": "../utilitaires/utilitaires2010.csproj"}, "WebTracing2010/1.0.0": {"type": "project", "path": "../WebTracing2010/WebTracing2010.csproj", "msbuildProject": "../WebTracing2010/WebTracing2010.csproj"}, "ws_DTO/1.0.0": {"type": "project", "path": "../ws_DTO/ws_DTO.csproj", "msbuildProject": "../ws_DTO/ws_DTO.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["EntityFramework >= 6.1.3", "ISO3166 >= 1.0.4", "Microsoft.IdentityModel.Tokens >= 7.0.3", "RestSharp >= 106.15.0", "System.IdentityModel.Tokens.Jwt >= 7.0.3", "Themis.Libraries.DTO >= 1.0.0", "Themis.Libraries.Utilities >= 1.0.0", "WebTracing2010 >= 1.0.0", "log4net >= 3.1.0", "utilitaires2010 >= 1.0.0", "ws_DTO >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\WORK\\VIEUXPROJETS\\ws_BLL\\ws_BLL.csproj", "projectName": "ws_bll", "projectPath": "D:\\WORK\\VIEUXPROJETS\\ws_BLL\\ws_BLL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\WORK\\VIEUXPROJETS\\ws_BLL\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {"D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj"}, "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj"}, "D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\utilitaires\\utilitaires2010.csproj"}, "D:\\WORK\\VIEUXPROJETS\\WebTracing2010\\WebTracing2010.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\WebTracing2010\\WebTracing2010.csproj"}, "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\ws_DTO.csproj": {"projectPath": "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\ws_DTO.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"EntityFramework": {"target": "Package", "version": "[6.1.3, )"}, "ISO3166": {"target": "Package", "version": "[1.0.4, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[7.0.3, )"}, "RestSharp": {"target": "Package", "version": "[106.15.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}, "log4net": {"target": "Package", "version": "[3.1.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "logs": [{"code": "NU1902", "level": "Warning", "warningLevel": 1, "message": "Le package 'System.IdentityModel.Tokens.Jwt' 7.0.3 présente une vulnérabilité de gravité moyenne connue, https://github.com/advisories/GHSA-59j7-ghrg-fj52.", "libraryId": "System.IdentityModel.Tokens.Jwt", "targetGraphs": [".NETFramework,Version=v4.8", ".NETFramework,Version=v4.8/win", ".NETFramework,Version=v4.8/win-arm64", ".NETFramework,Version=v4.8/win-x64", ".NETFramework,Version=v4.8/win-x86"]}, {"code": "NU1605", "level": "Warning", "warningLevel": 1, "message": "Passage à une version antérieure du package détecté : RestSharp de 112.1.0 à 106.15.0. Référencez le package directement à partir du projet pour sélectionner une version différente. \r\n ws_bll -> Themis.Libraries.Utilities -> RestSharp (>= 112.1.0) \r\n ws_bll -> RestSharp (>= 106.15.0)", "libraryId": "RestSharp", "targetGraphs": [".NETFramework,Version=v4.8", ".NETFramework,Version=v4.8/win", ".NETFramework,Version=v4.8/win-arm64", ".NETFramework,Version=v4.8/win-x64", ".NETFramework,Version=v4.8/win-x86"]}]}