﻿
------------------------------------------------------------------

CREATE TABLE [dbo].[formule_contrainte](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[formule_id] [int] NULL,
	[contrainte_id] [numeric](18, 0) NULL,
 CONSTRAINT [PK_formule_contrainte] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE [dbo].[formule_contrainte]  WITH CHECK ADD  CONSTRAINT [FK_formule_contrainte_contrainte] FOREIGN KEY([contrainte_id])
REFERENCES [dbo].[contrainte] ([contrainte_id])
GO

ALTER TABLE [dbo].[formule_contrainte] CHECK CONSTRAINT [FK_formule_contrainte_contrainte]
GO

ALTER TABLE [dbo].[formule_contrainte]  WITH CHECK ADD  CONSTRAINT [FK_formule_contrainte_formule_abonnement] FOREIGN KEY([formule_id])
REFERENCES [dbo].[formule_abonnement] ([form_abon_id])
GO

ALTER TABLE [dbo].[formule_contrainte] CHECK CONSTRAINT [FK_formule_contrainte_formule_abonnement]
GO
