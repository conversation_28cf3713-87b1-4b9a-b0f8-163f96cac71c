﻿DECLARE @NewTarifComplementId int 
DECLARE @NbResult int

	SELECT @NbResult = count(*) FROM Entree_Complement 
		WHERE entree_id = @pentree_id and Identite_id = @pidentite_id and seance_id = @pseance_id and dossier_id = @pdossier_id

IF @NbResult = 0
BEGIN
	INSERT INTO Tarif_Commentaire values (@pname_surname, '', 0,1,0)

	SELECT @NewTarifComplementId = SCOPE_IDENTITY()

	INSERT INTO Entree_Complement (entree_id, seance_id, consommateur_ID, Tarif_Commentaire_ID, Mode_Obtention_ID, Dossier_id, identite_id)
	VALUES(@pentree_id,@pseance_id,@pconsumer_id, @NewTarifComplementId,0,@pdossier_id,@pidentite_id)
END