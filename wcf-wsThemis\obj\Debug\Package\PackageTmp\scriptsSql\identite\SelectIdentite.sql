﻿SELECT DISTINCT i.identite_id as <PERSON><PERSON>,identite_nom as <PERSON><PERSON><PERSON>,identite_prenom as <PERSON>Name, i.identite_complement as IdentiteComplement,
  CONVERT(VARCHAR, identite_date_naissance, 103) as DOB,postal_rue1 as Address1,postal_rue2 as Address2,
  postal_rue3 as Address3,postal_rue4 as address4,
  postal_cp as PostalCode,
  postal_ville as City, postal_pays as Country,
  a.appellation_id as CivilityNamingID, a.appellation_code as CivilityNamingCode, 
  a.appellation_nom as CivilityNamingName, identite_password as Pwd,icw.password as pwdcrypt,
  ic.commentaire as Comment,ic.civilite as Sex,
  lib1.libelle_tel_nom as PhoneType1,i.postal_tel1 as PhoneNumber1,
  lib2.libelle_tel_nom as PhoneType2,i.postal_tel2 as PhoneNumber2,
  lib3.libelle_tel_nom as PhoneType3,i.postal_tel3 as <PERSON><PERSON><PERSON>ber3,
  lib4.libelle_tel_nom as PhoneType4,i.postal_tel4 as <PERSON><PERSON>umber4,
  lib5.libelle_tel_nom as PhoneType5,i.postal_tel5 as PhoneNumber5,
  lib6.libelle_tel_nom as PhoneType6,i.postal_tel6 as PhoneNumber6,
  lib7.libelle_tel_nom as PhoneType7,i.postal_tel7 as PhoneNumber7, 
  FicheSupprimer,  l.langue_id, code,
  LTRIM(RTRIM(i.ref_perso)) as ExternRef,
  i.identite_date_modification as dateUpdated,
  i.identite_date_creation as dateCreated,
  convert(int,isnull((	SELECT sum(  cc_solde*100) as cc_solde
            FROM compte_client cc
            WHERE cc_intitule_operation in ('MACPTE','MACPTANNU') and
            identite_id=i.identite_id
             --and commande_id=0 and manif_id=0 
            and cc_credit>isnull(  
                ( 
                  SELECT SUM(cc_debit) FROM compte_client  
                  WHERE identite_id=i.identite_id AND cc_intitule_operation ='RACPTE'   
                  and num_acompte= cc.cc_numpaiement 
                  ),0) and cc_solde>0
		),0)) as sumAcomptes,

		(SELECT count(*) from identite_infos_comp iic
			inner join info_comp ic on ic.info_comp_id =iic.info_comp_id
			inner join Info_comp_Grp gr on gr.groupe_id = ic.groupe_id
			WHERE iic.identite_id = i.identite_id and gr.libelle='SYSTEME' and ic.libelle='OPT_IN' and supprimer='N' ) as opt_in /* OPT_IN */

	FROM identite i 

    LEFT OUTER JOIN identite_complement ic on i.identite_id=ic.identite_id 
    LEFT OUTER JOIN global_appellation a on i.appellation_id=a.appellation_id
    LEFT OUTER JOIN libelle_tel lib1 on i.postal_tel1_libelle_id=lib1.libelle_tel_id
    LEFT OUTER JOIN libelle_tel lib2 on i.postal_tel2_libelle_id=lib2.libelle_tel_id
    LEFT OUTER JOIN libelle_tel lib3 on i.postal_tel3_libelle_id=lib3.libelle_tel_id
    LEFT OUTER JOIN libelle_tel lib4 on i.postal_tel4_libelle_id=lib4.libelle_tel_id
    LEFT OUTER JOIN libelle_tel lib5 on i.postal_tel5_libelle_id=lib5.libelle_tel_id
    LEFT OUTER JOIN libelle_tel lib6 on i.postal_tel4_libelle_id=lib6.libelle_tel_id
    LEFT OUTER JOIN libelle_tel lib7 on i.postal_tel5_libelle_id=lib7.libelle_tel_id 
    LEFT OUTER JOIN identite_compl_web icw on i.identite_id=icw.identite_id

  LEFT OUTER JOIN global_langue gl on gl.id = ic.langue_id
  LEFT OUTER JOIN langue l on l.langue_code = gl.code

    WHERE FicheSupprimer='[ficheSupprimer]' 