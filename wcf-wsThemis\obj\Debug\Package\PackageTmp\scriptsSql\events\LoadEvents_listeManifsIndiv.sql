﻿
declare @slistOffres varchar(50)

set @slistOffres ='[listoffersID]';

create table #myoffres (offreid int)

insert into #myoffres 
	SELECT name FROM splitstring (@slistOffres,',') c

--select * from #myoffres

if (@slistOffres ='') /* pas d'offres */
	begin
		SELECT 
			min(s.sessionDate) as FirstSessionDate,
			max(s.sessionDate) as LastSessionDate,EventName , EventCode, EventID, EventDescription1, EventDescription2, EventDescription3, EventDescription4, EventDescription5,
			 PlaceID,PlaceName,PlaceCode, groupe_id,
			 sum(AvalaiblesSeatsInternetCount) as AvalaiblesSeatsInternetCount,max(NotShowSessionDate) as NotShowSessionDate,islock,isPrivate as isPrivate
			 from
			(
				SELECT 
					DATEADD(MONTH, DATEDIFF(MONTH, 0, s.seance_date_deb), 0) as monthD, 
					s.seance_date_deb as sessionDate,
				--min(s.seance_date_deb) as FirstSessionDate,
				--max(sfin.seance_date_deb) as LastSessionDate,
				 sum(distinct  case sc.Cible_id when 0 then 0  else POWER(2,sc.Cible_id) end) as cible,
				 id_genre, 
				 groupe_id as id_genre_groupe, 
				 m.manifestation_id as EventID,manifestation_nom as EventName,manifestation_code as EventCode,
				 manifestation_descrip as EventDescription1,manifestation_descrip2 as EventDescription2,manifestation_descrip3 as EventDescription3,
				 manifestation_descrip4 as EventDescription4,manifestation_descrip5 as EventDescription5,
				 l.lieu_id as PlaceID,lieu_nom as PlaceName,lieu_code as PlaceCode,
					manifestation_groupe_id as groupe_id,

				gp.dispo as AvalaiblesSeatsInternetCount,s.nepasafficherdate as NotShowSessionDate,gpm.islock,isForPA as isPrivate
				FROM VueGestionPlaceManifSeanceAll gp
				INNER JOIN GP_manifestation gpm on gpm.manifestation_id=gp.manif_id
				INNER JOIN traduction_manifestation m ON m.manifestation_id=gp.manif_id  and m.langue_id=1
				INNER JOIN seance s ON s.seance_id=gp.seance_id and s.manifestation_id=gp.manif_id 
				INNER JOIN traduction_lieu l ON l.lieu_id=s.lieu_id  and l.langue_id=1
				INNER JOIN seance sfin ON sfin.seance_id=gp.seance_id and sfin.manifestation_id=gp.manif_id
				LEFT OUTER JOIN traduction_manifestation_groupe mg on mg.manif_groupe_id =m.manifestation_groupe_id  and mg.langue_id=1
				LEFT OUTER JOIN seance_cible sc on sc.Seance_id=s.seance_Id 
				LEFT OUTER JOIN cible c on c.id=sc.cible_id 
				LEFT OUTER JOIN manifestation_genre mgenre ON ID_genre=mgenre.id
				 and mg.langue_id=1
				 WHERE 1=1  
				 GROUP BY manifestation_nom,manifestation_code,manif_groupe_nom,m.manifestation_id,
				 manifestation_descrip,manifestation_descrip2,manifestation_descrip3,manifestation_descrip4,
				 manifestation_descrip5,l.lieu_id,lieu_nom,
				 lieu_code,manifestation_groupe_id,
				 gpm.islock,isForPA,id_genre,mgenre.groupe_id ,s.seance_date_deb, dispo, s.NEPASAFFICHERDATE, islock, isforPA
			 ) s

			 GROUP BY monthD, EventName , EventCode, EventID, EventDescription1, EventDescription2, EventDescription3, EventDescription4, EventDescription5,
			 PlaceID,PlaceName,PlaceCode, groupe_id, islock, isPrivate

			ORDER BY FirstSessionDate
	END
	ELSE
	BEGIN /* offres  */
	
	SELECT 
			min(s.sessionDate) as FirstSessionDate,
			max(s.sessionDate) as LastSessionDate,EventName , EventCode, EventID, EventDescription1, EventDescription2, EventDescription3, EventDescription4, EventDescription5,
			 PlaceID,PlaceName,PlaceCode, groupe_id,
			 sum(AvalaiblesSeatsInternetCount) as AvalaiblesSeatsInternetCount,max(NotShowSessionDate) as NotShowSessionDate,islock,OffrePartic as isPrivate
			 from
			(
					select 
					DATEADD(MONTH, DATEDIFF(MONTH, 0, s.seance_date_deb), 0) as monthD, 
					s.seance_date_deb as sessionDate,
				--min(s.seance_date_deb) as FirstSessionDate,
				--max(sfin.seance_date_deb) as LastSessionDate,
				 sum(distinct  case sc.Cible_id when 0 then 0  else POWER(2,sc.Cible_id) end) as cible,
				 id_genre, 
				 groupe_id as id_genre_groupe, 
				 m.manifestation_id as EventID,manifestation_nom as EventName,manifestation_code as EventCode,
				 manifestation_descrip as EventDescription1,manifestation_descrip2 as EventDescription2,manifestation_descrip3 as EventDescription3,
				 manifestation_descrip4 as EventDescription4,manifestation_descrip5 as EventDescription5,
				 l.lieu_id as PlaceID,lieu_nom as PlaceName,lieu_code as PlaceCode,
					manifestation_groupe_id as groupe_id,

				gp.dispo as AvalaiblesSeatsInternetCount,s.nepasafficherdate as NotShowSessionDate,gpm.islock,'1' as OffrePartic
				FROM VueGestionPlaceParticuliers gp
				INNER JOIN GP_manifestation gpm on gpm.manifestation_id=gp.manif_id

				INNER JOIN offre_gestion_place ogp on ogp.gestion_place_id=gp.gestion_place_id
				INNER JOIN #myoffres myo ON myo.offreid =ogp.offre_Id
				INNER JOIN traduction_manifestation m ON m.manifestation_id=gp.manif_id  and m.langue_id=1
				INNER JOIN seance s ON s.seance_id=gp.seance_id and s.manifestation_id=gp.manif_id 
				INNER JOIN traduction_lieu l ON l.lieu_id=s.lieu_id  and l.langue_id=1
				INNER JOIN seance sfin ON sfin.seance_id=gp.seance_id and sfin.manifestation_id=gp.manif_id
				LEFT OUTER JOIN traduction_manifestation_groupe mg on mg.manif_groupe_id =m.manifestation_groupe_id  and mg.langue_id=1
				LEFT OUTER JOIN seance_cible sc on sc.Seance_id=s.seance_Id 
				LEFT OUTER JOIN cible c on c.id=sc.cible_id 
				LEFT OUTER JOIN manifestation_genre mgenre ON ID_genre=mgenre.id
				 and mg.langue_id=1
				 
				 GROUP BY manifestation_nom,manifestation_code,manif_groupe_nom,m.manifestation_id,
				 manifestation_descrip,manifestation_descrip2,manifestation_descrip3,manifestation_descrip4,
				 manifestation_descrip5,l.lieu_id,lieu_nom,
				 lieu_code,manifestation_groupe_id,
				 gpm.islock,id_genre,mgenre.groupe_id ,s.seance_date_deb, dispo, s.NEPASAFFICHERDATE, islock
			 ) s

			 GROUP BY monthD, EventName , EventCode, EventID, EventDescription1, EventDescription2, EventDescription3, EventDescription4, EventDescription5,
			 PlaceID,PlaceName,PlaceCode, groupe_id, islock, OffrePartic

			ORDER BY FirstSessionDate


	END

	drop table #myoffres

	--select* from VueGestionPlaceParticuliers