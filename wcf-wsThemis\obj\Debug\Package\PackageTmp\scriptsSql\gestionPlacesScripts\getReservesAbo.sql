﻿
declare @formuleId int = [formulaID]
declare @eventId int = [eventID]
declare @sessionId int = [sessionID]



IF @formuleId != 0
BEGIN

	IF @eventId = 0
	BEGIN
		set @eventId = (select top 1 manif_id from gestion_place where formule_id=@formuleId and seance_id =@sessionId)
	END

	SELECT reserve_id, reserve_nom, reserve_code from
	(SELECT r.reserve_id, reserve_nom, reserve_code FROM gestion_place_reserve gpr
	inner join reserve r on r.reserve_id=gpr.reserve_id 
	inner join gestion_place gp on gp.gestion_place_id =gpr.gestion_place_id
	WHERE r.reserve_id=gpr.reserve_id AND formule_id=@formuleId
	and manif_id = @eventId 
	and 
	seance_id =@sessionId and gp.type_tarif_id in ([listTarifsID])
	UNION
	SELECT 0 as reserve_id,'AUCUNE'  as reserve_nom, 'AUCUNE' as reserve_code FROM gestion_place gp
	whERE gp.formule_id =@formuleId
	and manif_id =@eventId and seance_id =@sessionId and gp.type_tarif_id  in ([listTarifsID])
	and aucune_reserve=1) s order by reserve_id
	
END
else
BEGIN
	  SELECT reserve_id, reserve_nom, reserve_code from
	(SELECT r.reserve_id, reserve_nom, reserve_code FROM gestion_place_reserve gpr
	inner join reserve r on r.reserve_id=gpr.reserve_id 
	inner join gestion_place gp on gp.gestion_place_id =gpr.gestion_place_id
	WHERE r.reserve_id=gpr.reserve_id AND formule_id is not null
	and manif_id =@eventId 
	and seance_id =@sessionId 
	UNION
	SELECT 0 as reserve_id,'AUCUNE'  as reserve_nom, 'AUCUNE' as reserve_code FROM gestion_place gp
	whERE manif_id =@eventId and seance_id =@sessionId AND formule_id is not null --and gp.type_tarif_id  in (4290034)
	and aucune_reserve=1) s order by reserve_id
END



/*



if ([formulaID]!=0 and [listTarifsID]='')
begin

SELECT reserve_id, reserve_nom, reserve_code from
(SELECT r.reserve_id, reserve_nom, reserve_code FROM gestion_place_reserve gpr
inner join reserve r on r.reserve_id=gpr.reserve_id 
inner join gestion_place gp on gp.gestion_place_id =gpr.gestion_place_id
WHERE r.reserve_id=gpr.reserve_id AND formule_id=[formulaID]
and manif_id = [eventID] and seance_id =[sessionID] and gp.type_tarif_id in ([listTarifsID])
UNION
SELECT 0 as reserve_id,'AUCUNE'  as reserve_nom, 'AUCUNE' as reserve_code FROM gestion_place gp
whERE gp.formule_id =[formulaID]
and manif_id =[eventID] and seance_id =[sessionID] and gp.type_tarif_id  in ([listTarifsID])
and aucune_reserve=1) s order by reserve_id
end
else
begin
	  SELECT reserve_id, reserve_nom, reserve_code from
(SELECT r.reserve_id, reserve_nom, reserve_code FROM gestion_place_reserve gpr
inner join reserve r on r.reserve_id=gpr.reserve_id 
inner join gestion_place gp on gp.gestion_place_id =gpr.gestion_place_id
WHERE r.reserve_id=gpr.reserve_id AND formule_id is not null
and manif_id =[eventID] and seance_id =[sessionID] --and gp.type_tarif_id in ([listTarifsID])
UNION
SELECT 0 as reserve_id,'AUCUNE'  as reserve_nom, 'AUCUNE' as reserve_code FROM gestion_place gp
whERE manif_id =[eventID] and seance_id =[sessionID] AND formule_id is not null --and gp.type_tarif_id  in ([listTarifsID])
and aucune_reserve=1) s order by reserve_id
end

*/