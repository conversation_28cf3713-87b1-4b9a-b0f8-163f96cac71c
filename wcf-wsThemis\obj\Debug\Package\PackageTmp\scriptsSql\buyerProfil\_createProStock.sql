﻿USE [LANDESBUHNEN_SACHSEN]
GO
/****** Object:  StoredProcedure [dbo].[SP_WS_GETHISTO]    Script Date: 06/03/2017 11:22:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[SP_WS_GETHISTO_EPoS]  
    -- Add the parameters for the stored procedure here  
@buyerProfil int ,
@identiteIdP int,
@listCommandes varchar(max) 
AS  
BEGIN  
    -- SET NOCOUNT ON added to prevent extra result sets from  
    -- interfering with SELECT statements.  
    SET NOCOUNT ON;  
DECLARE @MANIFID INT,@DOSSIERID INT, @COMMANDEID INT  
    -- Insert statements for procedure here  
      
	  --select * from profil_acheteur

declare @identiteId int
select @identiteId = paiement_profil_acheteur FROM profil_acheteur WHERE id=@buyerProfil;
if (@identiteId is null)
	set @identiteId = @identiteIdP; 

--select * from prof
  
create table #TmpEntree (  
eventname varchar(50),  
sessiondate datetime,  
eventid int,  
orderid int,  
dossierid int,  
pricename varchar(50) ,  
categoryname varchar(50),  
type_ligne varchar(10),  
denom  varchar(50), rank varchar(50), seat varchar(50),  
amount int  
,amountValue int
,etat varchar(1)  
,entreeid int  
,seanceid int  
,date_operation datetime
)  

select Data into #tblListCmds from dbo.Split(@listCommandes,',') OPTION (MAXRECURSION 0)
    
DECLARE complex_cursor CURSOR FOR  
        SELECT   
        distinct top 100 s.manifestation_id, c.commande_id,dossier_id  from commande c  
        inner join commande_ligne cl ON cl.commande_id=c.commande_id      
        inner join seance s on s.seance_Id=cl.seance_id and cl.manifestation_id=s.manifestation_id   
        where c.identite_id=@identiteId --and cl.manifestation_id<>0  
        --and s.seance_date_deb>dateadd(day, - 3,GETDATE())  
        and cl.type_ligne='DOS'  
		and c.commande_id in (select * from #tblListCmds 	)
        order by c.commande_id desc  
          
OPEN complex_cursor;  
FETCH  NEXT FROM complex_cursor INTO @MANIFID, @COMMANDEID,@DOSSIERID;  
while(@@FETCH_STATUS=0)  
    BEGIN  
    DECLARE @SQL VARCHAR(5000)  
    IF (@MANIFID<>0)  
    BEGIN  
        SET @SQL ='INSERT INTO #TmpEntree SELECT m.manifestation_nom, s.seance_date_deb,  
        s.manifestation_id,  
        cmd.commande_id, dsvg.dossier_id, tt.type_tarif_nom, c.categ_nom,''entree''  
        ,d.denom_nom, r.rang, r.siege,  
          
        esvg.montant1 * 100 + esvg.montant2 * 100 +  
          
                            case  when modecol4=''REMISE''  then - esvg.montant4* 100   
                                        when modecol4=''TAXE'' or modecol4=''COMMISSION'' then + esvg.montant4* 100   
                                        else 0 END   
                                        +  
                         case  when modecol5=''REMISE''  then - esvg.montant5 * 100   
                                        when modecol5=''TAXE'' or modecol5=''COMMISSION'' then + esvg.montant5* 100   
                                        else 0 END +  
                         case  when modecol6=''REMISE''   then - esvg.montant6 * 100   
                                        when modecol6=''TAXE'' or modecol6=''COMMISSION'' then + esvg.montant6* 100   
                                        else 0 END +  
                        case  when modecol7=''REMISE''  then - esvg.montant7 * 100   
                                        when modecol7=''TAXE'' or modecol7=''COMMISSION'' then + esvg.montant7* 100   
                                        else 0 END +  
                         case  when modecol8=''REMISE'' then - esvg.montant8 * 100   
                                        when modecol8=''TAXE'' or modecol8=''COMMISSION''  then + esvg.montant8* 100   
                                        else 0 END +  
                         case  when modecol9=''REMISE''  then - esvg.montant9 * 100   
                                        when modecol9=''TAXE''  or modecol9=''COMMISSION'' then + esvg.montant9* 100   
                                        else 0 END +  
                         case  when modecol10=''REMISE''  then - esvg.montant10 * 100   
                                        when modecol10=''TAXE'' or modecol10=''COMMISSION'' then + esvg.montant10* 100   
                                        else 0 END  as amount,  
  
        esvg.montant3 * 100 as amountValue,  


        esvg.entree_etat, e.entree_id, e.seance_id,
		dsvg.date_operation
          
         FROM dossiersvg_' + LTRIM(STR(@MANIFID)) + ' dsvg  
            inner join entreesvg_' + LTRIM(STR(@MANIFID)) + ' esvg on esvg.dossier_id = dsvg.dossier_id and dsvg.dossier_v=esvg.dossier_v  
            inner join structure struc on 1=1  
            inner join seance s on esvg.seance_id=s.seance_Id  
            inner join manifestation m on m.manifestation_id=s.manifestation_id and m.manifestation_id=' + LTRIM(STR(@MANIFID)) + '  
            inner join type_tarif tt on esvg.type_tarif_id=tt.type_tarif_id  
            inner join categorie c on esvg.categorie_id =c.categ_id  
            inner join entree_' + LTRIM(STR(@MANIFID)) + ' e ON esvg.entree_id=e.entree_id  
            inner join commande cmd ON cmd.commande_id=dsvg.commande_id       
            inner join commande_ligne cmdl ON cmdl.commande_id=cmd.commande_id AND cmdl.dossier_id=dsvg.dossier_id  and cmdl.seance_id=s.seance_Id    
            inner join REFERENCE_LIEU_PHYSIQUE R on REFERENCE_UNIQUE_PHYSIQUE_ID = REF_UNIQ_PHY_ID  
            inner join DENOMINATION D on R.DENOMINATION_ID = D.DENOM_ID  

            WHERE dsvg.dossier_id='  + LTRIM(STR(@DOSSIERID)) + ' AND cmd.commande_id=' + LTRIM(STR(@COMMANDEID))  
            + ' AND type_ligne=''DOS''   
              
                    AND esvg.dossier_v = (select MAX(esvg2.dossier_v) FROM entreesvg_' + LTRIM(STR(@MANIFID)) + ' esvg2   
            --on esvg2.dossier_id=dsvg.dossier_id  
            WHERE --dsvg2.dossier_id  =dsvg.dossier_id and  
                 esvg.entree_id=esvg2.entree_id and esvg2.dossier_id=esvg.dossier_id  
                  and esvg.seance_id=s.seance_Id  
                 )  
              
      
                   
                   
            AND (dsvg.dossier_etat <> ''P'' or dsvg.type_operation <> ''EDIT'')  
            AND dsvg.identite_id=' +  LTRIM(STR(@identiteId)) + '  
            '  
              
        PRINT @SQL  
        EXEC (@SQL)  
    END  
      
    --print @MANIFID  
    --print @COMMANDEID  
FETCH  NEXT FROM complex_cursor INTO @MANIFID,@COMMANDEID,@DOSSIERID;  
    END   
  
-- ENTREES :  
  
SELECT eventName, sessiondate, orderid, sum(amount), sum(amountValue), count(*) as nbSeats
FROM #TmpEntree  
group by eventName, sessiondate, orderid
order by orderid
  
-- PRODUITS :  
SELECT  
    distinct  cl.manifestation_id as eventid, c.commande_id as orderid,dossier_id as dossierid, p.produit_id as productid, produit_nom as productname from commande c  
        inner join commande_ligne cl ON cl.commande_id=c.commande_id          
        inner join dossier_produit dp on dp.commande_id=cl.commande_id  
        inner join produit p on dp.produit_id=p.produit_id  
        where c.identite_id=@identiteId and type_ligne='PRO' --and cl.manifestation_id=0  
  
  
CLOSE complex_cursor;  
DEALLOCATE complex_cursor;  
      
END  
