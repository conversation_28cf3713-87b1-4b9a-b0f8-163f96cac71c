# Nouveau bouton "Créer une file d'attente"

## Description
Un nouveau bouton "Créer une file d'attente" a été ajouté dans l'interface d'administration des files d'attente (QueuingSettings.aspx). Ce bouton fait exactement la même chose que le bouton "Actif" de la section File d'attente V2 : il active automatiquement la file d'attente.

## Fonctionnalité
Quand l'utilisateur clique sur le bouton "Créer une file d'attente", le système :

1. **Appelle une méthode backend dédiée** `CreateQueue`
2. **Crée une file d'attente V2** avec des paramètres par défaut optimaux
3. **Active automatiquement** tous les paramètres nécessaires
4. **Sauvegarde** la configuration dans les fichiers système
5. **Recharge la page** pour afficher les nouveaux paramètres
6. **Affiche un message de confirmation** personnalisé

## Fichiers modifiés

### 1. Interface utilisateur
- **login/pages/QueuingSettings.aspx** : Ajout du bouton HTML
- **login/obj/Release/Package/PackageTmp/pages/QueuingSettings.aspx** : Même modification

### 2. Logique JavaScript
- **login/assets/js/pages/queuing_settings.js** : Ajout de la fonction `createQueueClick()` avec appel AJAX
- **login/obj/Release/Package/PackageTmp/assets/js/pages/queuing_settings.js** : Même modification

### 3. Backend C#
- **login/pages/QueuingSettings.aspx.cs** : Ajout de la méthode WebMethod `CreateQueue`

### 4. Traductions
- **login/pages/Resources/translate.fr.xml** : Ajout de la clé `btn_create_queue`
- **login/obj/Release/Package/PackageTmp/pages/Resources/translate.fr.xml** : Même modification

## Code ajouté

### HTML (dans QueuingSettings.aspx)
```html
<!-- Bouton pour créer une file d'attente -->
<div class="row">
    <div class="col-xs-12 text-left mt-2 mb-3">
        <button type="button" id="btnCreateQueue" class="btn btn-success btn-lg" data-trad="btn_create_queue">
            <i class="icon-plus"></i> Créer une file d'attente
        </button>
    </div>
</div>
```

### JavaScript (dans queuing_settings.js)
```javascript
// Nouveau bouton pour créer une file d'attente
$('#btnCreateQueue').on('click', function () {
    createQueueClick()
})

// Fonction pour créer une file d'attente via l'API backend
function createQueueClick() {
    // Désactiver le bouton pendant le traitement
    $('#btnCreateQueue').prop('disabled', true).html('<i class="icon-spinner icon-spin"></i> Création en cours...');

    var sData = JSON.stringify({
        _structureId: StructureId,
        _langCode: LangCode
    });

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'CreateQueue',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (response) {
            // Réactiver le bouton
            $('#btnCreateQueue').prop('disabled', false).html('<i class="icon-plus"></i> Créer une file d\'attente');

            // Afficher le message de succès
            ShowError("success", response.d, "alert alert-success alert-dismissable alert-fixed");

            // Recharger la page pour afficher les nouveaux paramètres
            setTimeout(function() {
                window.location.reload();
            }, 2000);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            // Réactiver le bouton et afficher l'erreur
            $('#btnCreateQueue').prop('disabled', false).html('<i class="icon-plus"></i> Créer une file d\'attente');
            ShowError("error", "Erreur lors de la création de la file d'attente", "alert alert-danger alert-dismissable alert-fixed");
        }
    });
}
```

### Backend C# (dans QueuingSettings.aspx.cs)
```csharp
[WebMethod]
[ScriptMethod(ResponseFormat = ResponseFormat.Json)]
public static string CreateQueue(int _structureId, string _langCode)
{
    try
    {
        // Créer un objet Queuing avec les paramètres par défaut pour V2
        Queuing newQueuing = new Queuing
        {
            QueuingV2 = new QueuingV2
            {
                IsActive = true, // Activer V2
                IsActiveFileAttente = true, // Activer la file d'attente
                ViaQueuingSite = "O",
                IsOpen = true, // Ouvrir la file d'attente
                IsCaptcha = false,
                MaxActiveUsers = int.Parse(MyConfigurationManager.AppSettings("MaxNumberOfPeople") ?? "100"),
                UsersPerTick = int.Parse(MyConfigurationManager.AppSettings("NumberOfPeoplePerPassage") ?? "10"),
                DbType = "sqlserver"
            }
            // ... autres paramètres par défaut
        };

        // Utiliser la méthode SaveQueuing existante pour sauvegarder
        string result = SaveQueuing(_structureId, _langCode, newQueuing);

        return result == "ok" ? "File d'attente V2 créée et activée avec succès !" : "Erreur lors de la création";
    }
    catch (Exception ex)
    {
        return $"Erreur : {ex.Message}";
    }
}
```

### Traduction (dans translate.fr.xml)
```xml
<btn_create_queue trad="btn_create_queue">Créer une file d'attente</btn_create_queue>
```

## Emplacement du bouton
Le bouton est placé **à l'extérieur des accordéons**, juste après la section des files d'attente mais avant le message d'avertissement. Il est toujours visible, même quand les accordéons sont fermés. Style : Bootstrap vert large (`btn-success btn-lg`) avec une icône plus (`icon-plus`).

## Comportement
- **Appel API dédié** : Le bouton appelle une méthode backend spécialisée `CreateQueue`
- **Paramètres optimaux** : Crée une file d'attente avec des paramètres par défaut optimaux
- **Feedback visuel** : Le bouton affiche un spinner pendant le traitement
- **Gestion d'erreurs** : Affiche des messages d'erreur appropriés en cas de problème
- **Rechargement automatique** : La page se recharge pour afficher les nouveaux paramètres
- **Sauvegarde complète** : Utilise la logique de sauvegarde existante pour garantir la cohérence

## Test
Pour tester la fonctionnalité :
1. Aller sur la page QueuingSettings.aspx
2. Cliquer sur le bouton vert "Créer une file d'attente"
3. Vérifier que le radio button "File d'attente V2" est activé
4. Vérifier que la checkbox "Activation de la file d'attente" est cochée
5. Vérifier que les paramètres sont sauvegardés
6. Vérifier l'affichage du message de confirmation
