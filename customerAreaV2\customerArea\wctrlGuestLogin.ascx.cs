﻿using customerArea.App_Code;
using log4net;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using ws_DTO.objets_liaisons;

namespace customerArea
{
    public partial class wctrlGuestLogin : System.Web.UI.UserControl
    {
        private static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        protected void Page_Load(object sender, EventArgs e)
        {
            
            if (!Page.IsPostBack)
            {
               
                int IdStructure = (int)Session["IdStructure"];

                int eventid = 0;

                if (Request.QueryString["eventId"] != "" && Request.QueryString["eventId"] != null && int.TryParse(Request.QueryString["eventId"], out eventid))
                {
                }
                string idpa = "0";
                if (Session["ProfilAcheteurId"] != null)
                {
                    idpa = (string)Session["ProfilAcheteurId"];
                }

                int eventId = 0;
                if (Request.QueryString["eventId"] != "" && Request.QueryString["eventId"] != null && int.TryParse(Request.QueryString["eventId"], out eventId))
                {
                    System.Web.HttpContext.Current.Session["eventId"] = eventId;
                }
                /*MyDictionary mySSC = new MyDictionary();
                mySSC = mySSC.GetDictionaryFromCache(IdStructure);


                if (mySSC["CREATEPROFILGUEST"] == "1")
                {
                    InitIHMGuest(IdStructure, eventid.ToString(), idpa);
                };
                */

                InitIHMGuest(IdStructure, eventid, idpa);


            }
        }

        protected void InitIHMGuest(int structureId, int eventId, string idpa)
        {
            Ihm myIhm = new Ihm();

            string plateformCode = "";
            if (Request.QueryString["plateformCode"] != "" && Request.QueryString["plateformCode"] != null)
            {
                plateformCode = Request.QueryString["plateformCode"].ToString();
                System.Web.HttpContext.Current.Session["plateformCode"] = Request.QueryString["plateformCode"].ToString();
            }

            string lang = Initialisations.GetUserLanguage();

            // dynamic globalPlateform = Settings.GetSettings(structureId, eventId, int.Parse(idpa), plateformCode, lang);

            var listJsonSettingsToMerge = new List<CustomJsonSettings>();
            listJsonSettingsToMerge.Add(new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true });
            listJsonSettingsToMerge.Add(new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false });
            listJsonSettingsToMerge.Add(new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false });


            dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, structureId, eventId, int.Parse(idpa), plateformCode, lang);

            if (globalPlateform.global.guest != null)
            {

                //logger.Debug("globalPlateform" + globalPlateform.ToString());

                var isGuest = globalPlateform.global.guest.Value;
                if (globalPlateform.global != null && globalPlateform.customer != null && globalPlateform.customer.global != null && globalPlateform.customer.global.guest != null)
                {
                    isGuest = globalPlateform.customer.global.light.Value;
                }
                if (isGuest)
                {
                    TextInfo cui = new CultureInfo(lang, false).TextInfo;

                    myIhm.ReadXMLFile("IHMPROFILGuest" + cui.ToTitleCase(plateformCode.ToLower()), eventId.ToString(), idpa, "page", "tableau", true);
                    myIhm.GetVisibility("diventeteguestlogin", ref diventeteguestlogin);
                    myIhm.GetVisibility("divexplainguestlogin", ref divexplainguestlogin);



                    if (lbEmailGuest != null && tbEmailGuest != null)
                    {
                        // ************* email
                        myIhm.GetVisibility("Email", ref tbEmailGuest, ref lbEmailGuest, "lbl_email_create");
                        
                    }

                    if (lbPrenomGuest != null && tbPrenomGuest != null)
                    {
                        myIhm.GetVisibility("FirstName", ref tbPrenomGuest, ref lbPrenomGuest, "lbl_prenom");
                    }

                    if (lbNomGuest != null && tbNomGuest != null)
                    {
                        myIhm.GetVisibility("SurName", ref tbNomGuest, ref lbNomGuest, "lbl_nom");
                    }

                    if (lbPostalCodeGuest != null && tbPostalCodeGuest != null)
                    {
                        myIhm.GetVisibility("PostalCode", ref tbPostalCodeGuest, ref lbPostalCodeGuest, "lbl_cp");
                    }

                    if (lbAddressGuest != null && tbAddressGuest != null)
                    {
                        myIhm.GetVisibility("Address", ref tbAddressGuest, ref lbAddressGuest, "lbl_address");
                    }

                    if (lbCityGuest != null && tbCityGuest != null)
                    {
                        myIhm.GetVisibility("City", ref tbCityGuest, ref lbCityGuest, "lbl_city");
                    }

                    if (lbCountryGuest != null && ddlCountryGuest != null)
                    {
                        myIhm.GetVisibility("Country", ref ddlCountryGuest, ref lbCountryGuest);

                        // Charger la liste des pays si le champ est visible
                        if (ddlCountryGuest.Visible)
                        {
                            ddlCountryGuest.Items.Clear();
                            ddlCountryGuest.Items.Add(new ListItem("Select a country", "0"));
                            ddlCountryGuest.Items.AddRange(Initialisations.LoadCountryList(structureId, false).ToArray());
                        }
                    }

                    if (lbMobileNumGuest != null && tbMobileNumGuest != null)
                    {
                        myIhm.GetVisibility("MobileNum", ref tbMobileNumGuest, ref lbMobileNumGuest, "lbl_mobile");
                    }


                }
            }

        }
    }
}