﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="btnSave" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="AucuneStructureSelectionnee" xml:space="preserve">
    <value>aucune structure sélectionnée</value>
  </data>
  <data name="AfficherLEtage" xml:space="preserve">
    <value>Afficher l'étage (fr)</value>
  </data>
  <data name="AfficherLEtageModeNormal" xml:space="preserve">
    <value>Afficher l'étage s'il existe (fr)</value>
  </data>
  <data name="AfficherLEtageModezaperLEtage" xml:space="preserve">
    <value>Zapper l'étage (fr)</value>
  </data>
  <data name="AfficherLEtageModezapperLEtage" xml:space="preserve">
    <value>Zapper l'étage (fr)</value>
  </data>
  <data name="AttribuerUneImageSurUneDesSéancesDeCetteManifestation" xml:space="preserve">
    <value>Attribuer une image sur une des séances de cette manifestation</value>
  </data>
  <data name="CodeLangue" xml:space="preserve">
    <value>Code langue</value>
  </data>
  <data name="Commentaire" xml:space="preserve">
    <value>Commentaire</value>
  </data>
  <data name="CommentaireBasDePage" xml:space="preserve">
    <value>Commentaire bas de page</value>
  </data>
  <data name="CommentaireHautDePage" xml:space="preserve">
    <value>Commentaire haut de page</value>
  </data>
  <data name="Copier" xml:space="preserve">
    <value>Copier</value>
  </data>
  <data name="CopierLeCommentaire" xml:space="preserve">
    <value>Copier le commentaire</value>
  </data>
  <data name="DateDeLaSéance" xml:space="preserve">
    <value>Date de la séance</value>
  </data>
  <data name="delete" xml:space="preserve">
    <value>supprimer</value>
  </data>
  <data name="Editer" xml:space="preserve">
    <value>Editer</value>
  </data>
  <data name="EffacerCommentaire" xml:space="preserve">
    <value>Effacer le commentaire</value>
  </data>
  <data name="ErrorPlusieursRegles" xml:space="preserve">
    <value>Attention !! Il existe plusieurs règles générales</value>
  </data>
  <data name="fichiervide" xml:space="preserve">
    <value>Le fichier est vide</value>
  </data>
  <data name="FileUploadedSuccessfully" xml:space="preserve">
    <value>File uploaded successfully</value>
  </data>
  <data name="go" xml:space="preserve">
    <value>ok (fr)</value>
  </data>
  <data name="HtmlAttendu" xml:space="preserve">
    <value>.html attendu</value>
  </data>
  <data name="IdDeLaSéance" xml:space="preserve">
    <value>Id de la séance</value>
  </data>
  <data name="InvalidFileContent" xml:space="preserve">
    <value>contenu fichier invalide (fr)</value>
  </data>
  <data name="InvalidFilenameSupplied" xml:space="preserve">
    <value>nom fichier invalide (fr)</value>
  </data>
  <data name="LangueParDefaut" xml:space="preserve">
    <value>Langue par defaut (general terms fr)</value>
  </data>
  <data name="LeFichierExiste" xml:space="preserve">
    <value>Le fichier existe</value>
  </data>
  <data name="Manifestations" xml:space="preserve">
    <value>Manifestations</value>
  </data>
  <data name="MiseAJourProperties" xml:space="preserve">
    <value>Mise à jour (fr)</value>
  </data>
  <data name="ModeCalendrier" xml:space="preserve">
    <value>Mode Calendrier (fr)</value>
  </data>
  <data name="ModeNormal" xml:space="preserve">
    <value>Mode Normal (fr)</value>
  </data>
  <data name="NomDeLaManifestation" xml:space="preserve">
    <value>Manifestation</value>
  </data>
  <data name="PathNotFound" xml:space="preserve">
    <value>Chemin d'accès introuvable (fr)</value>
  </data>
  <data name="PermissionToUploadFileDenied" xml:space="preserve">
    <value>Permission to upload file denied</value>
  </data>
  <data name="ReserveAucune" xml:space="preserve">
    <value>Aucune réserve (general terms fr)</value>
  </data>
  <data name="ResultCashStatement" xml:space="preserve">
    <value>Aucune commande selon ces critères (globaleresource fr)</value>
  </data>
  <data name="SeancesDeLaManif" xml:space="preserve">
    <value>Seances de la manif</value>
  </data>
  <data name="TypeDeChoixSéance" xml:space="preserve">
    <value>Type de choix séance (fr)</value>
  </data>
  <data name="TypeLocalResource" xml:space="preserve">
    <value>Type ( localResource) (fr)</value>
  </data>
  <data name="UnableToUploadFileExceedsMaximumLimit" xml:space="preserve">
    <value>Unable to upload file exceeds maximum limit</value>
  </data>
  <data name="UpLoad" xml:space="preserve">
    <value>UpLoad</value>
  </data>
  <data name="UploaderUnFichierHtmlDepuisVotreOrdinateur" xml:space="preserve">
    <value>Uploader un fichier html depuis votre ordinateur</value>
  </data>
  <data name="UploadLocalResource" xml:space="preserve">
    <value>Upload (localResource) (fr)</value>
  </data>
  <data name="Vider" xml:space="preserve">
    <value>vider</value>
  </data>
  <data name="Visualiser" xml:space="preserve">
    <value>Visualiser</value>
  </data>
  <data name="VisualiserEtModifierLeCommentaire" xml:space="preserve">
    <value>Visualiser et modifier le commentaire</value>
  </data>
  <data name="VisualiserLocalResource" xml:space="preserve">
    <value>Visualiser (localResource) (fr)</value>
  </data>
  <data name="VosManifestations" xml:space="preserve">
    <value>Vos manifestations</value>
  </data>
  <data name="VosSeancesSur" xml:space="preserve">
    <value>Vos seances sur  (GeneralTerm fr)</value>
  </data>
  <data name="Valider" xml:space="preserve">
    <value>Valider</value>
  </data>
  <data name="DeleteRuleSuccess" xml:space="preserve">
    <value>La règle a bien été supprimée</value>
  </data>
  <data name="DeleteRulesSuccess" xml:space="preserve">
    <value>Les regles de vente ont bien été supprimées</value>
  </data>
  <data name="ErrorDeleteRule" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression</value>
  </data>
  <data name="ErrorDeleteRules" xml:space="preserve">
    <value>Les règles de vente ont bien été supprimées</value>
  </data>
  <data name="lblActions" xml:space="preserve">
    <value>Actions</value>
    <comment>label des datatable Action</comment>
  </data>
  <data name="lblDeleteRulesSelected" xml:space="preserve">
    <value>Supprimer les règles sur toutes les séances sélectionnées  (general terms fr)</value>
  </data>
  <data name="lblEndDateOfValidity" xml:space="preserve">
    <value>jusqu'à (avant)</value>
  </data>
  <data name="lblEndDateOfValidityInt" xml:space="preserve">
    <value>jusqu'à (avant)</value>
  </data>
  <data name="lblGPID" xml:space="preserve">
    <value>Id</value>
  </data>
  <data name="lblIsValide" xml:space="preserve">
    <value>statut</value>
  </data>
  <data name="lblMax" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="lblMin" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="NbDispo" xml:space="preserve">
    <value>Dispo</value>
  </data>
  <data name="lblOperationDate" xml:space="preserve">
    <value>Date opération</value>
  </data>
  <data name="lblStartDateOfValidityInt" xml:space="preserve">
    <value> à partir de (avant)</value>
  </data>
  <data name="lblStartDateOfValidity" xml:space="preserve">
    <value>à partir de (avant)</value>
  </data>
  <data name="lblTooltipUpdateOffer" xml:space="preserve">
    <value>Mettre à jour l'offre (general terms fr)</value>
  </data>
  <data name="lblTooltipUpdateProfilAcheteur" xml:space="preserve">
    <value>Mettre à jour le profil acheteur (general terms fr)</value>
  </data>
  <data name="CopyRulesSuccess" xml:space="preserve">
    <value>Les règles de ventes ont bien été copiées</value>
  </data>
  <data name="DeleteRuleError" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression</value>
  </data>
  <data name="SuccessUpdateRules" xml:space="preserve">
    <value>Les règles de ventes ont bien été mises à jour  (general terms fr)</value>
  </data>
  <data name="deleteContrainte" xml:space="preserve">
    <value>La contrainte à bien été supprimée</value>
  </data>
  <data name="errorInsertContrainte" xml:space="preserve">
    <value>Une erreur est survenue pendant l'insertion de la contrainte</value>
  </data>
  <data name="successInsertContrainte" xml:space="preserve">
    <value>La contrainte à bien été ajoutée</value>
  </data>
  <data name="lblContrainteName" xml:space="preserve">
    <value>Nom de la contrainte</value>
  </data>
  <data name="lblEndDate" xml:space="preserve">
    <value>Date de fin</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="lblOfferName" xml:space="preserve">
    <value>Nom de l'offre</value>
  </data>
  <data name="lblSQL" xml:space="preserve">
    <value>SQL</value>
  </data>
  <data name="lblStartDate" xml:space="preserve">
    <value>Date de début</value>
  </data>
  <data name="successUpdateOffer" xml:space="preserve">
    <value>L'offre à bien été mis à jour</value>
  </data>
  <data name="btnSaveProfilAcheteur" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="ErrorDeleteOffer" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression</value>
  </data>
  <data name="ErrorUpdateProfilAcheteurForOffer" xml:space="preserve">
    <value>Une erreur s'est produite pendant la mise à jour des profils acheteur pour cette offre</value>
  </data>
  <data name="lblProfilAcheteurID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="lblProfilAcheteurName" xml:space="preserve">
    <value>Nom du profil acheteur</value>
  </data>
  <data name="SuccessDeleteOffer" xml:space="preserve">
    <value>L'offre à bien été supprimée</value>
  </data>
  <data name="SuccessInsertOffer" xml:space="preserve">
    <value>L'offre à bien été ajoutée</value>
  </data>
  <data name="SuccessUpdateProfilAcheteur" xml:space="preserve">
    <value>La mise à jour s'est terminée avec succès pour le profil acheteur</value>
  </data>
  <data name="SuccessUpdateProfilAcheteurForOffer" xml:space="preserve">
    <value>La mise à jour des profils acheteurs pour cette offre s'est terminée avec succès</value>
  </data>
  <data name="AddRulesSelected" xml:space="preserve">
    <value>Ajouter les règles sur toutes les séances sélectionnées </value>
  </data>
  <data name="Annuler" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Catégorie</value>
  </data>
  <data name="CompteClient" xml:space="preserve">
    <value>Compte client (pour facture)</value>
  </data>
  <data name="ConsommateurObligatoire" xml:space="preserve">
    <value>Consommateur obligatoire</value>
  </data>
  <data name="ErrorDeleteProfilAcheteur" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression</value>
  </data>
  <data name="ErrorInsertProfilAcheteur" xml:space="preserve">
    <value>Une erreur s'est produite pendant l'insertion du profil acheteur</value>
  </data>
  <data name="ErrorUpdateContrainteForOffer" xml:space="preserve">
    <value>Erreur pendant la mise à jour de l'offre pour une contrainte</value>
  </data>
  <data name="ErrorUpdateProfilAcheteur" xml:space="preserve">
    <value>Une erreur s'est produite pendant la mise à jour du profil acheteur</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Prénom</value>
  </data>
  <data name="Groupe" xml:space="preserve">
    <value>Groupe de manifestations</value>
  </data>
  <data name="Libelle" xml:space="preserve">
    <value>Libellé</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value> Veuillez patienter, le traitement est en cours... (Principale.master.resx)</value>
  </data>
  <data name="Non" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="OffreAucune" xml:space="preserve">
    <value>Aucune</value>
  </data>
  <data name="OperateurName" xml:space="preserve">
    <value>Operateur</value>
  </data>
  <data name="Oui" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="PaiementMode" xml:space="preserve">
    <value>Mode de reglement</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="Place" xml:space="preserve">
    <value>Lieu</value>
  </data>
  <data name="Reserve" xml:space="preserve">
    <value>Réserve</value>
  </data>
  <data name="Resume" xml:space="preserve">
    <value>Résumé (general terms fr)</value>
  </data>
  <data name="SessionDateTime" xml:space="preserve">
    <value>Séance</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="successDeleteProfilAcheteur" xml:space="preserve">
    <value>Le profil d'acheteur à bien été supprimé</value>
  </data>
  <data name="SuccessInsertProfilAcheteur" xml:space="preserve">
    <value>Le profil acheteur à bien été ajouté</value>
  </data>
  <data name="SuccessRuleInsert" xml:space="preserve">
    <value>La règle de vente a bien été ajoutée pour la séance  (general terms fr)</value>
  </data>
  <data name="SuccessRulesInsert" xml:space="preserve">
    <value>Les règles de ventes ont bien été ajoutées pour la liste de séances sélectionnées   (general terms fr)</value>
  </data>
  <data name="SuccessUpdateContrainteForOffer" xml:space="preserve">
    <value>La contrainte pour une offre à bien été mise à jour</value>
  </data>
  <data name="TitleDate" xml:space="preserve">
    <value>Date (general terms fr)</value>
  </data>
  <data name="TitleModeObtention" xml:space="preserve">
    <value>Mode d'obtention (general terms fr)</value>
  </data>
  <data name="TitleReserves" xml:space="preserve">
    <value>Réserves (general terms fr)</value>
  </data>
  <data name="TitleSeances" xml:space="preserve">
    <value>Choix des séances (general terms fr)</value>
  </data>
  <data name="TitleTarifs" xml:space="preserve">
    <value>Choix des tarifs (general terms fr)</value>
  </data>
  <data name="TypeEnvoi" xml:space="preserve">
    <value>Mode d'obtention</value>
  </data>
  <data name="TypeTarif" xml:space="preserve">
    <value>Tarif</value>
  </data>
  <data name="NomReserve" xml:space="preserve">
    <value>Nom de la réserve (general terms fr)</value>
  </data>
  <data name="Activer" xml:space="preserve">
    <value>Activer (general terms fr)</value>
  </data>
  <data name="Automatique" xml:space="preserve">
    <value>Automatique (GeneralTerm fr)</value>
  </data>
  <data name="Desactiver" xml:space="preserve">
    <value>Désactiver</value>
  </data>
  <data name="Heures" xml:space="preserve">
    <value>Heures  (general terms fr)</value>
  </data>
  <data name="Jours" xml:space="preserve">
    <value>Jours  (general terms fr)</value>
  </data>
  <data name="LegendEnd" xml:space="preserve">
    <value>Fin (GeneralTerm fr)</value>
  </data>
  <data name="LegendNbPlace" xml:space="preserve">
    <value>Nombre de places (general terms fr)</value>
  </data>
  <data name="LegendOptions" xml:space="preserve">
    <value>Options (GeneralTerm fr)</value>
  </data>
  <data name="LegendStart" xml:space="preserve">
    <value>Début (general terms fr)</value>
  </data>
  <data name="MemePlace" xml:space="preserve">
    <value>Même place (GeneralTerm fr)</value>
  </data>
  <data name="ModePrisesPlace" xml:space="preserve">
    <value>Mode de prise de places (GeneralTerm fr)</value>
  </data>
  <data name="PlacementLibre" xml:space="preserve">
    <value>Placement libre (GeneralTerm fr)</value>
  </data>
  <data name="Precedent" xml:space="preserve">
    <value>Précédent (GeneralTerm fr)</value>
  </data>
  <data name="Suivant" xml:space="preserve">
    <value>Suivant (GeneralTerm fr)</value>
  </data>
  <data name="SurPlan" xml:space="preserve">
    <value>Sur plan (GeneralTerm fr)</value>
  </data>
  <data name="TypePlaces" xml:space="preserve">
    <value>Type de prise de places (GeneralTerm fr)</value>
  </data>
  <data name="VoirPlaces" xml:space="preserve">
    <value>Voir les places (GeneralTerm fr)</value>
  </data>
  <data name="Produit" xml:space="preserve">
    <value>Nom du produit</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Type de stock</value>
  </data>
  <data name="Tester" xml:space="preserve">
    <value>Tester</value>
    <comment>indivUrlmanifs</comment>
  </data>
  <data name="UrlAccessDirect" xml:space="preserve">
    <value>Url pour un accès direct</value>
    <comment>indivUrlmanifs</comment>
  </data>
  <data name="ErrorDeleteProduct" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression</value>
  </data>
  <data name="SuccessDeleteProduct" xml:space="preserve">
    <value>Le produit internet a bien été supprimé</value>
  </data>
  <data name="BddName" xml:space="preserve">
    <value>Nom de la base de données</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="ConnexionTypeProd" xml:space="preserve">
    <value>Prod</value>
  </data>
  <data name="ConnexionTypeTest" xml:space="preserve">
    <value>Test</value>
  </data>
  <data name="ErrorAddConnexion" xml:space="preserve">
    <value>La connexion n'a pu être ajoutée</value>
  </data>
  <data name="ErrorUpdateConnexion" xml:space="preserve">
    <value>La connexion n'a pu être modifiée</value>
  </data>
  <data name="ErrorUpdateGroupRights" xml:space="preserve">
    <value>Une erreur s'est produite pendant la mise à jour des droits pour un group d'utilisateurs</value>
  </data>
  <data name="IPServeur" xml:space="preserve">
    <value>ip du serveur</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="SuccessAddConnexion" xml:space="preserve">
    <value>La connexion a bien été ajoutée</value>
  </data>
  <data name="SuccessUpdateConnexion" xml:space="preserve">
    <value>La connexion a bien été modifiée</value>
  </data>
  <data name="SuccessUpdateGroupRights" xml:space="preserve">
    <value>La mise à jour des droits pour les groupes d'utilisateurs à bien été effectuée</value>
  </data>
  <data name="TestConnexion" xml:space="preserve">
    <value>Test connexion</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Mettre à jour</value>
  </data>
  <data name="AucuneMaquette" xml:space="preserve">
    <value>Aucune maquette   (GeneralTerm fr)</value>
  </data>
  <data name="AucunFichierSauvegarder" xml:space="preserve">
    <value>Aucun fichier récemment sauvegardé</value>
  </data>
  <data name="GetLastFile" xml:space="preserve">
    <value>Récupérer le dernier fichier sauvegardé</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="SuccessCssInsert" xml:space="preserve">
    <value>Le fichier CSS à bien été sauvegardé</value>
  </data>
  <data name="FileNotFound" xml:space="preserve">
    <value>Aucun fichier trouvé default ou dans la structure</value>
  </data>
  <data name="ErrorUpdatePropertiesEvents" xml:space="preserve">
    <value>Une erreur est survenue pendant la mise à jour</value>
  </data>
  <data name="SuccessMailnsert" xml:space="preserve">
    <value>Le texte du mail à bien été sauvegarder</value>
  </data>
  <data name="SuccessUpdatePropertiesEvents" xml:space="preserve">
    <value>La mise à jour a été exécutée avec succès</value>
  </data>
  <data name="TabMailConfirmation" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="TabMailError" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="TabMailInscription" xml:space="preserve">
    <value>Inscription</value>
  </data>
  <data name="TabMailPassword" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="NotSVGDirectoryFound" xml:space="preserve">
    <value>Aucun répertoire SVG trouvé</value>
  </data>
  <data name="SuccessTagslnsert" xml:space="preserve">
    <value>Les tags à bien été sauvegarder</value>
  </data>
  <data name="ErrorDeleteSeanceFormule" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression</value>
  </data>
  <data name="ErrorDeleteStructureOnUser" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression des structures pour l'utilisateur</value>
  </data>
  <data name="ErrorDeleteUser" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression de l'utilisateur</value>
  </data>
  <data name="ErrorPasswordUser" xml:space="preserve">
    <value>Les mots de passes ne sont pas identiques</value>
  </data>
  <data name="ErrorInsertUser" xml:space="preserve">
    <value>Un problème est survenu pendant la création de l'utilisateur</value>
  </data>
  <data name="ErrorUpdateRule" xml:space="preserve">
    <value>Un problème est survenu pendant la mise à jour</value>
  </data>
  <data name="ErrorUpdateUser" xml:space="preserve">
    <value>Un problème est survenu pendant la mise à jour de l'utilisateur</value>
  </data>
  <data name="SuccessCommentaireInsert" xml:space="preserve">
    <value>Le commentaire à été enregistré avec succès</value>
  </data>
  <data name="SuccessDeleteSeanceFormule" xml:space="preserve">
    <value>La séance sur la formule a bien été supprimée</value>
  </data>
  <data name="SuccessDeleteUser" xml:space="preserve">
    <value>L'utilisateur a bien été supprimé</value>
  </data>
  <data name="SuccessInsertUser" xml:space="preserve">
    <value>L'utilisateur à bien été créer</value>
  </data>
  <data name="SuccessUpdateRule" xml:space="preserve">
    <value>La règle à bien été mise à jour</value>
  </data>
  <data name="SuccessUpdateUser" xml:space="preserve">
    <value>L'utilisateur à bien été mis à jour</value>
  </data>
  <data name="UtilisateurExiste" xml:space="preserve">
    <value>L'utilisateur existe déjà</value>
  </data>
  <data name="ErrorDelierConnexion" xml:space="preserve">
    <value>La connexion n'a pu être déliée</value>
  </data>
  <data name="ErrorInsertConnexion" xml:space="preserve">
    <value>L'ajout de la connexion n'a pu être créée</value>
  </data>
  <data name="ErrorInsertDataBase" xml:space="preserve">
    <value>La base de données n'a pu être créée</value>
  </data>
  <data name="ErrorInsertStructure" xml:space="preserve">
    <value>Une erreur est survenue pendant l'insertion de la nouvelle structure</value>
  </data>
  <data name="ErrorLierConnexion" xml:space="preserve">
    <value>La connexion n'a pu être déliée</value>
  </data>
  <data name="SuccessDelierConnexion" xml:space="preserve">
    <value>La connexion à été déliée avec succès</value>
  </data>
  <data name="SuccessInsertConnexion" xml:space="preserve">
    <value>L'ajout de la connexion à été créée avec succès</value>
  </data>
  <data name="SuccessInsertDataBase" xml:space="preserve">
    <value>La base de données à été créée avec succès</value>
  </data>
  <data name="SuccessInsertStructure" xml:space="preserve">
    <value>La nouvelle structure à bien été enregistrée</value>
  </data>
  <data name="SuccessLierConnexion" xml:space="preserve">
    <value>La connexion à été déliée avec succès</value>
  </data>
  <data name="lblBtnShift" xml:space="preserve">
    <value>Vous pouvez sélectionner plusieurs séances en appuyant sur le bouton SHIFT</value>
  </data>
  <data name="SelectAllManifs" xml:space="preserve">
    <value>Sélectionner toutes les manifestations</value>
  </data>
  <data name="SelectAllProducts" xml:space="preserve">
    <value>sélectionner tous les produits</value>
  </data>
  <data name="SelectAllTarifs" xml:space="preserve">
    <value>Sélectionner tous les tarifs</value>
  </data>
  <data name="TitleMaquettes" xml:space="preserve">
    <value>Maquettes</value>
  </data>
  <data name="TitleSelectProducts" xml:space="preserve">
    <value>Sélectionnez le ou les produit(s)</value>
  </data>
  <data name="lblTitleCreate" xml:space="preserve">
    <value>Création d'une règle de vente pour [manifName] le [sessionDate] </value>
  </data>
  <data name="lblTitleDateChoice" xml:space="preserve">
    <value>Choix des dates</value>
  </data>
  <data name="lblTitleEdit" xml:space="preserve">
    <value>Edition  pour [manifName]</value>
  </data>
  <data name="TooltipDeleteSeancesAbonnement" xml:space="preserve">
    <value>Supprimer la règle sur les séances de l'abonnement</value>
  </data>
  <data name="ErrorSelectManifs" xml:space="preserve">
    <value>Sélectionnez au moins une manifestation</value>
  </data>
  <data name="ExportExcel" xml:space="preserve">
    <value>Exporter sous Excel</value>
  </data>
  <data name="ExportPDF" xml:space="preserve">
    <value>Exporter en PDF</value>
  </data>
  <data name="TitleCashStatment" xml:space="preserve">
    <value>Etat de caisse</value>
  </data>
  <data name="KeyConfigNotFound" xml:space="preserve">
    <value>La clé dans le web.config n'existe pas</value>
  </data>
  <data name="SvgFileNotExist" xml:space="preserve">
    <value>le dossier svg n'existe pas (GeneralTerm fr)</value>
  </data>
  <data name="FileNotFoundSvg" xml:space="preserve">
    <value>Aucun fichier trouvé dans le dossier SVG</value>
  </data>
  <data name="SeanceDejaSelectionne" xml:space="preserve">
    <value>Séance déja sur ce produit  (GeneralTerm fr)</value>
  </data>
  <data name="InsertLogo" xml:space="preserve">
    <value>Le logo à bien été inséré  (GeneralTerm fr)</value>
  </data>
  <data name="PlayJob" xml:space="preserve">
    <value>Forcer la publication</value>
  </data>
  <data name="AccesSansBillet" xml:space="preserve">
    <value>Accès sans billet  (GeneralTerm fr)</value>
  </data>
  <data name="GlobalPanier" xml:space="preserve">
    <value>Global au panier  (GeneralTerm fr)</value>
  </data>
  <data name="TypeAcces" xml:space="preserve">
    <value>Type d'accès   (GeneralTerm fr)</value>
  </data>
  <data name="lblApercu" xml:space="preserve">
    <value>Apercu</value>
  </data>
  <data name="lblAucunCommentaire" xml:space="preserve">
    <value>Aucun commentaire paramétré</value>
  </data>
  <data name="lblConfirmPassword" xml:space="preserve">
    <value>Confirmer le mot de passe</value>
  </data>
  <data name="lblEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="lblEventGroups" xml:space="preserve">
    <value>Groupe(s) de manifestation(s) autorisé(s)</value>
  </data>
  <data name="lblLogin" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="NewUser" xml:space="preserve">
    <value>Nouvel Utilisateur</value>
  </data>
  <data name="lblPassword" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="lblProfil" xml:space="preserve">
    <value>Profil</value>
  </data>
  <data name="ChoixSeance" xml:space="preserve">
    <value>Choix de la séance et des Tarifs</value>
  </data>
  <data name="CreationCompte" xml:space="preserve">
    <value>Création d'un compte</value>
  </data>
  <data name="Identification" xml:space="preserve">
    <value>Identification</value>
  </data>
  <data name="ListeManifs" xml:space="preserve">
    <value>Liste des manifestations</value>
  </data>
  <data name="MonCompte" xml:space="preserve">
    <value>Mon compte</value>
  </data>
  <data name="Panier" xml:space="preserve">
    <value>Panier</value>
  </data>
  <data name="CldarSH" xml:space="preserve">
    <value>Choix de la séance par calendrier</value>
  </data>
  <data name="MaxBlockSz" xml:space="preserve">
    <value>Si 1, retourne le + grand bloc de places contigues</value>
  </data>
  <data name="ZapperEtag" xml:space="preserve">
    <value>Masquer choix de l'étage</value>
  </data>
  <data name="ZapperZES" xml:space="preserve">
    <value>si 1 zappe zone etag section</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>Se souvenir de moi</value>
  </data>
  <data name="DeleteRulesSelected" xml:space="preserve">
    <value>Supprimer les règles sur toutes les séances sélectionnées</value>
  </data>
  <data name="RegleAjouter" xml:space="preserve">
    <value>La règle de ventes ont bien été ajoutée pour la séance</value>
  </data>
  <data name="TarifAjouter" xml:space="preserve">
    <value>Le tarif à bien été ajouté</value>
  </data>
  <data name="Structures" xml:space="preserve">
    <value>Structures</value>
  </data>
  <data name="ActiverResumer" xml:space="preserve">
    <value>Activer le résumé</value>
  </data>
  <data name="AttentePaiement" xml:space="preserve">
    <value>Attente de paiement</value>
  </data>
  <data name="CancelPaiement" xml:space="preserve">
    <value>Annuler le paiement</value>
  </data>
  <data name="ChoixCategories" xml:space="preserve">
    <value>Choix des catégs</value>
  </data>
  <data name="ChoixFormule" xml:space="preserve">
    <value>Choix formule</value>
  </data>
  <data name="CommentaireFormule" xml:space="preserve">
    <value>Commentaire par formule</value>
  </data>
  <data name="CommentairePaiement" xml:space="preserve">
    <value>Commentaire de paiement</value>
  </data>
  <data name="ConfirmeProfil" xml:space="preserve">
    <value>Confirmation du profil</value>
  </data>
  <data name="CreationAbo" xml:space="preserve">
    <value>Création abo</value>
  </data>
  <data name="CreationProfil" xml:space="preserve">
    <value>Création du profil</value>
  </data>
  <data name="DetailCommande" xml:space="preserve">
    <value>Détail commande</value>
  </data>
  <data name="DetailComplet" xml:space="preserve">
    <value>Détail complet</value>
  </data>
  <data name="ReferenceUniqueBillet" xml:space="preserve">
    <value>Référence unique du billet</value>
  </data>
  <data name="DernierEtatPlace" xml:space="preserve">
    <value>Dernier état de la place sur la période</value>
  </data>
  <data name="IdFiliereVente" xml:space="preserve">
    <value>Id de la filière de vente</value>
  </data>
  <data name="NomFiliereVente" xml:space="preserve">
    <value>Nom de la filière de vente du billet</value>
  </data>
  <data name="IdManifestation" xml:space="preserve">
    <value>Id de la manifestation</value>
  </data>
  <data name="NomManifestation" xml:space="preserve">
    <value>Nom de la manifestation</value>
  </data>
  <data name="GroupeManifestations" xml:space="preserve">
    <value>Groupe de manifestations</value>
  </data>
  <data name="GenreManifestation" xml:space="preserve">
    <value>Genre de la manifestation</value>
  </data>
  <data name="SousGenreManifestation" xml:space="preserve">
    <value>Sous-Genre de la manifestation</value>
  </data>
  <data name="CibleManifestation" xml:space="preserve">
    <value>Cible de la manifestation</value>
  </data>
  <data name="IdSeance" xml:space="preserve">
    <value>Id de la séance</value>
  </data>
  <data name="DateDebutSeance" xml:space="preserve">
    <value>Date du début de la séance</value>
  </data>
  <data name="IdCategorie" xml:space="preserve">
    <value>Id de la catégorie</value>
  </data>
  <data name="NomCategorie" xml:space="preserve">
    <value>Nom de la catégorie</value>
  </data>
  <data name="IdTarif" xml:space="preserve">
    <value>Id du tarif</value>
  </data>
  <data name="NomTarif" xml:space="preserve">
    <value>Nom du tarif</value>
  </data>
  <data name="NumeroCommande" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="ModePaiement" xml:space="preserve">
    <value>Mode de paiement</value>
  </data>
  <data name="DateOperation" xml:space="preserve">
    <value>Date opération</value>
  </data>
  <data name="IdIdentite" xml:space="preserve">
    <value>Id identité</value>
  </data>
  <data name="NomIdentite" xml:space="preserve">
    <value>Nom identité</value>
  </data>
  <data name="PrenomIdentite" xml:space="preserve">
    <value>Prénom identité</value>
  </data>
  <data name="Civilite" xml:space="preserve">
    <value>Civilité</value>
  </data>
  <data name="CodePostal" xml:space="preserve">
    <value>Code Postal</value>
  </data>
  <data name="Ville" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="Pays" xml:space="preserve">
    <value>Pays</value>
  </data>
  <data name="DateNaissance" xml:space="preserve">
    <value>Date de Naissance</value>
  </data>
  <data name="FiliereCreationIdentite" xml:space="preserve">
    <value>Filière création identité</value>
  </data>
  <data name="TelephoneMobile" xml:space="preserve">
    <value>Téléphone mobile</value>
  </data>
  <data name="AdressePostale" xml:space="preserve">
    <value>Adresse postale</value>
  </data>
  <data name="OptIn" xml:space="preserve">
    <value>OPT-IN</value>
  </data>
  <data name="NumeroBillet" xml:space="preserve">
    <value>Numéro du billet</value>
  </data>
  <data name="ErrorPaiement" xml:space="preserve">
    <value>Erreur paiement</value>
  </data>
  <data name="EventList" xml:space="preserve">
    <value>Liste évênements</value>
  </data>
  <data name="ModificationTarif" xml:space="preserve">
    <value>Modification tarif</value>
  </data>
  <data name="Paiement" xml:space="preserve">
    <value>Paiement</value>
  </data>
  <data name="PanierEtIdentification" xml:space="preserve">
    <value>Panier et identitfication</value>
  </data>
  <data name="PopIdentification" xml:space="preserve">
    <value>Pop identification</value>
  </data>
  <data name="SessionsList" xml:space="preserve">
    <value>Liste des séances</value>
  </data>
  <data name="UpdateProfil" xml:space="preserve">
    <value>Mise à jour du profil</value>
  </data>
  <data name="ValidationAbo" xml:space="preserve">
    <value>Validation abonnement</value>
  </data>
  <data name="SessionId" xml:space="preserve">
    <value>Id séance</value>
  </data>
  <data name="TypeTarifId" xml:space="preserve">
    <value>Type tarif Id</value>
  </data>
  <data name="TypeTarifName" xml:space="preserve">
    <value>nom du tarif</value>
  </data>
</root>