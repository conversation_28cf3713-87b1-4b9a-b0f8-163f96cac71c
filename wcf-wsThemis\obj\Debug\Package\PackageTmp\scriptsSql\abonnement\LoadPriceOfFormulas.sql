﻿SELECT DISTINCT tt.type_tarif_id, type_tarif_nom, type_tarif_code, gp.formule_id as form_abon_id ,pref_affichage, gp.nb_max as nb_max, gp.nb_min as nb_min
FROM type_tarif tt 
INNER JOIN gestion_place gp on tt.type_tarif_id = gp.type_tarif_id
INNER JOIN abonnement_manifestation am on am.formule_id = gp.formule_id and am.manif_id = gp.manif_id and am.seance_id=gp.seance_id
AND isvalide=1 AND gp.formule_id IN ([ListeFormuleID])
order by pref_affichage,type_tarif_nom

