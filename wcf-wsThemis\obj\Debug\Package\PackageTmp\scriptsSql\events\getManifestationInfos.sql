﻿SELECT manifestation_nom as manif_nom, m.manifestation_id,
                        manifestation_code as Manif_code, manifestation_descrip as Manif_info_1,
                        manifestation_descrip2 as Manif_info_2,manifestation_descrip3  as Manif_info_3, 
                        manifestation_descrip4  as Manif_info_4,manifestation_descrip5 as Manif_info_5, 
                        ISNULL(manif_groupe_code,'') as GRPMAN_CODE, ISNULL(manif_groupe_nom,'') as GRPMAN_NOM,
                        ISNULL([producteur_nom],'') as NOM_PROD,ISNULL([producteur_code],'')  as CODE_PROD,LIC_PROD = CASE m.Licence_id
                        WHEN 1 THEN [num_licence1] WHEN 2 THEN [num_licence2] WHEN 3 THEN [num_licence3]
                        WHEN 4 THEN [num_licence4] WHEN 5 THEN [num_licence5]  ELSE [num_licence1] END

						,manifestation_descrip3 as TITRE_FILM_CNC
						,disci.nom as CLASSEMENT_CNC

                        FROM manifestation m 
                        LEFT OUTER JOIN producteur p on p.producteur_id= m.producteur_id
                        LEFT OUTER JOIN manifestation_groupe mg on mg.manif_groupe_id= m.manifestation_groupe_id
						LEFT OUTER JOIN Manif_Competences mcomp on mcomp.manifestation_id= m.manifestation_id
						LEFT OUTER JOIN discipline disci on mcomp.discipline_id= disci.id

                        WHERE m.MANIFESTATION_ID=[MANIF_ID]