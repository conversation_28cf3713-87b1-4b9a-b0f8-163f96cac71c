﻿DECLARE @pFormuleId int
DECLARE @SQLINSERT varchar(max)


if @pFormuleId = 0
begin

	set @SQLINSERT = 'INSERT INTO panier_entree  (panier_id, entree_id, manif_id, seance_id, categ_id, type_tarif_id,  rang, siege, 
	 manif_nom, seance_description, categ_nom, type_tarif_nom, montant, frais, vts_id,type_envoi,maquette_id,type_envoi_id) 
	 VALUES('[pBasketId]','+@pEntreeId+','+@pEventId+','+@pSessionId+','+@pCategId+','+@pTypeTaridId+','+@pRang+','+@pSiege+','+@eventName+','+@pSessionStartDate+','+@pCateName+','+@pTypeTarifNom+','+@pAmount+','+@pFees+',0,'+@pTypeenvoi+','+@pMaquetteId+','+@pTypeEnvoiId+');'

 END

 ELSE
 BEGIN
 
	set @SQLINSERT = 'INSERT INTO panier_entree_abo(panier_id, entree_id, manif_id, seance_id, categ_id, type_tarif_id, rang, siege, manif_nom, 
	seance_description, categ_nom, type_tarif_nom, montant, frais, vts_id,type_envoi,maquette_id,type_envoi_id, formule_id,formule_nom)  
	VALUES('+@pBasketId+','+@pEntreeId+','+@pEventId+','+@pSessionId+','+@pCategId+','+@pTypeTaridId+','+@pRang+','+@pSiege+','+@eventName+','
	+@pSessionStartDate+','+@pCateName+','+@pTypeTarifNom+','+@pAmount+','+@pFees+',0,'+@pTypeenvoi+','+@pMaquetteId+','+@pTypeEnvoiId+','+@pFormuleId+','+@pFormuleName+');'

 END

exec(@SQLINSERT)