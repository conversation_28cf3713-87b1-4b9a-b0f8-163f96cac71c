﻿declare @LangCode varchar(2) = '[langCode]'	

declare @langId INT = 0
select @langId = langue_id from langue where langue_code = @LangCode


SELECT  distinct ISNULL(tl.lieu_nom, l.lieu_nom) as placeName, l.lieu_id as placeId, ISNULL(tmg.manif_groupe_nom,  mg.manif_groupe_nom) as eventGroupName, ISNULL(tmg.manif_groupe_id, mg.manif_groupe_id) as eventGroupId,
ISNULL(tm.manifestation_nom, m.manifestation_nom) as eventName, m.manifestation_id as eventId, s.seance_date_deb as sessionStartDate,s.seance_id as sessionId
,'GP' = CASE WHEN gp.manif_id is NULL THEN 'NON' ELSE 'OUI' END,  
'manifsislock' = CASE 
				WHEN gpm.islock is NULL THEN 'false' 
				WHEN gpm.islock = 0 THEN 'false' 
				ELSE 'true' END, 
'seancesislock' = CASE 
				WHEN gps.islock is NULL THEN 'false' 
				WHEN gps.islock =0 THEN 'false' 
				ELSE 'true' END,
	ISNULL(tc.categ_nom, c.categ_nom) as categNom, c.categ_id as categId, c.pref_affichage as categPrefAffichage
	, 0 as is_distanciation, 0 as nb_distanciation_horizontal, 0 as nb_distanciation_vertical
	  INTO #tmp_result
 FROM manifestation m
 INNER JOIN manifestation_groupe mg ON m.manifestation_groupe_id = mg.manif_groupe_id 
INNER JOIN seance s on s.manifestation_id = m.manifestation_id
INNER JOIN lieu l ON l.lieu_id = s.lieu_id
INNER JOIN categorie c on c.lieu_id = l.lieu_id
LEFT OUTER JOIN GP_seance gps on  gps.seance_id = s.seance_Id
LEFT OUTER JOIN GP_manifestation gpm on gpm.manifestation_id = m.manifestation_id
INNER JOIN gestion_place gp on gp.manif_id = m.manifestation_id and gp.categ_id=c.categ_id
INNER JOIN offre_gestion_place ogp on ogp.gestion_place_id = gp.gestion_place_id

LEFT OUTER JOIN traduction_manifestation tm ON tm.manifestation_id = m.manifestation_id and tm.langue_id = @langId
LEFT OUTER JOIN traduction_manifestation_groupe tmg ON tmg.manif_groupe_id = mg.manif_groupe_id and tmg.langue_id = @langId
LEFT OUTER JOIN traduction_categorie tc ON tc.categ_id = c.categ_id and tc.langue_id = @langId
LEFT OUTER JOIN  traduction_lieu tl ON tl.lieu_id = l.lieu_id and tl.langue_id = @langId

WHERE offre_id=[OffreID]
AND isvalide=1
AND dispo>0
AND seance_date_deb > getdate() AND seance_cloturer<>'O' AND seance_masquer<>'O' AND seance_verrouiller<>'O'  AND OPTIONS <>'O'
ORDER BY GP desc,placeName,eventGroupName,eventName,sessionStartDate;



declare @currenteventid int
declare curs_events cursor scroll for 
	select distinct eventId from  #tmp_result 
open curs_events
fetch next from curs_events into @currenteventid
while @@FETCH_STATUS=0
BEGIN
		
	DECLARE @isDistancation INT, 
	@nb_distanciation_horizontal INT, 
	@nb_distanciation_vertical INT;

	select @isDistancation = count(*) from manifestation m	inner join  proprietes_of_manifs ppm on ppm.manifestation_id = m.manifestation_id
	inner join proprietes_references_of_manifs ppref on ppref.propriete_ref_id = ppm.propriete_ref_id
	where upper(code)='DISTCV' and m.manifestation_id = @currenteventid and valeur=1
	

	select @nb_distanciation_horizontal = ISNULL((select preference_valeur FROM structure_prefs WHERE preference_cle='DISTANCIATION_HORIZ'),0)
	select @nb_distanciation_vertical = ISNULL((select preference_valeur FROM structure_prefs WHERE preference_cle='DISTANCIATION_VERTI'),0) 


	update #tmp_result set is_distanciation =@isDistancation, 
		nb_distanciation_horizontal= @nb_distanciation_horizontal,  
		nb_distanciation_vertical = @nb_distanciation_vertical 
	WHERE eventid = @currenteventid

	fetch next from curs_events into @currenteventid
END
CLOSE curs_events
DEALLOCATE curs_events

select * from #tmp_result
order by eventName, SessionStartDate

drop table #tmp_result 
