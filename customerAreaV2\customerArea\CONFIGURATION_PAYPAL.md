# Configuration PayPal pour CustomerArea

## 🎯 Objectif

Ce document explique comment configurer l'authentification PayPal pour permettre aux utilisateurs de se connecter avec leur compte PayPal.

---

## ⚙️ Configuration requise

### 1. Fichier `config.ini.xml`

**Emplacement** : `D:\customerfiles\DEV\[idstructure]\CONFIGSERVER\config.ini.xml`

Ajoutez les lignes suivantes dans votre fichier `config.ini.xml` :

```xml
<PAYPAL_CONNECTUSERNAME>AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw</PAYPAL_CONNECTUSERNAME>
<PAYPAL_CONNECTPASSWORD>EO70VAO37ZELyJxtR7rR-lrFJVTTgRzWQsxtt1NAEcuj8CSKWP91c0n4ALcB0MXYnBjpXCXUsd4BnNDJ</PAYPAL_CONNECTPASSWORD>
```

**Note** : Ces identifiants sont pour l'environnement **Sandbox** (test). Pour la production, vous devrez créer une nouvelle application PayPal et utiliser les identifiants de production.

---

### 2. Fichier `appsettings.json`

**Emplacement** : `D:\customerfiles\DEV\[idstructure]\Customer\APPSETINGS\appsettings.json`

Ajoutez ou modifiez la section `login` dans votre fichier `appsettings.json` :

```json
{
  "global": {
    "guest": false,
    "light": false,
    "passwordRules": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$"
  },
  "login": {
    "facebookConnect": false,
    "facebook": {
      "applicationId": "",
      "urlJsLang": "//connect.facebook.net/fr_FR/sdk.js"
    },
    "payPalConnect": true,
    "payPal": {
      "appid": "AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw",
      "returnurl": "https://dev.themisweb.fr/customer/_loginPayPal.aspx?idstructure=[structureid]"
    },
    "unidyConnect": false,
    "unidy": {
      "clientId": "",
      "redirectUrl": ""
    }
  }
}
```

**Points importants** :
- `payPalConnect: true` active le bouton PayPal sur la page de connexion
- `appid` doit contenir votre Client ID PayPal
- `returnurl` doit pointer vers votre domaine (remplacez `dev.themisweb.fr` par votre domaine)
- `[structureid]` sera automatiquement remplacé par l'ID de la structure

---

### 3. Fichier `Web.config`

Les URLs PayPal sont déjà configurées dans le `Web.config` :

```xml
<add key="paypalconnect_urltokenservice" value="https://api.sandbox.paypal.com/v1/identity/openidconnect/tokenservice" />
<add key="paypalconnect_urluserinfo" value="https://api.sandbox.paypal.com/v1/identity/openidconnect/userinfo/?schema=openid" />
```

**Pour la production**, changez les URLs :
```xml
<add key="paypalconnect_urltokenservice" value="https://api.paypal.com/v1/identity/openidconnect/tokenservice" />
<add key="paypalconnect_urluserinfo" value="https://api.paypal.com/v1/identity/openidconnect/userinfo/?schema=openid" />
```

---

## 🔧 Configuration PayPal Developer

### 1. Créer une application PayPal (Sandbox)

1. Allez sur https://developer.paypal.com/dashboard/
2. Connectez-vous avec votre compte PayPal
3. Cliquez sur **"My Apps & Credentials"**
4. Cliquez sur **"Create App"** (dans la section Sandbox)
5. Donnez un nom à votre application (ex: "CustomerArea Authentication")
6. Cliquez sur **"Create App"**

### 2. Configurer l'application

1. Dans la page de votre application, activez **"Log In with PayPal"**
2. Configurez les **Return URLs** :
   ```
   https://dev.themisweb.fr/customer/_loginPayPal.aspx?idstructure=0224
   ```
   (Remplacez par votre domaine et votre ID de structure)

3. Sélectionnez les **scopes** (permissions) :
   - ✅ `openid`
   - ✅ `email`
   - ✅ `profile`
   - ✅ `address`

4. Cliquez sur **"Save"**

### 3. Récupérer les identifiants

- **Client ID** : Visible directement sur la page de l'application
- **Secret** : Cliquez sur "Show" pour l'afficher

**Note** : Les identifiants fournis dans ce document sont déjà configurés pour le Sandbox.

---

## 🧪 Tester l'authentification

### 1. Créer un compte de test Sandbox

1. Allez sur https://developer.paypal.com/dashboard/
2. Cliquez sur **"Sandbox"** > **"Accounts"**
3. Cliquez sur **"Create Account"**
4. Sélectionnez **"Personal"** (compte utilisateur)
5. Remplissez les informations
6. Cliquez sur **"Create Account"**
7. Notez l'email et le mot de passe

### 2. Tester la connexion

1. Accédez à votre page de connexion : `https://dev.themisweb.fr/customer/login.aspx?idstructure=0224`
2. Vous devriez voir le bouton **"Se connecter avec PayPal"**
3. Cliquez sur le bouton
4. Une popup s'ouvre vers PayPal
5. Connectez-vous avec le compte de test Sandbox
6. Autorisez l'application
7. Vous êtes redirigé et connecté sur votre site

---

## 🔍 Vérification

### Le bouton PayPal apparaît-il ?

Si le bouton n'apparaît pas, vérifiez :

1. **appsettings.json** :
   - Le fichier existe bien
   - `payPalConnect: true`
   - La syntaxe JSON est correcte

2. **Cache** :
   - Videz le cache du navigateur (Ctrl + F5)
   - Redémarrez l'application pool IIS

3. **Console du navigateur** :
   - Ouvrez la console (F12)
   - Vérifiez s'il y a des erreurs JavaScript

### La connexion fonctionne-t-elle ?

Si la connexion ne fonctionne pas, vérifiez :

1. **Logs** :
   - Chemin : `D:\LOGS\THEMIS\DEV\Customer\`
   - Recherchez les erreurs liées à `ConnectPayPal`

2. **Configuration** :
   - Client ID correct dans `appsettings.json` et `config.ini.xml`
   - Client Secret correct dans `config.ini.xml`
   - URL de retour correspond exactement à celle configurée dans PayPal

3. **Console du navigateur** :
   - Vérifiez les erreurs dans l'onglet "Console"
   - Vérifiez les requêtes dans l'onglet "Network"

---

## 🚀 Passage en production

### 1. Créer une application PayPal Production

1. Allez sur https://www.paypal.com/businessmanage/
2. Créez une nouvelle application (même processus que pour Sandbox)
3. Récupérez le nouveau Client ID et Secret

### 2. Mettre à jour la configuration

**config.ini.xml** :
```xml
<PAYPAL_CONNECTUSERNAME>VOTRE_CLIENT_ID_PRODUCTION</PAYPAL_CONNECTUSERNAME>
<PAYPAL_CONNECTPASSWORD>VOTRE_CLIENT_SECRET_PRODUCTION</PAYPAL_CONNECTPASSWORD>
```

**appsettings.json** :
```json
{
  "login": {
    "payPal": {
      "appid": "VOTRE_CLIENT_ID_PRODUCTION",
      "returnurl": "https://www.votre-domaine.com/customer/_loginPayPal.aspx?idstructure=[structureid]"
    }
  }
}
```

**Web.config** :
```xml
<add key="paypalconnect_urltokenservice" value="https://api.paypal.com/v1/identity/openidconnect/tokenservice" />
<add key="paypalconnect_urluserinfo" value="https://api.paypal.com/v1/identity/openidconnect/userinfo/?schema=openid" />
```

**Login.aspx** (ligne 245) :
```javascript
// Remplacer
var authUrl = "https://www.sandbox.paypal.com/signin" +
// Par
var authUrl = "https://www.paypal.com/signin" +
```

---

## 📚 Documentation complète

Pour plus de détails, consultez les documents à la racine du projet :
- [README_PAYPAL_AUTHENTICATION.md](../README_PAYPAL_AUTHENTICATION.md) - Documentation principale
- [QUICKSTART_PAYPAL.md](../QUICKSTART_PAYPAL.md) - Guide de démarrage rapide
- [GUIDE_AUTHENTIFICATION_PAYPAL.md](../GUIDE_AUTHENTIFICATION_PAYPAL.md) - Guide complet
- [TROUBLESHOOTING_PAYPAL.md](../TROUBLESHOOTING_PAYPAL.md) - Guide de dépannage
- [FLUX_TECHNIQUE_PAYPAL.md](../FLUX_TECHNIQUE_PAYPAL.md) - Flux technique détaillé

---

## 🔐 Sécurité

**⚠️ IMPORTANT** :
- Ne partagez JAMAIS votre Client Secret publiquement
- Ne commitez JAMAIS les identifiants dans Git
- Utilisez toujours HTTPS en production
- Les identifiants Sandbox ne fonctionnent PAS en production

---

**Dernière mise à jour** : 2025-10-07

