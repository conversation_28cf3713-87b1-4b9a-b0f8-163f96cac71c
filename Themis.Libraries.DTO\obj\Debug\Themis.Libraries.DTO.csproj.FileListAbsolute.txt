D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\Themis.Libraries.DTO.dll.config
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\Themis.Libraries.DTO.dll
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\Themis.Libraries.DTO.pdb
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\System.IO.Pipelines.dll
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\System.Text.Encodings.Web.dll
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\System.Text.Json.dll
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\System.IO.Pipelines.xml
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\System.Text.Encodings.Web.xml
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\bin\Debug\System.Text.Json.xml
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\obj\Debug\Themis.Libraries.DTO.csproj.AssemblyReference.cache
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\obj\Debug\Themis.Libraries.DTO.csproj.CoreCompileInputs.cache
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\obj\Debug\Themis.L.DCE4E288.Up2Date
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\obj\Debug\Themis.Libraries.DTO.dll
D:\WORK\VIEUXPROJETS\Themis.Libraries.DTO\obj\Debug\Themis.Libraries.DTO.pdb
