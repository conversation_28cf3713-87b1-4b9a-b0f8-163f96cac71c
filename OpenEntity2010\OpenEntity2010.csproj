﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{8F11805B-FC68-4EBC-BB35-D458FF8B9312}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>OpenEntity2010</RootNamespace>
    <AssemblyName>OpenEntity2010</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>%24/RobWebNet/RodWebSites/WebApplicationGroupe/WebApplicationGroupe/OpenEntity</SccProjectName>
    <SccLocalPath>.</SccLocalPath>
    <SccAuxPath>http://v-dev-tfs:8080/tfs/appscollection</SccAuxPath>
    <SccProvider>{4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common.cs" />
    <Compile Include="entities\AdhesionCatalogEntity.cs" />
    <Compile Include="entities\BoutiqueEntity.cs" />
    <Compile Include="entities\BoutiqueFamilleEntity.cs" />
    <Compile Include="entities\BoutiqueSousFamilleEntity.cs" />
    <Compile Include="entities\CategoryEntity.cs" />
    <Compile Include="entities\ChampsTicketAtHome.cs" />
    <Compile Include="entities\ContrainteEntity.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="entities\DemandPasswordResetEntity.cs" />
    <Compile Include="entities\Enums\ModePaiementTypeEnum.cs" />
    <Compile Include="entities\FormuleAbonnementEntity.cs" />
    <Compile Include="entities\GestionPlaceEntity.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="entities\GroupeFormuleEntity.cs" />
    <Compile Include="entities\LanguagesEntity.cs" />
    <Compile Include="entities\LigneGrilleTarifEntity.cs" />
    <Compile Include="entities\MajBddEntity.cs" />
    <Compile Include="entities\OCategoryEntity.cs" />
    <Compile Include="entities\CompteClientLineEntity.cs" />
    <Compile Include="entities\ConsomateurEntity.cs" />
    <Compile Include="entities\CommandeEntity.cs" />
    <Compile Include="entities\CommandeLigneEntity.cs" />
    <Compile Include="entities\DepotVenteLineEntity.cs" />
    <Compile Include="entities\DossierEntity.cs" />
    <Compile Include="entities\EventsGroupsEntity.cs" />
    <Compile Include="entities\IdentityEntity.cs" />
    <Compile Include="entities\MaquettesEntity.cs" />
    <Compile Include="entities\OEventEntity.cs" />
    <Compile Include="entities\OffreEntity.cs" />
    <Compile Include="entities\OLieuEntity.cs" />
    <Compile Include="entities\OperateurEntity.cs" />
    <Compile Include="entities\OProductEntity.cs" />
    <Compile Include="entities\ProductEntity.cs" />
    <Compile Include="entities\ProductInternetEntity.cs" />
    <Compile Include="entities\PropertiesEventsEntity.cs" />
    <Compile Include="entities\ProprietesRefOfEventsEntity.cs" />
    <Compile Include="entities\Reserve.cs" />
    <Compile Include="entities\OSessionEntity.cs" />
    <Compile Include="entities\RightsEntity.cs" />
    <Compile Include="entities\StructurePrefsEntity.cs" />
    <Compile Include="entities\TraductionGpEventDTO.cs" />
    <Compile Include="entities\TypeEnvoiEntity.cs" />
    <Compile Include="entities\TypeTarifEntity.cs" />
    <Compile Include="Manager\BoutiqueFamilleManager.cs" />
    <Compile Include="Manager\ContrainteManager.cs" />
    <Compile Include="Manager\DemandPasswordResetManager.cs" />
    <Compile Include="Manager\EventManager.cs" />
    <Compile Include="Manager\EventsGroupsManager.cs" />
    <Compile Include="Manager\GestionPlaceManager.cs" />
    <Compile Include="Manager\LieuManager.cs" />
    <Compile Include="Manager\MajBddManager.cs" />
    <Compile Include="Manager\ModePaiementManager.cs" />
    <Compile Include="Manager\OffreContrainteManager.cs" />
    <Compile Include="Manager\OffreManager.cs" />
    <Compile Include="Manager\OperateurPaiementManager.cs" />
    <Compile Include="Manager\ProductInternetManager.cs" />
    <Compile Include="Manager\ProfilAcheteurManager.cs" />
    <Compile Include="entities\ModePaiementEntity.cs" />
    <Compile Include="entities\ProfilAcheteurEntity.cs" />
    <Compile Include="Manager\PropertiesEventsManager.cs" />
    <Compile Include="Manager\ReserveManager.cs" />
    <Compile Include="Manager\StructurePrefsManager.cs" />
    <Compile Include="Manager\TypeEnvoiManager.cs" />
    <Compile Include="Manager\WidgetsManager.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="entities\SaleSettingsConsumerProfilEntity.cs" />
    <Compile Include="entities\SaleSettingsEntity.cs" />
    <Compile Include="entities\SaleSettingsOfferEntity.cs" />
    <Compile Include="entities\SectionEntity.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\utilitaires\utilitaires2010.csproj">
      <Project>{43eb2f59-7b43-4d1e-a62c-16c06e39e756}</Project>
      <Name>utilitaires2010</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>