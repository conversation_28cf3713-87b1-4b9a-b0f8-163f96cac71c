﻿
-- Grille de T sans decomposition Etage,section, etc : juste categ / tarif / dispo (de gestion place) !
--> plus rapide

declare @offreid int
set @offreid=[OffreID]

declare @manifid int
set @manifid=[EventID]

declare @langue_id int
set @langue_id=0


 	DECLARE @const_placementlibre int; set @const_placementlibre =32;
	 	DECLARE @session_id int; set @session_id=0
		declare @identiteId int; set @identiteId=0


	CREATE TABLE #myhisto (manifid int, seanceid int, categid int, typetarifid int, nbr int)

 	DECLARE @sqlreq varchar(MAX)
 	CREATE TABLE #tableResultGrilleTarif (
 	EventID int,
 	SessionID int, 
	formulaId int,
 	RailingPriceID int,	AmountExceptTax decimal(18, 10)
 	,Charge	decimal(18, 10),
 	 Tax	decimal(18, 10),
 	 Discount	decimal(18, 10),
 	 Commission	decimal(18, 10),
 	 TotalTax decimal(18, 10),
 	 TotalAmount decimal(18, 10),	
 	 TicketAmount	decimal(18, 10),
	 CategoryID int,
 	 CategoryName	varchar(50),
 	 CategoryCode varchar(50),
 	 PriceID	int,
 	 PriceName	varchar(50),

 	 NbSeatMin int,
 	 NbSeatMax int,
 	 BookingType int,
 	 NotNumbered int, 	 
 	 GpId int,
 	 DisplaySequence int,
	 NbSeat int);

 	CREATE TABLE #tableResultSession (gestion_place_id int, eventId int, sessionId int, nbseatmin int, nbseatmax int);
 	
	CREATE TABLE #mymultiplicator (type_tarif_id int, mult int)

    CREATE TABLE #mygp (gestion_place_id int)



			DECLARE @listgp TABLE(
 		 			manif_id int,
					gestion_place_id int,
					isforpa int)

	 		INSERT INTO @listgp 
		 			select 
						manif_id, gp.gestion_place_id ,1 as isForpa 		 				
 						from gestion_place gp,
 						offre_gestion_place ogp ,offre o
 						where ogp.offre_id = o.offre_id
 						and o.offre_id=@offreid
 						and ogp.gestion_place_id=gp.gestion_place_id
 						and gp.isvalide=1
 						and manif_id=@manifid
 					union
					select manif_id, gp.gestion_place_id ,0 as isForpa 
 						from gestion_place gp 			 				
 						where gp.isvalide=1 and iscontrainteidentite=0
 						and manif_id=@manifid;
 				
 				insert into #mygp
 				select gestion_place_id  from @listgp
 				


DECLARE EventCursor CURSOR SCROLL FOR
 		SELECT DISTINCT (manif_id) from #mygp, gestion_place gp WHERE gp.gestion_place_id=#mygp.gestion_place_id

 		SET @sqlreq ='';	
 		DECLARE @strTraduction varchar(50)
 		SET @strTraduction='';
 		IF (@langue_id!=0)
 		BEGIN
 			set @strTraduction='traduction_';
 		END
 		DECLARE @manif_id_encours INT
 		
 		OPEN EventCursor 
 		FETCH NEXT FROM EventCursor INTO @manif_id_encours
 		WHILE @@FETCH_STATUS = 0
 		BEGIN
 			print @manif_id_encours
 			declare @zapperEtage INT
 			select @zapperEtage = count(*) from proprietes_of_manifs pm, proprietes_references_of_manifs pref
				where pref.propriete_ref_id=pm.propriete_ref_id and valeur=1
				and pm.manifestation_id=@manif_id_encours
				and pref.code='ZapperEtag'


 			declare @zapperZEC INT
 			select @zapperZEC = count(*) from proprietes_of_manifs pm, proprietes_references_of_manifs pref
				where pref.propriete_ref_id=pm.propriete_ref_id and valeur=1
				and pm.manifestation_id=@manif_id_encours
				and pref.code='ZapperZES'
			print 'zapperZEC=' + convert(varchar,@zapperZEC)								
			print 'zapperEtage=' + convert(varchar,@zapperEtage)
 			--SELECT @zapperEtage 
 			--set @sqlreq = @sqlreq + 
 			SET @sqlreq = 
 			'INSERT INTO #tableResultGrilleTarif		
 			SELECT ' + CHAR(13) +
			'EventID, SessionId, formulaId, RailingPriceID, AmountExceptTax, Charge, Tax, discount, Commission, TotalTax, TotalAmount, TicketAmount, CategoryID, CategoryName, CategoryCode, PriceID, PriceName 
, NbSeatMin, NbSeatMax,BookingType,NotNumbered,GpId,DisplaySequence, dispo' + CHAR(13) +
 'FROM (SELECT ' +
 'vgp.manif_id as EventID' +
 ',vgp.seance_id as SessionID'  + CHAR(13) +
 ',vgp.formule_id as formulaId ' + CHAR(13) +
 ',vts.vts_id as RailingPriceID'  + CHAR(13) +
 ',vts.vts_grille1 as AmountExceptTax'  + CHAR(13) +
 ',vts.vts_grille2 as Charge '+ CHAR(13) +
 ',Tax= case when modecol4=''TAXE'' then vts.vts_grille4 else 0 END + case  when modecol5=''TAXE'' then vts.vts_grille5 else 0 END + case  when modecol6=''TAXE'' then vts.vts_grille6 else 0 END + case  when modecol7=''TAXE'' then vts.vts_grille7 else 0 END + case  when modecol8=''TAXE'' then vts.vts_grille8 else 0 END + case  when modecol9=''TAXE'' then vts.vts_grille9 else 0 END + case  when modecol10=''TAXE'' then vts.vts_grille10 else 0 END'			+ CHAR(13) +
 ',Discount= case when modecol4=''REMISE'' then vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then vts.vts_grille10 else 0 END' + CHAR(13) +
 ',Commission= case when modecol4=''COMMISSION'' then vts.vts_grille4 else 0 END + case  when modecol5=''COMMISSION'' then vts.vts_grille5 else 0 END + case  when modecol6=''COMMISSION'' then vts.vts_grille6 else 0 END + case  when modecol7=''COMMISSION'' then vts.vts_grille7 else 0 END + case  when modecol8=''COMMISSION'' then vts.vts_grille8 else 0 END + case  when modecol9=''COMMISSION'' then vts.vts_grille9 else 0 END + case  when modecol10=''COMMISSION'' then vts.vts_grille10 else 0 END'+ CHAR(13) +
 ',TotalTax= case  when modecol4=''REMISE''  then - vts.vts_grille4 when modecol4=''TAXE'' or modecol4=''COMMISSION'' then  vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then - vts.vts_grille5 when modecol5=''TAXE'' or modecol5=''COMMISSION'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then - vts.vts_grille6 when modecol6=''TAXE'' or modecol6=''COMMISSION'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then - vts.vts_grille7 when modecol7=''TAXE'' or modecol7=''COMMISSION'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then - vts.vts_grille8 when modecol8=''TAXE'' or modecol8=''COMMISSION'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then - vts.vts_grille9 when modecol9=''TAXE''or modecol9=''COMMISSION'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then - vts.vts_grille10 when modecol10=''TAXE'' or modecol10=''COMMISSION'' then  vts.vts_grille10 else 0 END	' + CHAR(13) +
 ',TotalAmount= vts.vts_grille1+vts.vts_grille2+case when modecol4=''REMISE'' then - vts.vts_grille4  when modecol4=''TAXE'' or modecol4=''COMMISSION'' then vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then - vts.vts_grille5 when modecol5=''TAXE'' or modecol5=''COMMISSION'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then - vts.vts_grille6 when modecol6=''TAXE'' or modecol6=''COMMISSION'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then - vts.vts_grille7 when modecol7=''TAXE'' or modecol7=''COMMISSION'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then - vts.vts_grille8 when modecol8=''TAXE'' or modecol8=''COMMISSION'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then - vts.vts_grille9 when modecol9=''TAXE''or modecol9=''COMMISSION'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then - vts.vts_grille10 when modecol10=''TAXE'' or modecol10=''COMMISSION'' then vts.vts_grille10 else 0 END	 ' + CHAR(13) +
 ',TicketAmount=vts.vts_grille3' + CHAR(13)



SET @sqlreq = @sqlreq + ',vts.categ_id as CategoryID' + CHAR(13) +
 ',c.categ_nom as CategoryName' + CHAR(13) +
 ',c.categ_code as CategoryCode' + CHAR(13) +
 ',vts.type_tarif_id as PriceID' + CHAR(13) +
 ',tt.type_tarif_nom as PriceName' + CHAR(13) +
 ',vgp.nb_min as NbSeatMin,'  + CHAR(13);

		set @sqlreq = @sqlreq + CHAR(9) + ' vgp.nb_max  as NbSeatMax'+ CHAR(13);

	set @sqlreq = @sqlreq +', BookingType = CASE WHEN vgp.prise_place is null THEN 0 ELSE vgp.prise_place END ' +
 ',NotNumbered = CASE WHEN (vgp.prise_place & ' + convert(varchar,@const_placementlibre) + ')=0 THEN 0 ELSE 1 END ' +
 ',vgp.gestion_place_id as GpId ' +
 ',tt.pref_affichage as DisplaySequence,dispo  ' + char(13) +
 			'FROM valeur_tarif_stock' + convert(varchar,@manif_id_encours) + ' vts '  +
 			'INNER JOIN (' + char(13) +						
 			'SELECT gp.manif_id,gp.gestion_place_id, gp.seance_id, gp.formule_id, gp.type_tarif_id, gp.categ_id ,gp.nb_min,gp.nb_max,gp.prise_place, gp.dispo as dispo ' + char(13) +		 
	 
 			'FROM  gestion_place gp ' + CHAR(13) +

 		 'INNER JOIN valeur_tarif_stock' + convert(varchar,@manif_id_encours) + ' vts ON vts.type_tarif_id = gp.type_tarif_id  ' + char(13) +
 		 'INNER JOIN #mygp ON #mygp.gestion_place_id = gp.gestion_place_id ' + CHAR(13) +

		  		 'INNER JOIN categorie c on c.categ_id = vts.categ_id ' + CHAR(13);



			set @sqlreq = @sqlreq + char(9) + ' WHERE vts_v =(SELECT MAX(vts_v) FROM valeur_tarif_stock' + convert(varchar,@manif_id_encours) + ' vts2' + CHAR(13)
			set @sqlreq = @sqlreq + char(9) + 'WHERE vts2.tarif_logique_id=vts.tarif_logique_id' + CHAR(13)
			set @sqlreq = @sqlreq + char(9) + 'and vts2.seance_id=vts.seance_id' + CHAR(13)
			set @sqlreq = @sqlreq + char(9) + 'and vts2.categ_id= vts.categ_id' + CHAR(13)
			set @sqlreq = @sqlreq + char(9) + 'and vts2.type_tarif_id= vts.type_tarif_id) and vts.vts_grille1>=0'+ CHAR(13)


			set @sqlreq = @sqlreq +  CHAR(13) + char(9) + 'GROUP BY gp.manif_id,gp.gestion_place_id,gp.seance_id,gp.formule_id,gp.type_tarif_id,gp.categ_id,gp.nb_min,gp.nb_max,gp.prise_place,dispo ' +
            

                                   ') vgp'+ CHAR(13)
 			+' ON vts.type_tarif_id =  vgp.type_tarif_id /*and vts.categ_id = vgp.categ_id */ AND vts.seance_id = vgp.seance_id  ' +                            
 			'INNER JOIN structure st ON st.structure_id >0' +   char(13) +
 			'INNER JOIN ' + @strTraduction + 'categorie c ON c.categ_id=vts.categ_id' + char(13) +
 			'INNER JOIN ' + @strTraduction + 'type_tarif tt ON tt.type_tarif_id=vts.type_tarif_id' + char(13);


 		 
 		 SET @sqlreq = @sqlreq + 'WHERE vts_v =(SELECT MAX(vts_v) FROM valeur_tarif_stock' + convert(varchar,@manif_id_encours) + ' vts2' + char(13) +
 								CHAR(9) + 'WHERE vts2.tarif_logique_id=vts.tarif_logique_id' + char(13)
                                 + CHAR(9) +'and vts2.seance_id=vts.seance_id' + char(13)
                                 + CHAR(9) +'and vts2.categ_id= vts.categ_id' + char(13)
                                 + CHAR(9) +'and vts2.type_tarif_id= vts.type_tarif_id)'
 		   + ' AND vts.vts_grille1>=0 ' + char(13) 




 		   set @sqlreq = @sqlreq + ') ssr WHERE 1=1 ' + char(13);

 		   	if (@session_id>0)
	begin
		set @sqlreq = @sqlreq + ' AND SessionID=' + convert(varchar,@session_id) + char(13)
	end

 		    set @sqlreq = @sqlreq + ' group by EventID, SessionId, formulaId, RailingPriceID, AmountExceptTax, Charge, Tax, discount, Commission, TotalTax, TotalAmount, TicketAmount, CategoryID, CategoryName, CategoryCode, PriceID, PriceName , 
			 DisplaySequence, nbSeatMin, nbSeatMax, bookingType,NotNumbered,GpId,DisplaySequence,dispo';
 		   set @sqlreq = @sqlreq +  ' ORDER BY CategoryName,DisplaySequence;' + CHAR(13) + CHAR(13) 

		   
 		   
 			print @sqlreq
 		exec (@sqlreq)
 		FETCH NEXT FROM EventCursor INTO @manif_id_encours
 	END
 	CLOSE EventCursor
 	DEALLOCATE EventCursor
 	
	drop table #mymultiplicator

	--update #tableResultGrilleTarif set PriceName=PriceName + ' SUPER PROMO !!!' where priceid =4660083

 	select * from #tableResultGrilleTarif --where priceid <>4660083
	 	select * from #tableResultSession


		--drop table #mymultiplicator
		drop table #myhisto

	drop table #tableResultGrilleTarif
	drop table #tableResultSession
	
		drop table #mygp

