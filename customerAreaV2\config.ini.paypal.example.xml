<?xml version="1.0" encoding="utf-8"?>
<!-- 
  Exemple de configuration PayPal pour config.ini.xml
  
  Emplacement du fichier :
  - DEV: D:\customerfiles\DEV\[idstructure]\CONFIGSERVER\config.ini.xml
  - TEST: \\Srv-paiement64\customerfiles\TEST\[idstructure]\CONFIGSERVER\config.ini.xml
  - PROD: \\Srv-paiement64\customerfiles\PROD\[idstructure]\CONFIGSERVER\config.ini.xml
  
  Instructions :
  1. Copiez les lignes ci-dessous dans votre fichier config.ini.xml existant
  2. Remplacez les valeurs par vos identifiants PayPal
  3. Pour Sandbox (test) : utilisez vos identifiants Sandbox
  4. Pour Production : utilisez vos identifiants Production
-->

<configuration>
  <!-- Autres configurations existantes... -->
  
  <!-- ========================================== -->
  <!-- CONFIGURATION PAYPAL LOGIN                 -->
  <!-- ========================================== -->
  
  <!-- 
    Client ID de votre application PayPal
    Où le trouver : https://developer.paypal.com/dashboard/ > My Apps & Credentials
  -->
  <PAYPAL_CONNECTUSERNAME>REMPLACEZ_PAR_VOTRE_CLIENT_ID</PAYPAL_CONNECTUSERNAME>
  
  <!-- 
    Client Secret de votre application PayPal
    Où le trouver : https://developer.paypal.com/dashboard/ > My Apps & Credentials > Show
    ⚠️ ATTENTION : Ne partagez JAMAIS cette valeur publiquement
  -->
  <PAYPAL_CONNECTPASSWORD>REMPLACEZ_PAR_VOTRE_CLIENT_SECRET</PAYPAL_CONNECTPASSWORD>
  
  <!-- ========================================== -->
  <!-- EXEMPLES DE VALEURS                        -->
  <!-- ========================================== -->
  
  <!-- 
  SANDBOX (Test) :
  <PAYPAL_CONNECTUSERNAME>AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw</PAYPAL_CONNECTUSERNAME>
  <PAYPAL_CONNECTPASSWORD>EO70VAO37ZELyJxtR7rR-lrFJVTTgRzWQsxtt1NAEcuj8CSKWP91c0n4ALcB0MXYnBjpXCXUsd4BnNDJ</PAYPAL_CONNECTPASSWORD>
  
  PRODUCTION :
  <PAYPAL_CONNECTUSERNAME>Votre_Client_ID_Production</PAYPAL_CONNECTUSERNAME>
  <PAYPAL_CONNECTPASSWORD>Votre_Client_Secret_Production</PAYPAL_CONNECTPASSWORD>
  -->
  
  <!-- Autres configurations existantes... -->
  
</configuration>

