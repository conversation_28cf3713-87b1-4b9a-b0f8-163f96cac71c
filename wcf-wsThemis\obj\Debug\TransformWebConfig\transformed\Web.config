﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,Log4net" />
  </configSections>
  <appSettings>
    <add key="TypeRun" value="TEST" />
    <add key="CryptoKey" value="RodWebShop95" />
    <add key="configFileForLog4net" value="D:\WORK\WEBSERVICES\WCF_THEMIS\log4netconfig.xml" />
    <add key="inboxToken" value="********************************" />
    <add key="ThemisIniPathTest" value="\\Srv-paiement64\customerfiles\TEST\[idstructureSur4zeros]\CONFIGSERVER\config.ini.xml" />
    <add key="ThemisIniPath" value="\\Srv-paiement64\customerfiles\PROD\[idstructureSur4zeros]\CONFIGSERVER\config.ini.xml" />
    <add key="mailing_InBox_baseUrl" value="https://entreedirecte-dev.inbox.fr" />
    <add key="LogosPath" value="c:\" />
    <add key="path_script_sql_PROD" value="\\**************\webservices\PROD\wcfThemisV\2.0.73\scriptsSql\[directory\][filename][.structureid].sql" />
    <add key="path_script_sql" value="\\**************\webservices\DEV\wcfThemis\scriptsSql\[directory\][filename][.structureid].sql" />
    <!--<add key="path_script_sql" value="I:\pga\SourcesDotNet\webServices\wcf_webThemis\wcf_webThemis\wcf-wsThemis\scriptsSql\[directory\][filename][.structureid].sql"/>-->
    <add key="listWTsConns" value="\\**************\webservices\TEST\listConnexionsWTFlag\flagWTConnexions.flg" />
    <add key="LogosPath" value="\\*************\customerfiles\TEST\[idstructureSur4zeros]\PAIEMENT\IMAGES\LogosMaquettes\" />
    <add key="logsLevel" value="Normal" />
  </appSettings>
  <connectionStrings>
    <add name="WSAdminConnectionString" connectionString="Data Source=*************;Initial Catalog=WSAdmin_test;User ID=SphereWebTest;Password=************************************************************************************************************" providerName="System.Data.SqlClient" />
    <add name="WebTracingConnectionString" connectionString="Data Source=************;Initial Catalog=WebTracing;User ID=SphereWeb;Password=************************************************************************************************************" providerName="System.Data.SqlClient" />
    <add name="WSAdminConnectionStringProd" connectionString="Data Source=************;Initial Catalog=WSAdmin;User ID=SphereWeb;Password=************************************************************************************************************" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <!--
    Pour obtenir une description des modifications de web.config, voir http://go.microsoft.com/fwlink/?LinkId=235367.

    Les attributs suivants peuvent être définis dans la balise <httpRuntime>.
      <system.Web>
        <httpRuntime targetFramework="4.7.1" />
      </system.Web>
  -->
  <system.web>
    <compilation debug="true" targetFramework="4.8" />
    <httpRuntime targetFramework="4.5" />
  </system.web>
  <system.serviceModel>
    <behaviors>
      <serviceBehaviors>
        <behavior>
          <!-- To avoid disclosing metadata information, set the values below to false before deployment -->
          <serviceMetadata httpGetEnabled="true" httpsGetEnabled="true" />
          <!-- To receive exception details in faults for debugging purposes, set the value below to true.  Set to false before deployment to avoid disclosing exception information -->
          <serviceDebug includeExceptionDetailInFaults="true" />
        </behavior>
      </serviceBehaviors>
    </behaviors>
    <protocolMapping>
      <add binding="basicHttpsBinding" scheme="https" />
    </protocolMapping>
    <serviceHostingEnvironment aspNetCompatibilityEnabled="true" multipleSiteBindingsEnabled="true" />
  </system.serviceModel>
  <system.webServer>
    <modules runAllManagedModulesForAllRequests="true" />
    <!--
        To browse web app root directory during debugging, set the value below to true.
        Set to false before deployment to avoid disclosing web app folder information.
      -->
    <directoryBrowse enabled="true" />
  </system.webServer>
  <log4net>
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <file type="log4net.Util.PatternString" value="I:\pga\SourcesDotNet\webServices\wsForClientsFullWS_Tfs\%property{structureId}.log" />
      <appendToFile value="true" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="2000KB" />
      <staticLogFileName value="true" />
      <countDirection value="1" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="[%d{yyyy-MM-dd HH:mm:ss:fff}] - (%property{IP}) [%-5p] – %m%n" />
      </layout>
    </appender>
    <appender name="RollingFileAppenderComm" type="log4net.Appender.RollingFileAppender">
      <file type="log4net.Util.PatternString" value="I:\pga\SourcesDotNet\webServices\wsForClientsFullWS_Tfs\common.log" />
      <appendToFile value="true" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="2000KB" />
      <staticLogFileName value="true" />
      <countDirection value="1" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="[%d{yyyy-MM-dd HH:mm:ss:fff}] - (%property{structureId}) (%property{ip}) [%-5p] – %m%n" />
      </layout>
    </appender>
    <root>
      <level value="ALL" />
      <appender-ref ref="RollingFileAppender" />
      <appender-ref ref="RollingFileAppenderComm" />
    </root>
  </log4net>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.0" newVersion="9.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
<!--ProjectGuid: 648C5A8D-0A62-4439-B2BC-1F377D7ACAF3-->