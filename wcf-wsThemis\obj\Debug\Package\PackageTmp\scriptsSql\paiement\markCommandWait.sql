﻿

--set @flag = REPLACE(@flag,'''','''''')
declare @manifId int, @seanceId int, @dossierId int
declare cur_cl cursor for
	select manifestation_id, seance_id, dossier_id from commande_ligne cl where commande_id = @cmdId and type_ligne ='DOS'

open cur_cl
fetch next from cur_cl into @manifid, @seanceid, @dossierid
while (@@FETCH_STATUS =0)
begin
	declare @sql varchar(max)
	set @sql ='UPDATE entree_' + ltrim(rtrim(STR(@manifid))) + ' SET flag_selection=''' + @flag + ''' WHERE entree_etat=''R'' and dossier_id=' + STR(@dossierid)
	set @sql = @sql +' UPDATE dossier_' + ltrim(rtrim(STR(@manifid))) + ' SET dossier_icone=2874 WHERE commande_id=' + STR(@cmdId) + ' AND dossier_id=' + STR(@dossierid)
	
	exec (@sql)
	print @sql
	fetch next from cur_cl into @manifid, @seanceid, @dossierid
end
close cur_cl
deallocate cur_cl

update commande set commande_lock =@Flag where commande_id = @CmdId