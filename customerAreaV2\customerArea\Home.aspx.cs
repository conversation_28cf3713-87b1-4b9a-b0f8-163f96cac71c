﻿using customerArea.App_Code;
using log4net;
using System;
using System.Collections.Generic;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml.Linq;
using ws_bll.WT;
using ws_DTO.objets_liaisons;
using System.Linq;
using System.Xml;
using ws_DTO;
using App_Code;

namespace customerArea
{
    public partial class Home : basePage
    {
        private static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


        public string AdherentNumber { get; set; } = "";
        /// <summary>
        /// kkkk
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!UserIsAuthentified)
            {
                logger.Debug("pas authentifié");
                string pageIdentif = "Login.aspx";

                string qs = Page.Request.Url.Query.Replace("resetI=1", "wrst=1");
                string fileP = pageIdentif + qs;

                Response.Redirect(fileP, true);
            }
            else
            {

                int structureId = 0;
                if (Request.QueryString["idstructure"] != "" && Request.QueryString["idstructure"] != null && int.TryParse(Request.QueryString["idstructure"], out structureId))
                {
                    System.Web.HttpContext.Current.Session["idstructure"] = structureId;
                }
                else if (GetStructureId() > 0)
                {
                    structureId = GetStructureId();
                }

                GestionTraceManager.WriteLog(structureId, "page_load home...", TypeLog.LoadPage);

                int resulEvent = 0;
                if (Request.QueryString["eventId"] != "" && Request.QueryString["eventId"] != null && int.TryParse(Request.QueryString["eventId"], out resulEvent))
                {
                    System.Web.HttpContext.Current.Session["eventId"] = resulEvent;
                }
                string idpa = "0";
                if (System.Web.HttpContext.Current.Session["ProfilAcheteurId"] != null)
                {
                    idpa = (string)System.Web.HttpContext.Current.Session["ProfilAcheteurId"];
                }

                // if (System.Web.HttpContext.Current.Session["SVarUserIsGuest"] == null && !string.IsNullOrEmpty(System.Web.HttpContext.Current.Session["SVarUserIsGuest"].ToString()) && Convert.ToBoolean(System.Web.HttpContext.Current.Session["SVarUserIsGuest"].ToString()) == false)

                int identiteId = 0;
                if (!string.IsNullOrEmpty(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString()))
                {

                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }

                if (identiteId == 0)
                {
                    AdherentNumber = "";
                }
                else
                {
                    AdherentNumber = Utils.GetAdherentNumber(IdStructure, identiteId);
                }



                if (System.Web.HttpContext.Current.Session["SVarUserIsGuest"] != null && Convert.ToBoolean(System.Web.HttpContext.Current.Session["SVarUserIsGuest"].ToString()) == true)
                {
                    logger.Debug("authentifié guest structureid : " + structureId + " event : " + resulEvent + " idpa : " + idpa);
                    Ihm ihm = new Ihm();
                    string contentDiv = ihm.GetTemplate(resulEvent.ToString(), idpa, "template_home_guest");

                    content.InnerHtml = contentDiv;
                }
                else
                {

                    string plateformCode = "";
                    if (Request.QueryString["plateformCode"] != "" && Request.QueryString["plateformCode"] != null)
                    {
                        plateformCode = Request.QueryString["plateformCode"].ToString();
                        System.Web.HttpContext.Current.Session["plateformCode"] = Request.QueryString["plateformCode"].ToString();
                    }

                    string lang = App_Code.Initialisations.GetUserLanguage();


                    List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
                        {
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
                        };




                    logger.Debug("authentifié structureid : " + structureId + " event : " + resulEvent + " idpa : " + idpa);
                    Ihm ihm = new Ihm();
                    string contentDiv = ihm.GetTemplate(resulEvent.ToString(), idpa, "template_home");



                    //On récup les options du fichier appsettings.json
                    dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, structureId, resulEvent, int.Parse(idpa), plateformCode, lang);

                    List<string> listToDelete = new List<string>();

                    List<string> lstPagesToCheck = new List<string>()
                    {
                        "myprofil","myorders","myreservations","mybankdetails","mywaitinglist","myconsumerslist","myattachments","myreabofans","mywishlist","myadhesions",
                        "myticketsw","myordersw"
                    };

                    foreach (var pageToCheck in lstPagesToCheck)
                    {
                        dynamic isPageAuthorized = globalPlateform.global.pages[pageToCheck]?.Value;
                        if (globalPlateform.customer != null && globalPlateform.customer.global != null && globalPlateform.customer.global.pages != null)
                        {
                            isPageAuthorized = globalPlateform.customer.global.pages?[pageToCheck].Value;
                        }
                        //Ajout dans la liste à supprimer
                        if (isPageAuthorized == null || !isPageAuthorized)
                        {
                            listToDelete.Add(pageToCheck);
                        }
                    }

                    LoginDefault ld = new LoginDefault();


                    wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                    IdentiteEntity user = new IdentiteEntity();
                    user = wcfThemis.GetCustomerProfileOfEmailOrIdentiteId(
                       structureId.ToString(),
                       string.Empty,
                       string.Empty,
                       identiteId.ToString(),
                       ld.GetEmailPhoneNumber(structureId),
                       "N"
                    );

                    if (user != null)
                    {
                        System.Web.HttpContext.Current.Session["SVarMyLogin"] = "true";
                        System.Web.HttpContext.Current.Session["SVarUserIdentity"] = "true";
                        System.Web.HttpContext.Current.Session["SVarMyResponse"] = user;
                        System.Web.HttpContext.Current.Session["SVarUserIdentityNom"] = user.SurName;
                        System.Web.HttpContext.Current.Session["SVarUserIdentityPrenom"] = user.FirstName;
                        System.Web.HttpContext.Current.Session["SVarUserIdentityID"] = user.Identite_id.ToString();
                        System.Web.HttpContext.Current.Session["SVarUserEmail"] = user.Email;
                        System.Web.HttpContext.Current.Session["SVarUserIsGuest"] = user.IsGuestLogin.ToString().ToLower();

                    }

                    XDocument doc = new XDocument();
                    doc = XDocument.Parse(contentDiv);

                    IEnumerable<XElement> hdFirstimgcollapse = doc.Descendants("li").Where(at => at.LastAttribute.Name.LocalName == "data-pagename").ToList();

                    IEnumerable<XElement> noeudToDelete = hdFirstimgcollapse.Where(a => listToDelete.Any(x => x.ToString() == a.LastAttribute.Value));
                    noeudToDelete.Remove();



                    contentDiv = doc.ToString();



                    string urlToUpdate = Urls.getUrlPopUpdateIdentification(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[linkupdate]", urlToUpdate);

                    string urlPageHisto = Urls.getUrlPopHistoCustomer(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[urlPageHisto]", urlPageHisto);


                    string urlBankDetails = Urls.getUrlBankDetails(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[linkBankDetails]", urlBankDetails);


                    string urlReaboFans = Urls.getUrlFans(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[linkreabo]", urlReaboFans);

                    string urlWaitList = Urls.getUrlWaitList(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[linkwaitlist]", urlWaitList);


                    string urlLinkedConsumersList = Urls.getUrlLinkedConsumersList(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[linkconsumerslist]", urlLinkedConsumersList);

                    string urlAttachments = Urls.getUrlAttachments(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[linkattachments]", urlAttachments);

                    string urlResaList = Urls.getUrlPopResaListCustomer(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[urlPageResaList]", urlResaList);

                    string urlAdhesions = Urls.getUrlAdhesions(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[linkadhesions]", urlAdhesions);

                    string urlTicketW = Urls.getUrlTicketsW(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[linkticketsw]", urlTicketW);

                    string urlOrdersW = Urls.getUrlOrdersW(IdStructure, resulEvent.ToString(), true);
                    contentDiv = contentDiv.Replace("[linkordersw]", urlOrdersW);


                    content.InnerHtml = contentDiv;

                }


                string fileP = Page.Request.FilePath;
                fileP = fileP.Substring(fileP.LastIndexOf('/') + 1, fileP.LastIndexOf(".") - fileP.LastIndexOf('/') - 1);


                #region fichiers javascripts par page/structures
                // envoie le fichier javascript propres à la page 
                System.Text.StringBuilder sb_insere_javascriptsfiles = new System.Text.StringBuilder();
                //basePage bp = new basePage();
                //Urls url = new Urls();

                Urls url = new Urls();
                //ScriptManager.RegisterStartupScript(this.Page, typeof(Page), string.Format("StartupInclude"), sb_insere_javascriptsfiles.ToString(), false);
                sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile(fileP, resulEvent, true));
                // sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile("commons", resulEvent, true));
                ScriptManager.RegisterStartupScript(Page, typeof(Page), "Master", sb_insere_javascriptsfiles.ToString(), false);
                #endregion
            }
        }
    }
}