<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test PayPal Login - Sandbox</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 800px;
            width: 100%;
            padding: 40px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-section h2 {
            color: #0070ba;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .config-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #0070ba;
        }

        .config-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .config-value {
            font-family: 'Courier New', monospace;
            color: #666;
            font-size: 13px;
            word-break: break-all;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
        }

        .btn-paypal {
            background: #0070ba;
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 112, 186, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-paypal:hover {
            background: #005ea6;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 112, 186, 0.4);
        }

        .btn-paypal:active {
            transform: translateY(0);
        }

        .log-container {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-info {
            color: #4ec9b0;
        }

        .log-success {
            color: #4ec9b0;
            background: rgba(78, 201, 176, 0.1);
        }

        .log-error {
            color: #f48771;
            background: rgba(244, 135, 113, 0.1);
        }

        .log-warning {
            color: #dcdcaa;
            background: rgba(220, 220, 170, 0.1);
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-sandbox {
            background: #fff3cd;
            color: #856404;
        }

        .status-ready {
            background: #d4edda;
            color: #155724;
        }

        .icon {
            font-size: 20px;
        }

        .button-container {
            text-align: center;
            margin: 30px 0;
        }

        .info-box {
            background: #e7f3ff;
            border-left: 4px solid #0070ba;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .info-box p {
            color: #004085;
            font-size: 14px;
            line-height: 1.6;
        }

        .clear-logs {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-size: 12px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .clear-logs:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test PayPal Login</h1>
        <p class="subtitle">
            <span class="status-badge status-sandbox">🔧 SANDBOX MODE</span>
            <span class="status-badge status-ready">✅ READY</span>
        </p>

        <div class="info-box">
            <p>
                <strong>📋 Instructions :</strong><br>
                1. Vérifiez que votre nouvelle App ID est bien configurée ci-dessous<br>
                2. Cliquez sur "Se connecter avec PayPal"<br>
                3. Utilisez un compte PayPal Sandbox pour tester<br>
                4. Vérifiez les logs pour voir le flux d'authentification
            </p>
        </div>

        <!-- Configuration Section -->
        <div class="test-section">
            <h2><span class="icon">⚙️</span> Configuration PayPal</h2>
            
            <div class="config-item">
                <div class="config-label">Client ID (App ID)</div>
                <div class="config-value" id="clientId">AXtTYg23KpqsghJhv85H_nmR6tw_yR69Y93nVaavOaiMnb7dF8Sw2Olqo7C5fG6R2jixYaefK970fVzH</div>
            </div>

            <div class="config-item">
                <div class="config-label">Return URL</div>
                <div class="config-value" id="returnUrl">http://localhost/customer/_loginPayPal.aspx</div>
            </div>

            <div class="config-item">
                <div class="config-label">Scopes</div>
                <div class="config-value">openid email profile address</div>
            </div>

            <div class="config-item">
                <div class="config-label">Environment</div>
                <div class="config-value">Sandbox (https://www.sandbox.paypal.com)</div>
            </div>
        </div>

        <!-- Test Button -->
        <div class="button-container">
            <button class="btn-paypal" onclick="loginWithPayPal()">
                <span class="icon">🔐</span>
                Se connecter avec PayPal
            </button>
        </div>

        <!-- Logs Section -->
        <div class="test-section">
            <h2><span class="icon">📝</span> Logs en temps réel</h2>
            <button class="clear-logs" onclick="clearLogs()">🗑️ Effacer les logs</button>
            <div class="log-container" id="logContainer">
                <div class="log-entry log-info">🚀 Prêt pour le test...</div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const CLIENT_ID = 'AXtTYg23KpqsghJhv85H_nmR6tw_yR69Y93nVaavOaiMnb7dF8Sw2Olqo7C5fG6R2jixYaefK970fVzH';
        const RETURN_URL = 'http://localhost/customer/_loginPayPal.aspx';
        const SCOPES = 'openid email profile address';
        const SANDBOX_URL = 'https://www.sandbox.paypal.com/signin';

        // Fonction de logging
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('fr-FR');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            
            let icon = '📝';
            if (type === 'success') icon = '✅';
            if (type === 'error') icon = '❌';
            if (type === 'warning') icon = '⚠️';
            
            logEntry.textContent = `[${timestamp}] ${icon} ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div class="log-entry log-info">🚀 Logs effacés...</div>';
        }

        // Fonction de connexion PayPal
        function loginWithPayPal() {
            addLog('🔵 Début du processus d\'authentification PayPal', 'info');
            addLog(`Client ID: ${CLIENT_ID.substring(0, 20)}...`, 'info');
            addLog(`Return URL: ${RETURN_URL}`, 'info');

            // Construire l'URL d'authentification PayPal
            const authUrl = `${SANDBOX_URL}` +
                `?intent=connect` +
                `&client_id=${CLIENT_ID}` +
                `&response_type=code` +
                `&scope=${encodeURIComponent(SCOPES)}` +
                `&redirect_uri=${encodeURIComponent(RETURN_URL)}`;

            addLog('🔗 URL d\'authentification générée', 'success');
            addLog(`URL: ${authUrl.substring(0, 100)}...`, 'info');

            // Dimensions de la popup
            const width = 450;
            const height = 550;
            const left = (screen.width / 2) - (width / 2);
            const top = (screen.height / 2) - (height / 2);

            addLog('🪟 Ouverture de la popup PayPal...', 'info');

            // Ouvrir la popup
            const popup = window.open(
                authUrl,
                'paypal-login',
                `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no`
            );

            if (popup) {
                addLog('✅ Popup ouverte avec succès !', 'success');
                addLog('👉 Connectez-vous avec un compte PayPal Sandbox', 'warning');
                
                // Surveiller la fermeture de la popup
                const checkPopup = setInterval(() => {
                    if (popup.closed) {
                        clearInterval(checkPopup);
                        addLog('🔴 Popup fermée', 'info');
                        checkForAuthCode();
                    }
                }, 500);
            } else {
                addLog('❌ Impossible d\'ouvrir la popup ! Vérifiez les bloqueurs de popup.', 'error');
            }
        }

        // Vérifier si un code d'autorisation est présent dans l'URL
        function checkForAuthCode() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const codePP = urlParams.get('codePP');

            if (code || codePP) {
                const authCode = code || codePP;
                addLog('🎉 Code d\'autorisation reçu !', 'success');
                addLog(`Code: ${authCode.substring(0, 20)}...`, 'success');
                addLog('✅ Authentification réussie ! Le code peut maintenant être échangé contre un token.', 'success');
            } else {
                addLog('ℹ️ Aucun code d\'autorisation détecté dans l\'URL', 'info');
            }
        }

        // Écouter les messages de la popup (callback)
        window.addEventListener('message', function(event) {
            // Vérifier l'origine pour la sécurité
            if (event.origin !== window.location.origin) {
                return;
            }

            if (event.data.type === 'paypal-auth-code') {
                addLog('📨 Message reçu de la popup', 'info');
                addLog(`Code: ${event.data.code.substring(0, 20)}...`, 'success');
                addLog('✅ Flux d\'authentification terminé avec succès !', 'success');
            }
        });

        // Vérifier au chargement de la page
        window.addEventListener('load', function() {
            addLog('✅ Page de test chargée', 'success');
            addLog(`Client ID configuré: ${CLIENT_ID.substring(0, 30)}...`, 'info');
            checkForAuthCode();
        });
    </script>
</body>
</html>

