﻿/* abo : abonnement/LoadEventsOfFormula.sql */

 	DECLARE @const_placementlibre int; set @const_placementlibre =32;
	DECLARE @const_priseauto int; set @const_priseauto =16;
	DECLARE @const_vueplacement int; set @const_vueplacement =8;
	DECLARE @const_choixsurplan int; set @const_choixsurplan =1;

SELECT 'N' as todelete, m.manifestation_id as eventid,s.seance_Id as sessionId, m.manifestation_nom as eventName,
l.lieu_id as placeId, l.lieu_nom as placeName, s.seance_date_deb as SessionStartDate,
 groupe_formule_id as managementRuleID,
 am.obligatoire as ismandatory,
 gt.categ_id ,
 gt.dispo,
   --categorie.categ_nom ,
  case when gt.dispo < 1 then categorie.categ_nom + '/*' else categorie.categ_nom end as categ_nom,
 gt.type_tarif_id ,
 gt.gestion_place_id as gpId

,gp.placesTotal
 ,notnumbered = CASE WHEN (gp.prise_place & @const_placementlibre)=0 THEN 0 ELSE 1 END
 ,priseauto = CASE WHEN (gp.prise_place & @const_priseauto)=0 THEN 0 ELSE 1 END
 ,vueplacement = CASE WHEN (gp.prise_place & @const_vueplacement)=0 THEN 0 ELSE 1 END
 ,choixplan = CASE WHEN (gp.prise_place & @const_choixsurplan)=0 THEN 0 ELSE 1 END
 , fg.groupe_id,  gt.zone_id, z.zone_nom, gt.etage_id, et.etage_nom, gt.section_id, sect.section_nom, categorie.pref_affichage, ordre
 , 0 as is_distanciation, 0 as nb_distanciation_horizontal, 0 as nb_distanciation_vertical
  INTO #tmp_result

 FROM  abonnement_manifestation am 
 
INNER JOIN formule_abonnement fa on fa.form_abon_id = am.formule_id
INNER JOIN formule_groupe fg on fg.groupe_id = am.groupe_formule_id
INNER JOIN manifestation m ON m.manifestation_id = am.manif_id
INNER JOIN seance s ON s.manifestation_id=m.manifestation_id AND s.seance_Id=am.seance_id
INNER JOIN lieu l on l.lieu_id =s.lieu_id
INNER JOIN gestion_place_DispoAbosZES gt on gt.manif_id = m.manifestation_id and gt.session_id = am.seance_id and gt.formule_id=am.formule_id
INNER JOIN gestion_place gp on gp.gestion_place_id=gt.gestion_place_id
INNER JOIN categorie on gt.categ_id = categorie.categ_id

INNER JOIN zone z on z.zone_id = gt.zone_id
INNER JOIN etage et on et.etage_id = gt.etage_id
INNER JOIN section sect on sect.section_id = gt.section_id

WHERE am.formule_id in ([formulaID]) and fg.libelle_id<>0
and s.seance_cloturer='N' and s.seance_verrouiller='N' and s.seance_masquer='N' and s.seance_niveau=1  
and s.seance_date_deb>getdate()
and  gt.type_tarif_id in ([listTarifID])
ORDER BY managementRuleID, ordre, manifestation_nom, s.seance_date_deb

--pour tester, jouer avec ça:
--delete #tmp_result where etage_id=140001 and sessionid= 754

--delete #tmp_result where section_id=140004 and sessionid= 788

--delete #tmp_result where section_id= 140005 and eventid=362

declare @currentManagementruleid int
declare  curs_constraintefermee cursor scroll for
	select distinct managementruleid from #tmp_result where ismandatory ='O'
OPEN curs_constraintefermee 
FETCH NEXT FROM curs_constraintefermee INTO @currentManagementruleid --parcourt des contraintes de l'offre
WHILE @@FETCH_STATUS=0
BEGIN 

	print @currentManagementruleid
	declare @nsessions int
	select @nsessions = count(distinct sessionid) from #tmp_result where managementruleid = @currentManagementruleid and todelete='N'
	--select @nsessions

	--- delete zone 
	declare @currentzoneid int
	declare curs_zones cursor scroll for 
		select distinct zone_id from  #tmp_result where managementruleid = @currentManagementruleid and todelete='N'
	open curs_zones
	fetch next from curs_zones into @currentzoneid
	while @@FETCH_STATUS=0
	BEGIN
		declare @nsessionsavecthiszone int
		select @nsessionsavecthiszone = count(distinct sessionId) FROM #tmp_result where managementruleid = @currentManagementruleid and zone_id=@currentzoneid and todelete='N'
		--select @nsessionsavecthiszone
		if (@nsessionsavecthiszone<>@nsessions)
			update #tmp_result set todelete ='O' WHERE zone_id = @currentzoneid and managementruleid = @currentManagementruleid

		fetch next from curs_zones into @currentzoneid
	END
	CLOSE curs_zones
	DEALLOCATE curs_zones

	--- delete etage 
	declare @currentetageid int
	declare curs_etages cursor scroll for 
		select distinct etage_id from  #tmp_result where managementruleid = @currentManagementruleid and todelete='N'
	open curs_etages
	fetch next from curs_etages into @currentetageid
	while @@FETCH_STATUS=0
	BEGIN
		declare @nsessionsavecthisetage int
		select @nsessionsavecthisetage = count(distinct sessionId) FROM #tmp_result where managementruleid = @currentManagementruleid and etage_id=@currentetageid and todelete='N'
		--select @nsessionsavecthisetage
		if (@nsessionsavecthisetage<>@nsessions)
			update #tmp_result set todelete ='O' WHERE etage_id = @currentetageid and managementruleid = @currentManagementruleid

		fetch next from curs_etages into @currentetageid
	END
	CLOSE curs_etages
	DEALLOCATE curs_etages

		--- delete sections 
	declare @currentsectionid int
	declare curs_sections cursor scroll for 
		select distinct section_id from  #tmp_result where managementruleid = @currentManagementruleid and todelete='N'
	open curs_sections
	fetch next from curs_sections into @currentsectionid
	while @@FETCH_STATUS=0
	BEGIN
		declare @nsessionsavecthisection int
		select @nsessionsavecthisection = count(distinct sessionId) FROM #tmp_result where managementruleid = @currentManagementruleid and section_id=@currentsectionid and todelete='N'
		--select @nsessionsavecthisection
		if (@nsessionsavecthisection<>@nsessions)
			update #tmp_result set todelete ='O' WHERE section_id = @currentsectionid and managementruleid = @currentManagementruleid

		fetch next from curs_sections into @currentsectionid
	END
	CLOSE curs_sections
	DEALLOCATE curs_sections


		--- delete categs 
	declare @currentcategid int
	declare curs_categs cursor scroll for 
		select distinct categ_id from  #tmp_result where managementruleid = @currentManagementruleid and todelete='N'
	open curs_categs
	fetch next from curs_categs into @currentcategid
	while @@FETCH_STATUS=0
	BEGIN
		declare @nsessionsavecthicateg int
		select @nsessionsavecthicateg = count(distinct sessionId) FROM #tmp_result where managementruleid = @currentManagementruleid and categ_id=@currentcategid and todelete='N'
		--select @nsessionsavecthisection
		if (@nsessionsavecthicateg<>@nsessions)
			update #tmp_result set todelete ='O' WHERE categ_id = @currentcategid and managementruleid = @currentManagementruleid

		fetch next from curs_categs into @currentcategid
	END
	CLOSE curs_categs
	DEALLOCATE curs_categs

	FETCH NEXT FROM curs_constraintefermee INTO @currentManagementruleid
END
CLOSE curs_constraintefermee
DEALLOCATE curs_constraintefermee




declare @currenteventid int
declare curs_events cursor scroll for 
	select distinct eventid from  #tmp_result 
open curs_events
fetch next from curs_events into @currenteventid
while @@FETCH_STATUS=0
BEGIN
		
	DECLARE @isDistancation INT, 
	@nb_distanciation_horizontal INT, 
	@nb_distanciation_vertical INT;

	select @isDistancation = count(*) from manifestation m	inner join  proprietes_of_manifs ppm on ppm.manifestation_id = m.manifestation_id
	inner join proprietes_references_of_manifs ppref on ppref.propriete_ref_id = ppm.propriete_ref_id
	where upper(code)='DISTCV' and m.manifestation_id = @currenteventid and valeur=1
	

	select @nb_distanciation_horizontal = ISNULL((select preference_valeur FROM structure_prefs WHERE preference_cle='DISTANCIATION_HORIZ'),0)
	select @nb_distanciation_vertical = ISNULL((select preference_valeur FROM structure_prefs WHERE preference_cle='DISTANCIATION_VERTI'),0) 


	update #tmp_result set is_distanciation =@isDistancation, 
		nb_distanciation_horizontal= @nb_distanciation_horizontal,  
		nb_distanciation_vertical = @nb_distanciation_vertical 
	WHERE eventid = @currenteventid

	fetch next from curs_events into @currenteventid
END
CLOSE curs_events
DEALLOCATE curs_events

select * from #tmp_result where 
--ismandatory ='O' and 
todelete = 'N'
order by managementRuleID, ordre, eventName, SessionStartDate, pref_affichage

drop table #tmp_result 