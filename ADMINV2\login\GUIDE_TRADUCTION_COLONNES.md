# Guide d'implémentation - Traduction des en-têtes de colonnes SQL

## Problème résolu
Les en-têtes de colonnes provenant de la requête SQL avec des alias français comme "Reference unique du billet", "Dernier état de la place sur la période", etc. ne sont pas traduits dans l'interface utilisateur.

## Solution implémentée

### 1. Traductions ajoutées dans les fichiers de ressources

**Fichiers modifiés :**
- `login/App_GlobalResources/GeneralTerms.resx` (français)
- `login/App_GlobalResources/GeneralTerms.de.resx` (allemand)  
- `login/App_GlobalResources/GeneralTerms.es.resx` (espagnol)
- `login/pages/Resources/translate.fr.xml` (JavaScript français)
- `login/pages/Resources/translate.en.xml` (JavaScript anglais)

**Nouvelles clés de traduction ajoutées :**
- `ReferenceUniqueBillet` / `reference_unique_billet`
- `DernierEtatPlace` / `dernier_etat_place`
- `IdFiliereVente` / `id_filiere_vente`
- `NomFiliereVente` / `nom_filiere_vente`
- `IdManifestation` / `id_manifestation`
- `NomManifestation` / `nom_manifestation`
- `GroupeManifestations` / `groupe_manifestations`
- `GenreManifestation` / `genre_manifestation`
- `SousGenreManifestation` / `sous_genre_manifestation`
- `CibleManifestation` / `cible_manifestation`
- `IdSeance` / `id_seance`
- `DateDebutSeance` / `date_debut_seance`
- `IdCategorie` / `id_categorie`
- `NomCategorie` / `nom_categorie`
- `IdTarif` / `id_tarif`
- `NomTarif` / `nom_tarif`
- `NumeroCommande` / `numero_commande`
- `ModePaiement` / `mode_paiement`
- `DateOperation` / `date_operation`
- `IdIdentite` / `id_identite`
- `NomIdentite` / `nom_identite`
- `PrenomIdentite` / `prenom_identite`
- `Civilite` / `civilite`
- `CodePostal` / `code_postal`
- `Ville` / `ville`
- `Pays` / `pays`
- `DateNaissance` / `date_naissance`
- `FiliereCreationIdentite` / `filiere_creation_identite`
- `TelephoneMobile` / `telephone_mobile`
- `AdressePostale` / `adresse_postale`
- `OptIn` / `opt_in`
- `NumeroBillet` / `numero_billet`

### 2. Fichier SQL modifié

**Fichier créé :** `login/GetStatementCashFullDetail_translated.sql`

Ce fichier contient la requête SQL modifiée avec les clés de traduction au lieu des alias français.

**Exemple de modification :**
```sql
-- AVANT
as 'Reference unique du billet'
as 'Dernier état de la place sur la période'

-- APRÈS  
as 'reference_unique_billet'
as 'dernier_etat_place'
```

### 3. Utilitaire JavaScript de traduction

**Fichier créé :** `login/assets/js/column-header-translation.js`

Contient les fonctions `TranslateColumnHeaders()` et `TranslateTableHeaders()` pour traduire les en-têtes côté client si nécessaire.

## Étapes d'implémentation

### Étape 1 : Remplacer le fichier SQL original
1. Localisez le fichier SQL utilisé par `GET_SCRIPT(_structureid, "select\\", "GetStatementCashFullDetail")`
2. Remplacez son contenu par celui de `GetStatementCashFullDetail_translated.sql`
3. Ou modifiez directement les alias dans le fichier existant

### Étape 2 : Adapter le code JavaScript (si DataTables)
```javascript
// Dans votre configuration DataTables
"aoColumns": [
    { 
        "mDataProp": "reference_unique_billet",
        "sTitle": ReadXmlTranslate('reference_unique_billet')
    },
    { 
        "mDataProp": "dernier_etat_place",
        "sTitle": ReadXmlTranslate('dernier_etat_place')
    }
    // ... autres colonnes
]
```

## ✅ Solution implémentée

### Problème résolu
Le tableau de cash statement affichait les **clés SQL** au lieu des **libellés traduits** dans les en-têtes de colonnes.

### Solution mise en place

#### 1. **Traduction côté serveur (C#)**
**Fichier modifié :** `login/pages_stats/cash_statement.aspx.cs`

- Ajout de la méthode `TranslateColumnHeader()` qui convertit les clés SQL en libellés lisibles
- Modification de la méthode `ConvertDataTableToHTML()` pour utiliser cette traduction

```csharp
// AVANT
htmlthisseance += "<td class='bgGrey bold'>" + dtManif.Columns[k].ColumnName + "</td>";

// APRÈS
string translatedHeader = TranslateColumnHeader(dtManif.Columns[k].ColumnName);
htmlthisseance += "<td class='bgGrey bold'>" + translatedHeader + "</td>";
```

#### 2. **Traduction côté client (JavaScript) - Sécurité**
**Fichier modifié :** `login/assets/js/column-header-translation.js`

- Extension de la fonction `TranslateColumnHeaders()` pour gérer les tableaux générés par C#
- Support des structures `<td class='bgGrey bold'>` utilisées dans cash statement

**Fichier modifié :** `login/pages_stats/cash_statement.aspx`

- Inclusion du script de traduction
- Appel automatique de la traduction après injection des tableaux

```javascript
// Applique la traduction des en-têtes pour tous les tableaux
$('.dataTables table').each(function() {
    var tableId = $(this).attr('id');
    if (!tableId) {
        tableId = 'table_' + Math.random().toString(36).substr(2, 9);
        $(this).attr('id', tableId);
    }
    TranslateTableHeaders(tableId);
});
```

### Résultat
- ✅ Les en-têtes affichent maintenant des libellés lisibles
- ✅ Solution robuste avec double sécurité (serveur + client)
- ✅ Compatible avec la structure existante
- ✅ Extensible pour d'autres tableaux

### Étape 3 : Alternative - Traduction côté client
Si vous ne pouvez pas modifier le SQL, utilisez la fonction JavaScript :
```javascript
// Après le chargement du tableau
TranslateTableHeaders('votre-table-id');
```

### Étape 4 : Alternative - Traduction côté serveur C#
```csharp
// Dans votre méthode GetStatementCashFullDetail
DataSet ds = GetDataSet(_structureid, sql);
if (ds != null && ds.Tables.Count > 0)
{
    TranslateColumnHeaders(ds.Tables[0], _userLanguage);
}
```

## Avantages de cette solution

1. **Cohérence** : Utilise le système de traduction existant
2. **Réutilisabilité** : Les clés peuvent être utilisées ailleurs
3. **Maintenabilité** : Facile d'ajouter de nouvelles langues
4. **Performance** : Pas de traitement supplémentaire si implémenté au niveau SQL

## Test de la solution

1. Modifiez le fichier SQL avec les nouvelles clés
2. Rechargez la page avec le tableau
3. Vérifiez que les en-têtes sont traduits selon la langue de l'utilisateur
4. Testez avec différentes langues (français, anglais, allemand, espagnol)

## Langues supportées

- **Français** : Traductions complètes
- **Anglais** : Traductions complètes  
- **Allemand** : Traductions complètes
- **Espagnol** : Traductions complètes

Pour ajouter d'autres langues, ajoutez les traductions dans les fichiers de ressources correspondants.
