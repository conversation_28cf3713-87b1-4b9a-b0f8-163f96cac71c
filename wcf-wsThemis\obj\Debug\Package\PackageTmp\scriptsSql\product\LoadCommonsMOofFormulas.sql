﻿
DECLARE @nbGestionPlaceId int /* compte les gestion_place */ 
select  @nbGestionPlaceId = count(*) from gestion_place where gestion_place_id in ([listGestionPlaceId])


SELECT p.produit_id as productID, produit_nom as ProductName,
CONVERT(INTEGER,(montant1 + montant2)*100) as TotalAmount ,CONVERT(INTEGER,montant2 * 100)  as Charge , nbrGestionPlaceForThisProduct
FROM (
SELECT count(*) as nbrGestionPlaceForThisProduct  /* compte les gestion_place relier a ce produit */  , productID FROM (
select  distinct p.produit_id as ProductID, formule_id, gp.type_tarif_id, gp.gestion_place_id
FROM gestion_place gp inner join gestion_place_type_envoi gpt on gpt.gestion_place_id=gp.gestion_place_id
		INNER JOIN produit p on  p.produit_id = gpt.produit_id
		INNER JOIN produit_stock ps on ps.produit_id=p.produit_id
		where gp.gestion_place_id in ([listGestionPlaceId])
			) s
	GROUP BY ProductID
) s2 
INNER JOIN produit p on p.produit_id = s2.ProductID
INNER JOIN produit_stock ps on ps.produit_id=p.produit_id 
WHERE nbrGestionPlaceForThisProduct = @nbGestionPlaceId /* autant de gestion_place pour ce produit que le nombre reçu en paramètre */ 
ORDER BY p.pref_affichage, produit_nom