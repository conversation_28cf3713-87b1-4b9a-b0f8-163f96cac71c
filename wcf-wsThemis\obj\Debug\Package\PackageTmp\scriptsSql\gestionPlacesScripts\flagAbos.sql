﻿
--update entree_[eventID] set flag_selection='' WHERE flag_selection = 'T[userID]' and entree_etat='L'  and seance_id=[sessionID]

--sp_ws_autoplaces 'entree_[eventID]', [sessionID], [categID], '[listReservesID]', [nbrToFlag], 'T[userID]', 0,0,0,0,0



update entree_[eventID] set flag_selection='' WHERE flag_selection = 'T[userID]' and entree_etat='L'  and seance_id=[sessionID]

exec sp_ws_autoplaces 'entree_[eventID]', [sessionID], [categID], '[listReservesID]', [nbrToFlag], 'T[userID]', [sectionID], [floorID], [zoneID], 0, 0