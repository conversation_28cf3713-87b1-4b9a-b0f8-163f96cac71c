DECLARE @datedeb varchar(25);SET @datedeb='[startDate]';
DECLARE @datefin varchar(25); set @datefin='[endDate]';


SELECT structure_id, sum(nbrE) as nbrE, sum(mtVU) as MtVU, sum(nbrPV) as nbrPV, sum(nbrPC) as nbrPC,
sum(nbrEAbo) as nbrEAbo,  sum(nbrPVAbo) as nbrPVAbo, sum(MtEAbo) as MtEAbo, sum(nbrPCAbo) as nbrPCAbo,
sum(nbrE) + sum(nbrEAbo) as nbrEValideesUnitetAbo, sum(nbrFEV) as NbrFrEnvoiValides, sum(nbrProdV) as nbrProdValides, sum(MtFEnvoi) as MtFEnvoi, sum(MtProduits) as MtProduits 
, sum(mtVU) + sum(MtEAbo) + sum(MtFEnvoi) + sum(MtProduits) as MtTotal, sum(NbrEntPayeNonValide) as NbrEntPayeNonValide, sum(MtPFT) as MtPFT, sum(MtFEnvoiPFT) as MtFEnvoiPFT, sum(nbreAboMulti)  as nbreAboMulti
FROM
( 
	/* ligne à 0 */
	SELECT [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC,0 as nbrEAbo , 0 as nbrPVAbo
	,0 as nbrPCAbo, 0 as nbrFEV, 0 as nbrProdV, 0 as MtVU, 0 as MtEAbo, 0 as MtFEnvoi, 0 as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti

	UNION /* entrees validées & paniers validés */
	SELECT [structureCurrent] as structure_id, count(panier_entree_id) as nbrE, count(distinct(p.panier_id)) as nbrPV, 0 as nbrPC,0 as nbrEAbo , 0 as nbrPVAbo
	, 0 as nbrPCAbo, 0 as nbrFEV, 0 as nbrProdV, sum(montant) as MtVU, 0 as MtEAbo, 0 as MtFEnvoi, 0 as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti
	FROM panier p, panier_entree pe, users u
	WHERE pe.panier_id=p.panier_id
	/*AND date_operation > cast(@datedeb as datetime)
	AND date_operation < cast(@datefin as datetime)*/
	[whereDate]
	[wherePa]
	AND p.structure_id in ([structureCurrent])
	AND etat in ('V','Q','P') and p.commande_id is not null
	AND p.web_user_id = u.user_id 
	GROUP BY p.structure_id

	UNION /* paniers créés */
	SELECT [structureCurrent] as structure_id_id, 0 as nbrE, 0 as nbrPV, count(distinct(p.panier_id)) as nbrPC, 0 as nbrEAbo, 0 as nbrPVAbo
	, 0 as nbrPCAbo, 0 as nbrFEV, 0 as nbrProdV, 0 as MtVU, 0 as MtEAbo, 0 as MtFEnvoi, 0 as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti
	FROM panier p, panier_entree pe, users u
	WHERE pe.panier_id=p.panier_id
	/*AND date_operation > cast(@datedeb as datetime)
	AND date_operation < cast(@datefin as datetime)*/
	[whereDate]
	[wherePa]
	AND p.structure_id in ([structureCurrent])
	AND p.web_user_id = u.user_id 
	GROUP BY p.structure_id

	UNION /* entrees abo validées & paniers abo validés */
	SELECT [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC, count(panier_entree_id) as nbrEAbo,
	count(distinct(p.panier_id)) as nbrPVAbo, 0 as nbrPCAbo, 0 as nbrFEV, 0 as nbrProdV, 0 as MtVU, sum(montant) as MtEAbo, 0 as MtFEnvoi, 0 as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti
	FROM panier p, panier_entree_abo pea, users u
	WHERE pea.panier_id=p.panier_id
	/*AND date_operation > cast(@datedeb as datetime)
	AND date_operation < cast(@datefin as datetime)*/
	[whereDate]
	[wherePa]
	AND p.structure_id in ([structureCurrent])
	AND etat in ('V','Q','P') and p.commande_id is not null
	AND p.web_user_id = u.user_id 
	GROUP BY p.structure_id

	UNION /* paniers abo créés */
	SELECT [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC, 0 as nbrEAbo,
	0 as nbrPVAbo, count(distinct(p.panier_id)) as nbrPCAbo, 0 as nbrFEV, 0 as nbrProdV, 0 as MtVU, 0 as MtEAbo, 0 as MtFEnvoi, 0 as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti
	FROM panier p, panier_entree_abo pea, users u
	WHERE pea.panier_id=p.panier_id
	/*AND date_operation > cast(@datedeb as datetime)
	AND date_operation < cast(@datefin as datetime)*/
	[whereDate]
	[wherePa]
	AND p.structure_id in ([structureCurrent])
	AND p.web_user_id = u.user_id 
	GROUP BY p.structure_id

	UNION /* frais envoi vente unitaire validés */
	SELECT [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC, 0 as nbrEAbo,
	0 as nbrPVAbo, 0 as nbrPCAbo, sum(nombre) as nbrFEV, 0 as nbrProdV, 0 as MtVU, 0 as MtEAbo, sum(montant*nombre) as MtFEnvoi, 0 as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti
	FROM panier p, panier_produit pp, users u
	WHERE pp.panier_id=p.panier_id
	/*AND date_operation > cast(@datedeb as datetime)
	AND date_operation < cast(@datefin as datetime)*/
	[whereDate]
	[wherePa]
	AND etat in ('V','Q','P') and p.commande_id is not null and type_ligne='FRAIS'
	AND p.structure_id in ([structureCurrent])
	AND p.web_user_id = u.user_id 
	GROUP BY p.structure_id

	UNION /* frais envoi vente abo validés */
	SELECT [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC, 0 as nbrEAbo,
	0 as nbrPVAbo, 0 as nbrPCAbo, sum(nombre) as nbrFEV, 0 as nbrProdV, 0 as MtVU, 0 as MtEAbo, sum(montant*nombre) as MtFEnvoi, 0 as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti
	FROM panier p, panier_produit_abo ppa, users u
	WHERE ppa.panier_id=p.panier_id
	/*AND date_operation > cast(@datedeb as datetime)
	AND date_operation < cast(@datefin as datetime)*/
	[whereDate]
	[wherePa]
	AND etat in ('V','Q','P') and p.commande_id is not null and type_ligne='FRAIS'
	AND p.structure_id in ([structureCurrent])
	AND p.web_user_id = u.user_id 
	GROUP BY p.structure_id

	UNION /* produit unitaire validés */
	SELECT [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC, 0 as nbrEAbo,
	0 as nbrPVAbo, 0 as nbrPCAbo, 0 as nbrFEV, sum(nombre) as nbrProdV, 0 as MtVU, 0 as MtEAbo, 0 as MtFEnvoi, sum(montant*nombre) as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti
	FROM panier p, panier_produit pp, users u
	WHERE pp.panier_id=p.panier_id
	/*AND date_operation > cast(@datedeb as datetime)
	AND date_operation < cast(@datefin as datetime)*/
	[whereDate]
	[wherePa]
	AND etat in ('V','Q','P') and p.commande_id is not null and type_ligne<>'FRAIS'
	 AND p.structure_id in ([structureCurrent])
	AND p.web_user_id = u.user_id 
	GROUP BY p.structure_id

	UNION /* produit abo validés */
	SELECT [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC, 0 as nbrEAbo,
	0 as nbrPVAbo, 0 as nbrPCAbo, 0 as nbrFEV, sum(nombre) as nbrProdV, 0 as MtVU, 0 as MtEAbo, 0 as MtFEnvoi, sum(montant*nombre) as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti
	FROM panier p, panier_produit_abo ppa, users u
	WHERE ppa.panier_id=p.panier_id
	/*AND date_operation > cast(@datedeb as datetime)
	AND date_operation < cast(@datefin as datetime)*/
	[whereDate]
	[wherePa]
	AND etat in ('V','Q','P') and p.commande_id is not null and type_ligne<>'FRAIS'
	 AND p.structure_id in ([structureCurrent])
	AND p.web_user_id = u.user_id 
	GROUP BY p.structure_id

	UNION /* entrées payés non validés */
	SELECT [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC,0 as nbrEAbo , 0 as nbrPVAbo
	, 0 as nbrPCAbo, 0 as nbrFEV, 0 as nbrProdV, 0 as MtVU, 0 as MtEAbo, 0 as MtFEnvoi, 0 as MtProduits, count(panier_entree_id) as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti
	FROM panier p, panier_entree pe, users u
	WHERE pe.panier_id=p.panier_id
	/*AND date_operation > cast(@datedeb as datetime)
	AND date_operation < cast(@datefin as datetime)*/
	[whereDate]
	[wherePa]
	AND p.structure_id in ([structureCurrent])
	AND etat in ('P') and p.commande_id is not null
	AND p.web_user_id = u.user_id 
	GROUP BY p.structure_id


	UNION /* montant entrées multi abo */
	select [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC,0 as nbrEAbo , 0 as nbrPVAbo
	,0 as nbrPCAbo, 0 as nbrFEV, 0 as nbrProdV, 0 as MtVU, 0 as MtEAbo, 0 as MtFEnvoi, 0 as MtProduits, 0 as NbrEntPayeNonValide, sum(montant) as MtPFT, 0 as MtFEnvoiPFT, 0 as nbreAboMulti
	from panier p, panier_formule_tarif pft, panier_formule_tarif_entree pfte, users u
	where p.panier_id = pft.panier_id and pft.panier_formule_tarif_id = pfte.panier_formule_tarif_id
	AND etat in ('V') and p.commande_id is not null
	AND p.web_user_id = u.user_id 
	AND p.structure_id in ([structureCurrent])
	[whereDate]
	[wherePa]
	GROUP BY p.structure_id


	UNION /* frais multi abo  */
	select [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC,0 as nbrEAbo , 0 as nbrPVAbo
	,0 as nbrPCAbo, 0 as nbrFEV, 0 as nbrProdV, 0 as MtVU, 0 as MtEAbo, 0 as MtFEnvoi, 0 as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, sum(montant*frais) as MtFEnvoiPFT, 0 as nbreAboMulti
	from panier p, panier_formule_tarif pft, panier_formule_tarif_entree pfte, users u
	where p.panier_id = pft.panier_id and pft.panier_formule_tarif_id = pfte.panier_formule_tarif_id
	AND etat in ('V') and p.commande_id is not null
	AND p.web_user_id = u.user_id 
	AND p.structure_id in ([structureCurrent])
	[whereDate]
	[wherePa]
	GROUP BY p.structure_id


	UNION /* nbr entrées multi abo  */
	select [structureCurrent] as structure_id, 0 as nbrE, 0 as nbrPV, 0 as nbrPC,0 as nbrEAbo , 0 as nbrPVAbo
	,0 as nbrPCAbo, 0 as nbrFEV, 0 as nbrProdV, 0 as MtVU, 0 as MtEAbo, 0 as MtFEnvoi, 0 as MtProduits, 0 as NbrEntPayeNonValide, 0 as MtPFT, 0 as MtFEnvoiPFT, count(p.panier_id) as nbreAboMulti
	from panier p, panier_formule_tarif pft, panier_formule_tarif_entree pfte, users u
	where p.panier_id = pft.panier_id and pft.panier_formule_tarif_id = pfte.panier_formule_tarif_id
	AND p.structure_id in ([structureCurrent])
	AND etat in ('V') and p.commande_id is not null
	AND p.web_user_id = u.user_id 
	[whereDate]
	[wherePa]
	GROUP BY p.structure_id


) as ssreq
WHERE ssreq.structure_id in ([structureCurrent])
GROUP BY ssreq.structure_id ORDER BY ssreq.structure_id