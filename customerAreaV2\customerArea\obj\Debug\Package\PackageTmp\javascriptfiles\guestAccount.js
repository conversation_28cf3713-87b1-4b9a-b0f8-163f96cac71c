﻿$(document).ready(function() {

    $('#btnCreateGuestAccount').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var curForm = $(this).closest('#guestForm');
        if (validForm(curForm)) {
            createGuestCustomer();
        }
    });

});


function createGuestCustomer() {

    var structureid = $('#myhead').attr('structureid');
    var eventid = (!!$("#myhead").attr('eventid')) ? $("#myhead").attr('eventid') : 0;

    var ctrlh = $('#myhead').attr('ctrlh');

    var CustomerEntity = {};
    CustomerEntity.Name = $('#tbNomGuest').val() || '*GUEST';
    CustomerEntity.FirstName = $('#tbPrenomGuest').val() || '';
    CustomerEntity.Email = $('#tbEmailGuest').val() || '';
    CustomerEntity.Password = "";
    CustomerEntity.CiviliteId = "0";
    CustomerEntity.Sex = "";
    CustomerEntity.DateOfBirthday = "";
    CustomerEntity.Address1 = $('#tbAddressGuest').val() || "";
    CustomerEntity.Address2 = "";
    CustomerEntity.Address3 = "";
    CustomerEntity.Address4 = "";
    CustomerEntity.PostalCode = $('#tbPostalCodeGuest').val() || "";
    CustomerEntity.City = $('#tbCityGuest').val() || "";
    CustomerEntity.Country = $('#ddlCountryGuest').val() || "";
    CustomerEntity.HomeNum = "";
    CustomerEntity.MobileNum = $('#tbMobileNumGuest').val() || "";
    CustomerEntity.FaxNum = "";
    CustomerEntity.InfosCompChecked = "";
    CustomerEntity.InfosCompNotChecked = "";
    CustomerEntity.Hash = ctrlh;
    CustomerEntity.Comment = "";
    CustomerEntity.PhysiqueMorale = $('input[name=phymoral]:checked').val() || "P";
    CustomerEntity.IdentiteComplement = $('#tbEnterprise').val() || "";
    CustomerEntity.TitreId = $('#ddlFunction option:selected').val() || "0";



    // var structureid = numToNDigitStr($('#myhead').attr('structureid'), 4);

    var sData = JSON.stringify({ structureid: structureid, eventid: eventid, customer: CustomerEntity, isLight : false });

    $.ajax({
        type: "POST",
        url: 'login.aspx/CreateGuestCustomer',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function(data) {
            CreateMethodSuccess(data.d, "", "");
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            // ShowError("msgNotificationAlert", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, "alert alert-danger alert-dismissable", -1);
            console.log(XMLHttpRequest.responseJSON + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });
}