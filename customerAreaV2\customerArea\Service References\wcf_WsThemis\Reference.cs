﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace customerArea.wcf_WsThemis {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="ArrayOfInt", Namespace="http://tempuri.org/", ItemName="int")]
    [System.SerializableAttribute()]
    public class ArrayOfInt : System.Collections.Generic.List<int> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GestionPlaceEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class GestionPlaceEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int gestion_place_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.ReserveEntity[] listReservesField;
        
        private int EventIdField;
        
        private int SessionIdField;
        
        private int prisePlaceField;
        
        private bool isAutomatiqueField;
        
        private bool isSurPlanField;
        
        private bool isPlacementLibreField;
        
        private bool IsVoirPlaceField;
        
        private int CategoryIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CategoryNameField;
        
        private int ZoneIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ZoneNameField;
        
        private int FloorIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string FloorNameField;
        
        private int SectionIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string SectionNameField;
        
        private int PriceIdField;
        
        private int nbMaxField;
        
        private int nbMinField;
        
        private int nbSeatDispoField;
        
        private int nbMaxOnSeanceField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string hashKeyField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.PriceEntity priceEntField;
        
        private int formulaIdField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int gestion_place_id {
            get {
                return this.gestion_place_idField;
            }
            set {
                if ((this.gestion_place_idField.Equals(value) != true)) {
                    this.gestion_place_idField = value;
                    this.RaisePropertyChanged("gestion_place_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public customerArea.wcf_WsThemis.ReserveEntity[] listReserves {
            get {
                return this.listReservesField;
            }
            set {
                if ((object.ReferenceEquals(this.listReservesField, value) != true)) {
                    this.listReservesField = value;
                    this.RaisePropertyChanged("listReserves");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=2)]
        public int EventId {
            get {
                return this.EventIdField;
            }
            set {
                if ((this.EventIdField.Equals(value) != true)) {
                    this.EventIdField = value;
                    this.RaisePropertyChanged("EventId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=3)]
        public int SessionId {
            get {
                return this.SessionIdField;
            }
            set {
                if ((this.SessionIdField.Equals(value) != true)) {
                    this.SessionIdField = value;
                    this.RaisePropertyChanged("SessionId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=4)]
        public int prisePlace {
            get {
                return this.prisePlaceField;
            }
            set {
                if ((this.prisePlaceField.Equals(value) != true)) {
                    this.prisePlaceField = value;
                    this.RaisePropertyChanged("prisePlace");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=5)]
        public bool isAutomatique {
            get {
                return this.isAutomatiqueField;
            }
            set {
                if ((this.isAutomatiqueField.Equals(value) != true)) {
                    this.isAutomatiqueField = value;
                    this.RaisePropertyChanged("isAutomatique");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=6)]
        public bool isSurPlan {
            get {
                return this.isSurPlanField;
            }
            set {
                if ((this.isSurPlanField.Equals(value) != true)) {
                    this.isSurPlanField = value;
                    this.RaisePropertyChanged("isSurPlan");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=7)]
        public bool isPlacementLibre {
            get {
                return this.isPlacementLibreField;
            }
            set {
                if ((this.isPlacementLibreField.Equals(value) != true)) {
                    this.isPlacementLibreField = value;
                    this.RaisePropertyChanged("isPlacementLibre");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=8)]
        public bool IsVoirPlace {
            get {
                return this.IsVoirPlaceField;
            }
            set {
                if ((this.IsVoirPlaceField.Equals(value) != true)) {
                    this.IsVoirPlaceField = value;
                    this.RaisePropertyChanged("IsVoirPlace");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=9)]
        public int CategoryId {
            get {
                return this.CategoryIdField;
            }
            set {
                if ((this.CategoryIdField.Equals(value) != true)) {
                    this.CategoryIdField = value;
                    this.RaisePropertyChanged("CategoryId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string CategoryName {
            get {
                return this.CategoryNameField;
            }
            set {
                if ((object.ReferenceEquals(this.CategoryNameField, value) != true)) {
                    this.CategoryNameField = value;
                    this.RaisePropertyChanged("CategoryName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=11)]
        public int ZoneId {
            get {
                return this.ZoneIdField;
            }
            set {
                if ((this.ZoneIdField.Equals(value) != true)) {
                    this.ZoneIdField = value;
                    this.RaisePropertyChanged("ZoneId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string ZoneName {
            get {
                return this.ZoneNameField;
            }
            set {
                if ((object.ReferenceEquals(this.ZoneNameField, value) != true)) {
                    this.ZoneNameField = value;
                    this.RaisePropertyChanged("ZoneName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=13)]
        public int FloorId {
            get {
                return this.FloorIdField;
            }
            set {
                if ((this.FloorIdField.Equals(value) != true)) {
                    this.FloorIdField = value;
                    this.RaisePropertyChanged("FloorId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=14)]
        public string FloorName {
            get {
                return this.FloorNameField;
            }
            set {
                if ((object.ReferenceEquals(this.FloorNameField, value) != true)) {
                    this.FloorNameField = value;
                    this.RaisePropertyChanged("FloorName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=15)]
        public int SectionId {
            get {
                return this.SectionIdField;
            }
            set {
                if ((this.SectionIdField.Equals(value) != true)) {
                    this.SectionIdField = value;
                    this.RaisePropertyChanged("SectionId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=16)]
        public string SectionName {
            get {
                return this.SectionNameField;
            }
            set {
                if ((object.ReferenceEquals(this.SectionNameField, value) != true)) {
                    this.SectionNameField = value;
                    this.RaisePropertyChanged("SectionName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=17)]
        public int PriceId {
            get {
                return this.PriceIdField;
            }
            set {
                if ((this.PriceIdField.Equals(value) != true)) {
                    this.PriceIdField = value;
                    this.RaisePropertyChanged("PriceId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=18)]
        public int nbMax {
            get {
                return this.nbMaxField;
            }
            set {
                if ((this.nbMaxField.Equals(value) != true)) {
                    this.nbMaxField = value;
                    this.RaisePropertyChanged("nbMax");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=19)]
        public int nbMin {
            get {
                return this.nbMinField;
            }
            set {
                if ((this.nbMinField.Equals(value) != true)) {
                    this.nbMinField = value;
                    this.RaisePropertyChanged("nbMin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=20)]
        public int nbSeatDispo {
            get {
                return this.nbSeatDispoField;
            }
            set {
                if ((this.nbSeatDispoField.Equals(value) != true)) {
                    this.nbSeatDispoField = value;
                    this.RaisePropertyChanged("nbSeatDispo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=21)]
        public int nbMaxOnSeance {
            get {
                return this.nbMaxOnSeanceField;
            }
            set {
                if ((this.nbMaxOnSeanceField.Equals(value) != true)) {
                    this.nbMaxOnSeanceField = value;
                    this.RaisePropertyChanged("nbMaxOnSeance");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=22)]
        public string hashKey {
            get {
                return this.hashKeyField;
            }
            set {
                if ((object.ReferenceEquals(this.hashKeyField, value) != true)) {
                    this.hashKeyField = value;
                    this.RaisePropertyChanged("hashKey");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=23)]
        public customerArea.wcf_WsThemis.PriceEntity priceEnt {
            get {
                return this.priceEntField;
            }
            set {
                if ((object.ReferenceEquals(this.priceEntField, value) != true)) {
                    this.priceEntField = value;
                    this.RaisePropertyChanged("priceEnt");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=24)]
        public int formulaId {
            get {
                return this.formulaIdField;
            }
            set {
                if ((this.formulaIdField.Equals(value) != true)) {
                    this.formulaIdField = value;
                    this.RaisePropertyChanged("formulaId");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PriceEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class PriceEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int PriceIdField;
        
        private int CategoryIdField;
        
        private int VtsIdField;
        
        private int UnitTTCAmountField;
        
        private int UnitFeeAmountField;
        
        private int UnitValueField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Price_nameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Price_codeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string hashKeyField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SeatUnitSalesEntity[] ListSeatsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SeatAboMultiEntity[] ListAboMultiSeatsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SeatAboEntity[] ListAboSeatsField;
        
        private int CommissionField;
        
        private int DiscountField;
        
        private int TaxField;
        
        private int TotalTaxField;
        
        private int BookingTypeField;
        
        private int NbSeatMaxField;
        
        private int NbSeatMinField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int PriceId {
            get {
                return this.PriceIdField;
            }
            set {
                if ((this.PriceIdField.Equals(value) != true)) {
                    this.PriceIdField = value;
                    this.RaisePropertyChanged("PriceId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=1)]
        public int CategoryId {
            get {
                return this.CategoryIdField;
            }
            set {
                if ((this.CategoryIdField.Equals(value) != true)) {
                    this.CategoryIdField = value;
                    this.RaisePropertyChanged("CategoryId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=2)]
        public int VtsId {
            get {
                return this.VtsIdField;
            }
            set {
                if ((this.VtsIdField.Equals(value) != true)) {
                    this.VtsIdField = value;
                    this.RaisePropertyChanged("VtsId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=3)]
        public int UnitTTCAmount {
            get {
                return this.UnitTTCAmountField;
            }
            set {
                if ((this.UnitTTCAmountField.Equals(value) != true)) {
                    this.UnitTTCAmountField = value;
                    this.RaisePropertyChanged("UnitTTCAmount");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=4)]
        public int UnitFeeAmount {
            get {
                return this.UnitFeeAmountField;
            }
            set {
                if ((this.UnitFeeAmountField.Equals(value) != true)) {
                    this.UnitFeeAmountField = value;
                    this.RaisePropertyChanged("UnitFeeAmount");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=5)]
        public int UnitValue {
            get {
                return this.UnitValueField;
            }
            set {
                if ((this.UnitValueField.Equals(value) != true)) {
                    this.UnitValueField = value;
                    this.RaisePropertyChanged("UnitValue");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string Price_name {
            get {
                return this.Price_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Price_nameField, value) != true)) {
                    this.Price_nameField = value;
                    this.RaisePropertyChanged("Price_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string Price_code {
            get {
                return this.Price_codeField;
            }
            set {
                if ((object.ReferenceEquals(this.Price_codeField, value) != true)) {
                    this.Price_codeField = value;
                    this.RaisePropertyChanged("Price_code");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string hashKey {
            get {
                return this.hashKeyField;
            }
            set {
                if ((object.ReferenceEquals(this.hashKeyField, value) != true)) {
                    this.hashKeyField = value;
                    this.RaisePropertyChanged("hashKey");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public customerArea.wcf_WsThemis.SeatUnitSalesEntity[] ListSeats {
            get {
                return this.ListSeatsField;
            }
            set {
                if ((object.ReferenceEquals(this.ListSeatsField, value) != true)) {
                    this.ListSeatsField = value;
                    this.RaisePropertyChanged("ListSeats");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public customerArea.wcf_WsThemis.SeatAboMultiEntity[] ListAboMultiSeats {
            get {
                return this.ListAboMultiSeatsField;
            }
            set {
                if ((object.ReferenceEquals(this.ListAboMultiSeatsField, value) != true)) {
                    this.ListAboMultiSeatsField = value;
                    this.RaisePropertyChanged("ListAboMultiSeats");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public customerArea.wcf_WsThemis.SeatAboEntity[] ListAboSeats {
            get {
                return this.ListAboSeatsField;
            }
            set {
                if ((object.ReferenceEquals(this.ListAboSeatsField, value) != true)) {
                    this.ListAboSeatsField = value;
                    this.RaisePropertyChanged("ListAboSeats");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=12)]
        public int Commission {
            get {
                return this.CommissionField;
            }
            set {
                if ((this.CommissionField.Equals(value) != true)) {
                    this.CommissionField = value;
                    this.RaisePropertyChanged("Commission");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=13)]
        public int Discount {
            get {
                return this.DiscountField;
            }
            set {
                if ((this.DiscountField.Equals(value) != true)) {
                    this.DiscountField = value;
                    this.RaisePropertyChanged("Discount");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=14)]
        public int Tax {
            get {
                return this.TaxField;
            }
            set {
                if ((this.TaxField.Equals(value) != true)) {
                    this.TaxField = value;
                    this.RaisePropertyChanged("Tax");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=15)]
        public int TotalTax {
            get {
                return this.TotalTaxField;
            }
            set {
                if ((this.TotalTaxField.Equals(value) != true)) {
                    this.TotalTaxField = value;
                    this.RaisePropertyChanged("TotalTax");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=16)]
        public int BookingType {
            get {
                return this.BookingTypeField;
            }
            set {
                if ((this.BookingTypeField.Equals(value) != true)) {
                    this.BookingTypeField = value;
                    this.RaisePropertyChanged("BookingType");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=17)]
        public int NbSeatMax {
            get {
                return this.NbSeatMaxField;
            }
            set {
                if ((this.NbSeatMaxField.Equals(value) != true)) {
                    this.NbSeatMaxField = value;
                    this.RaisePropertyChanged("NbSeatMax");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=18)]
        public int NbSeatMin {
            get {
                return this.NbSeatMinField;
            }
            set {
                if ((this.NbSeatMinField.Equals(value) != true)) {
                    this.NbSeatMinField = value;
                    this.RaisePropertyChanged("NbSeatMin");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ReserveEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class ReserveEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int reserve_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string reserve_nameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string reserve_codeField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int reserve_id {
            get {
                return this.reserve_idField;
            }
            set {
                if ((this.reserve_idField.Equals(value) != true)) {
                    this.reserve_idField = value;
                    this.RaisePropertyChanged("reserve_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string reserve_name {
            get {
                return this.reserve_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.reserve_nameField, value) != true)) {
                    this.reserve_nameField = value;
                    this.RaisePropertyChanged("reserve_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string reserve_code {
            get {
                return this.reserve_codeField;
            }
            set {
                if ((object.ReferenceEquals(this.reserve_codeField, value) != true)) {
                    this.reserve_codeField = value;
                    this.RaisePropertyChanged("reserve_code");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SeatUnitSalesEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class SeatUnitSalesEntity : customerArea.wcf_WsThemis.SeatEntity {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SeatAboMultiEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class SeatAboMultiEntity : customerArea.wcf_WsThemis.SeatAboEntity {
        
        private int identity_idField;
        
        private int panier_formule_tarif_idField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int identity_id {
            get {
                return this.identity_idField;
            }
            set {
                if ((this.identity_idField.Equals(value) != true)) {
                    this.identity_idField = value;
                    this.RaisePropertyChanged("identity_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int panier_formule_tarif_id {
            get {
                return this.panier_formule_tarif_idField;
            }
            set {
                if ((this.panier_formule_tarif_idField.Equals(value) != true)) {
                    this.panier_formule_tarif_idField = value;
                    this.RaisePropertyChanged("panier_formule_tarif_id");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SeatAboEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(customerArea.wcf_WsThemis.SeatAboMultiEntity))]
    public partial class SeatAboEntity : customerArea.wcf_WsThemis.SeatEntity {
        
        private int Formula_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Formula_nameField;
        
        private int contrainteField;
        
        private int groupe_idField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int Formula_id {
            get {
                return this.Formula_idField;
            }
            set {
                if ((this.Formula_idField.Equals(value) != true)) {
                    this.Formula_idField = value;
                    this.RaisePropertyChanged("Formula_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Formula_name {
            get {
                return this.Formula_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Formula_nameField, value) != true)) {
                    this.Formula_nameField = value;
                    this.RaisePropertyChanged("Formula_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int contrainte {
            get {
                return this.contrainteField;
            }
            set {
                if ((this.contrainteField.Equals(value) != true)) {
                    this.contrainteField = value;
                    this.RaisePropertyChanged("contrainte");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int groupe_id {
            get {
                return this.groupe_idField;
            }
            set {
                if ((this.groupe_idField.Equals(value) != true)) {
                    this.groupe_idField = value;
                    this.RaisePropertyChanged("groupe_id");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BasketLineEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(customerArea.wcf_WsThemis.SeatEntity))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(customerArea.wcf_WsThemis.SeatAboEntity))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(customerArea.wcf_WsThemis.SeatAboMultiEntity))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(customerArea.wcf_WsThemis.SeatUnitSalesEntity))]
    public partial class BasketLineEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string hashKeyField;
        
        private int BasketIdField;
        
        private int BasketLineIdField;
        
        private int FraisinCentField;
        
        private int MontantTTCinCentField;
        
        private int Manif_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Event_nameField;
        
        private int Seance_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Seance_descriptionField;
        
        private int Type_envoi_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Type_envoi_nameField;
        
        private int Maquette_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string StructureidField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string StructureNameField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string hashKey {
            get {
                return this.hashKeyField;
            }
            set {
                if ((object.ReferenceEquals(this.hashKeyField, value) != true)) {
                    this.hashKeyField = value;
                    this.RaisePropertyChanged("hashKey");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=1)]
        public int BasketId {
            get {
                return this.BasketIdField;
            }
            set {
                if ((this.BasketIdField.Equals(value) != true)) {
                    this.BasketIdField = value;
                    this.RaisePropertyChanged("BasketId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=2)]
        public int BasketLineId {
            get {
                return this.BasketLineIdField;
            }
            set {
                if ((this.BasketLineIdField.Equals(value) != true)) {
                    this.BasketLineIdField = value;
                    this.RaisePropertyChanged("BasketLineId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=3)]
        public int FraisinCent {
            get {
                return this.FraisinCentField;
            }
            set {
                if ((this.FraisinCentField.Equals(value) != true)) {
                    this.FraisinCentField = value;
                    this.RaisePropertyChanged("FraisinCent");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=4)]
        public int MontantTTCinCent {
            get {
                return this.MontantTTCinCentField;
            }
            set {
                if ((this.MontantTTCinCentField.Equals(value) != true)) {
                    this.MontantTTCinCentField = value;
                    this.RaisePropertyChanged("MontantTTCinCent");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=5)]
        public int Manif_id {
            get {
                return this.Manif_idField;
            }
            set {
                if ((this.Manif_idField.Equals(value) != true)) {
                    this.Manif_idField = value;
                    this.RaisePropertyChanged("Manif_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string Event_name {
            get {
                return this.Event_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Event_nameField, value) != true)) {
                    this.Event_nameField = value;
                    this.RaisePropertyChanged("Event_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=7)]
        public int Seance_id {
            get {
                return this.Seance_idField;
            }
            set {
                if ((this.Seance_idField.Equals(value) != true)) {
                    this.Seance_idField = value;
                    this.RaisePropertyChanged("Seance_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string Seance_description {
            get {
                return this.Seance_descriptionField;
            }
            set {
                if ((object.ReferenceEquals(this.Seance_descriptionField, value) != true)) {
                    this.Seance_descriptionField = value;
                    this.RaisePropertyChanged("Seance_description");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=9)]
        public int Type_envoi_id {
            get {
                return this.Type_envoi_idField;
            }
            set {
                if ((this.Type_envoi_idField.Equals(value) != true)) {
                    this.Type_envoi_idField = value;
                    this.RaisePropertyChanged("Type_envoi_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string Type_envoi_name {
            get {
                return this.Type_envoi_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Type_envoi_nameField, value) != true)) {
                    this.Type_envoi_nameField = value;
                    this.RaisePropertyChanged("Type_envoi_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=11)]
        public int Maquette_id {
            get {
                return this.Maquette_idField;
            }
            set {
                if ((this.Maquette_idField.Equals(value) != true)) {
                    this.Maquette_idField = value;
                    this.RaisePropertyChanged("Maquette_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string Structureid {
            get {
                return this.StructureidField;
            }
            set {
                if ((object.ReferenceEquals(this.StructureidField, value) != true)) {
                    this.StructureidField = value;
                    this.RaisePropertyChanged("Structureid");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=13)]
        public string StructureName {
            get {
                return this.StructureNameField;
            }
            set {
                if ((object.ReferenceEquals(this.StructureNameField, value) != true)) {
                    this.StructureNameField = value;
                    this.RaisePropertyChanged("StructureName");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SeatEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(customerArea.wcf_WsThemis.SeatAboEntity))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(customerArea.wcf_WsThemis.SeatAboMultiEntity))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(customerArea.wcf_WsThemis.SeatUnitSalesEntity))]
    public partial class SeatEntity : customerArea.wcf_WsThemis.BasketLineEntity {
        
        private int Seat_idField;
        
        private int Vts_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string RankField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string SeatField;
        
        private int Categ_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Categ_nameField;
        
        private int Type_tarif_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Type_tarif_nameField;
        
        private int Gestion_place_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string type_siegeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Denomination_nameField;
        
        private int denom_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string orientationField;
        
        private int Floor_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Floor_nameField;
        
        private int Zone_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Zone_nameField;
        
        private int Section_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Section_nameField;
        
        private int PosxField;
        
        private int PosyField;
        
        private int IindexField;
        
        private int bordureCodeField;
        
        private int bordureTopField;
        
        private int bordureRightField;
        
        private int bordureBottomField;
        
        private int bordureLeftField;
        
        private int bordureColorField;
        
        private int Reserve_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Reserve_nameField;
        
        private int FreeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string isMineField;
        
        private bool isPlacementLibreField;
        
        private int dossier_IdField;
        
        private int ValueField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int Seat_id {
            get {
                return this.Seat_idField;
            }
            set {
                if ((this.Seat_idField.Equals(value) != true)) {
                    this.Seat_idField = value;
                    this.RaisePropertyChanged("Seat_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int Vts_id {
            get {
                return this.Vts_idField;
            }
            set {
                if ((this.Vts_idField.Equals(value) != true)) {
                    this.Vts_idField = value;
                    this.RaisePropertyChanged("Vts_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string Rank {
            get {
                return this.RankField;
            }
            set {
                if ((object.ReferenceEquals(this.RankField, value) != true)) {
                    this.RankField = value;
                    this.RaisePropertyChanged("Rank");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string Seat {
            get {
                return this.SeatField;
            }
            set {
                if ((object.ReferenceEquals(this.SeatField, value) != true)) {
                    this.SeatField = value;
                    this.RaisePropertyChanged("Seat");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=4)]
        public int Categ_id {
            get {
                return this.Categ_idField;
            }
            set {
                if ((this.Categ_idField.Equals(value) != true)) {
                    this.Categ_idField = value;
                    this.RaisePropertyChanged("Categ_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string Categ_name {
            get {
                return this.Categ_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Categ_nameField, value) != true)) {
                    this.Categ_nameField = value;
                    this.RaisePropertyChanged("Categ_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=6)]
        public int Type_tarif_id {
            get {
                return this.Type_tarif_idField;
            }
            set {
                if ((this.Type_tarif_idField.Equals(value) != true)) {
                    this.Type_tarif_idField = value;
                    this.RaisePropertyChanged("Type_tarif_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string Type_tarif_name {
            get {
                return this.Type_tarif_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Type_tarif_nameField, value) != true)) {
                    this.Type_tarif_nameField = value;
                    this.RaisePropertyChanged("Type_tarif_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=8)]
        public int Gestion_place_id {
            get {
                return this.Gestion_place_idField;
            }
            set {
                if ((this.Gestion_place_idField.Equals(value) != true)) {
                    this.Gestion_place_idField = value;
                    this.RaisePropertyChanged("Gestion_place_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string type_siege {
            get {
                return this.type_siegeField;
            }
            set {
                if ((object.ReferenceEquals(this.type_siegeField, value) != true)) {
                    this.type_siegeField = value;
                    this.RaisePropertyChanged("type_siege");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string Denomination_name {
            get {
                return this.Denomination_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Denomination_nameField, value) != true)) {
                    this.Denomination_nameField = value;
                    this.RaisePropertyChanged("Denomination_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=11)]
        public int denom_id {
            get {
                return this.denom_idField;
            }
            set {
                if ((this.denom_idField.Equals(value) != true)) {
                    this.denom_idField = value;
                    this.RaisePropertyChanged("denom_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string orientation {
            get {
                return this.orientationField;
            }
            set {
                if ((object.ReferenceEquals(this.orientationField, value) != true)) {
                    this.orientationField = value;
                    this.RaisePropertyChanged("orientation");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=13)]
        public int Floor_id {
            get {
                return this.Floor_idField;
            }
            set {
                if ((this.Floor_idField.Equals(value) != true)) {
                    this.Floor_idField = value;
                    this.RaisePropertyChanged("Floor_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=14)]
        public string Floor_name {
            get {
                return this.Floor_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Floor_nameField, value) != true)) {
                    this.Floor_nameField = value;
                    this.RaisePropertyChanged("Floor_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=15)]
        public int Zone_id {
            get {
                return this.Zone_idField;
            }
            set {
                if ((this.Zone_idField.Equals(value) != true)) {
                    this.Zone_idField = value;
                    this.RaisePropertyChanged("Zone_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=16)]
        public string Zone_name {
            get {
                return this.Zone_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Zone_nameField, value) != true)) {
                    this.Zone_nameField = value;
                    this.RaisePropertyChanged("Zone_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=17)]
        public int Section_id {
            get {
                return this.Section_idField;
            }
            set {
                if ((this.Section_idField.Equals(value) != true)) {
                    this.Section_idField = value;
                    this.RaisePropertyChanged("Section_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=18)]
        public string Section_name {
            get {
                return this.Section_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Section_nameField, value) != true)) {
                    this.Section_nameField = value;
                    this.RaisePropertyChanged("Section_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=19)]
        public int Posx {
            get {
                return this.PosxField;
            }
            set {
                if ((this.PosxField.Equals(value) != true)) {
                    this.PosxField = value;
                    this.RaisePropertyChanged("Posx");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=20)]
        public int Posy {
            get {
                return this.PosyField;
            }
            set {
                if ((this.PosyField.Equals(value) != true)) {
                    this.PosyField = value;
                    this.RaisePropertyChanged("Posy");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=21)]
        public int Iindex {
            get {
                return this.IindexField;
            }
            set {
                if ((this.IindexField.Equals(value) != true)) {
                    this.IindexField = value;
                    this.RaisePropertyChanged("Iindex");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=22)]
        public int bordureCode {
            get {
                return this.bordureCodeField;
            }
            set {
                if ((this.bordureCodeField.Equals(value) != true)) {
                    this.bordureCodeField = value;
                    this.RaisePropertyChanged("bordureCode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=23)]
        public int bordureTop {
            get {
                return this.bordureTopField;
            }
            set {
                if ((this.bordureTopField.Equals(value) != true)) {
                    this.bordureTopField = value;
                    this.RaisePropertyChanged("bordureTop");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=24)]
        public int bordureRight {
            get {
                return this.bordureRightField;
            }
            set {
                if ((this.bordureRightField.Equals(value) != true)) {
                    this.bordureRightField = value;
                    this.RaisePropertyChanged("bordureRight");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=25)]
        public int bordureBottom {
            get {
                return this.bordureBottomField;
            }
            set {
                if ((this.bordureBottomField.Equals(value) != true)) {
                    this.bordureBottomField = value;
                    this.RaisePropertyChanged("bordureBottom");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=26)]
        public int bordureLeft {
            get {
                return this.bordureLeftField;
            }
            set {
                if ((this.bordureLeftField.Equals(value) != true)) {
                    this.bordureLeftField = value;
                    this.RaisePropertyChanged("bordureLeft");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=27)]
        public int bordureColor {
            get {
                return this.bordureColorField;
            }
            set {
                if ((this.bordureColorField.Equals(value) != true)) {
                    this.bordureColorField = value;
                    this.RaisePropertyChanged("bordureColor");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=28)]
        public int Reserve_id {
            get {
                return this.Reserve_idField;
            }
            set {
                if ((this.Reserve_idField.Equals(value) != true)) {
                    this.Reserve_idField = value;
                    this.RaisePropertyChanged("Reserve_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=29)]
        public string Reserve_name {
            get {
                return this.Reserve_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Reserve_nameField, value) != true)) {
                    this.Reserve_nameField = value;
                    this.RaisePropertyChanged("Reserve_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=30)]
        public int Free {
            get {
                return this.FreeField;
            }
            set {
                if ((this.FreeField.Equals(value) != true)) {
                    this.FreeField = value;
                    this.RaisePropertyChanged("Free");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=31)]
        public string isMine {
            get {
                return this.isMineField;
            }
            set {
                if ((object.ReferenceEquals(this.isMineField, value) != true)) {
                    this.isMineField = value;
                    this.RaisePropertyChanged("isMine");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=32)]
        public bool isPlacementLibre {
            get {
                return this.isPlacementLibreField;
            }
            set {
                if ((this.isPlacementLibreField.Equals(value) != true)) {
                    this.isPlacementLibreField = value;
                    this.RaisePropertyChanged("isPlacementLibre");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=33)]
        public int dossier_Id {
            get {
                return this.dossier_IdField;
            }
            set {
                if ((this.dossier_IdField.Equals(value) != true)) {
                    this.dossier_IdField = value;
                    this.RaisePropertyChanged("dossier_Id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=34)]
        public int Value {
            get {
                return this.ValueField;
            }
            set {
                if ((this.ValueField.Equals(value) != true)) {
                    this.ValueField = value;
                    this.RaisePropertyChanged("Value");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EventEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class EventEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int EventIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Structure_idField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Structure_nameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventNameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventCodeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventDescription1Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventDescription2Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventDescription3Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventDescription4Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventDescription5Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SessionEntity[] ListSessionsField;
        
        private int EventGroupIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.EventsGroupsEntity EventGroupField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string HasContraintesParametreesField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.LieuEntity LieuField;
        
        private int EventSousGenreIdField;
        
        private int EventGenreIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.CibleEntity[] LstCiblesField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.EventInfo EventInfoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.EventsGenreEntity GenreField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.EventsSousGenreEntity SousGenreField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int EventId {
            get {
                return this.EventIdField;
            }
            set {
                if ((this.EventIdField.Equals(value) != true)) {
                    this.EventIdField = value;
                    this.RaisePropertyChanged("EventId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Structure_id {
            get {
                return this.Structure_idField;
            }
            set {
                if ((object.ReferenceEquals(this.Structure_idField, value) != true)) {
                    this.Structure_idField = value;
                    this.RaisePropertyChanged("Structure_id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Structure_name {
            get {
                return this.Structure_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Structure_nameField, value) != true)) {
                    this.Structure_nameField = value;
                    this.RaisePropertyChanged("Structure_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string EventName {
            get {
                return this.EventNameField;
            }
            set {
                if ((object.ReferenceEquals(this.EventNameField, value) != true)) {
                    this.EventNameField = value;
                    this.RaisePropertyChanged("EventName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string EventCode {
            get {
                return this.EventCodeField;
            }
            set {
                if ((object.ReferenceEquals(this.EventCodeField, value) != true)) {
                    this.EventCodeField = value;
                    this.RaisePropertyChanged("EventCode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string EventDescription1 {
            get {
                return this.EventDescription1Field;
            }
            set {
                if ((object.ReferenceEquals(this.EventDescription1Field, value) != true)) {
                    this.EventDescription1Field = value;
                    this.RaisePropertyChanged("EventDescription1");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string EventDescription2 {
            get {
                return this.EventDescription2Field;
            }
            set {
                if ((object.ReferenceEquals(this.EventDescription2Field, value) != true)) {
                    this.EventDescription2Field = value;
                    this.RaisePropertyChanged("EventDescription2");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string EventDescription3 {
            get {
                return this.EventDescription3Field;
            }
            set {
                if ((object.ReferenceEquals(this.EventDescription3Field, value) != true)) {
                    this.EventDescription3Field = value;
                    this.RaisePropertyChanged("EventDescription3");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string EventDescription4 {
            get {
                return this.EventDescription4Field;
            }
            set {
                if ((object.ReferenceEquals(this.EventDescription4Field, value) != true)) {
                    this.EventDescription4Field = value;
                    this.RaisePropertyChanged("EventDescription4");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string EventDescription5 {
            get {
                return this.EventDescription5Field;
            }
            set {
                if ((object.ReferenceEquals(this.EventDescription5Field, value) != true)) {
                    this.EventDescription5Field = value;
                    this.RaisePropertyChanged("EventDescription5");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public customerArea.wcf_WsThemis.SessionEntity[] ListSessions {
            get {
                return this.ListSessionsField;
            }
            set {
                if ((object.ReferenceEquals(this.ListSessionsField, value) != true)) {
                    this.ListSessionsField = value;
                    this.RaisePropertyChanged("ListSessions");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=11)]
        public int EventGroupId {
            get {
                return this.EventGroupIdField;
            }
            set {
                if ((this.EventGroupIdField.Equals(value) != true)) {
                    this.EventGroupIdField = value;
                    this.RaisePropertyChanged("EventGroupId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public customerArea.wcf_WsThemis.EventsGroupsEntity EventGroup {
            get {
                return this.EventGroupField;
            }
            set {
                if ((object.ReferenceEquals(this.EventGroupField, value) != true)) {
                    this.EventGroupField = value;
                    this.RaisePropertyChanged("EventGroup");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=13)]
        public string HasContraintesParametrees {
            get {
                return this.HasContraintesParametreesField;
            }
            set {
                if ((object.ReferenceEquals(this.HasContraintesParametreesField, value) != true)) {
                    this.HasContraintesParametreesField = value;
                    this.RaisePropertyChanged("HasContraintesParametrees");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=14)]
        public customerArea.wcf_WsThemis.LieuEntity Lieu {
            get {
                return this.LieuField;
            }
            set {
                if ((object.ReferenceEquals(this.LieuField, value) != true)) {
                    this.LieuField = value;
                    this.RaisePropertyChanged("Lieu");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=15)]
        public int EventSousGenreId {
            get {
                return this.EventSousGenreIdField;
            }
            set {
                if ((this.EventSousGenreIdField.Equals(value) != true)) {
                    this.EventSousGenreIdField = value;
                    this.RaisePropertyChanged("EventSousGenreId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=16)]
        public int EventGenreId {
            get {
                return this.EventGenreIdField;
            }
            set {
                if ((this.EventGenreIdField.Equals(value) != true)) {
                    this.EventGenreIdField = value;
                    this.RaisePropertyChanged("EventGenreId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=17)]
        public customerArea.wcf_WsThemis.CibleEntity[] LstCibles {
            get {
                return this.LstCiblesField;
            }
            set {
                if ((object.ReferenceEquals(this.LstCiblesField, value) != true)) {
                    this.LstCiblesField = value;
                    this.RaisePropertyChanged("LstCibles");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=18)]
        public customerArea.wcf_WsThemis.EventInfo EventInfo {
            get {
                return this.EventInfoField;
            }
            set {
                if ((object.ReferenceEquals(this.EventInfoField, value) != true)) {
                    this.EventInfoField = value;
                    this.RaisePropertyChanged("EventInfo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=19)]
        public customerArea.wcf_WsThemis.EventsGenreEntity Genre {
            get {
                return this.GenreField;
            }
            set {
                if ((object.ReferenceEquals(this.GenreField, value) != true)) {
                    this.GenreField = value;
                    this.RaisePropertyChanged("Genre");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=20)]
        public customerArea.wcf_WsThemis.EventsSousGenreEntity SousGenre {
            get {
                return this.SousGenreField;
            }
            set {
                if ((object.ReferenceEquals(this.SousGenreField, value) != true)) {
                    this.SousGenreField = value;
                    this.RaisePropertyChanged("SousGenre");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EventsGroupsEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class EventsGroupsEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int EventGroupIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventGroupNameField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int EventGroupId {
            get {
                return this.EventGroupIdField;
            }
            set {
                if ((this.EventGroupIdField.Equals(value) != true)) {
                    this.EventGroupIdField = value;
                    this.RaisePropertyChanged("EventGroupId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string EventGroupName {
            get {
                return this.EventGroupNameField;
            }
            set {
                if ((object.ReferenceEquals(this.EventGroupNameField, value) != true)) {
                    this.EventGroupNameField = value;
                    this.RaisePropertyChanged("EventGroupName");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LieuEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class LieuEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int LieuIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string LieuNomField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string PlaceCodeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.EventEntity[] LstEventsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SessionEntity[] LstSessionsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Address_address1Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Address_address2Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Address_address3Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Address_address4Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Address_postal_codeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Address_cityField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Address_countryField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int LieuId {
            get {
                return this.LieuIdField;
            }
            set {
                if ((this.LieuIdField.Equals(value) != true)) {
                    this.LieuIdField = value;
                    this.RaisePropertyChanged("LieuId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string LieuNom {
            get {
                return this.LieuNomField;
            }
            set {
                if ((object.ReferenceEquals(this.LieuNomField, value) != true)) {
                    this.LieuNomField = value;
                    this.RaisePropertyChanged("LieuNom");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string PlaceCode {
            get {
                return this.PlaceCodeField;
            }
            set {
                if ((object.ReferenceEquals(this.PlaceCodeField, value) != true)) {
                    this.PlaceCodeField = value;
                    this.RaisePropertyChanged("PlaceCode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public customerArea.wcf_WsThemis.EventEntity[] LstEvents {
            get {
                return this.LstEventsField;
            }
            set {
                if ((object.ReferenceEquals(this.LstEventsField, value) != true)) {
                    this.LstEventsField = value;
                    this.RaisePropertyChanged("LstEvents");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public customerArea.wcf_WsThemis.SessionEntity[] LstSessions {
            get {
                return this.LstSessionsField;
            }
            set {
                if ((object.ReferenceEquals(this.LstSessionsField, value) != true)) {
                    this.LstSessionsField = value;
                    this.RaisePropertyChanged("LstSessions");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string Address_address1 {
            get {
                return this.Address_address1Field;
            }
            set {
                if ((object.ReferenceEquals(this.Address_address1Field, value) != true)) {
                    this.Address_address1Field = value;
                    this.RaisePropertyChanged("Address_address1");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string Address_address2 {
            get {
                return this.Address_address2Field;
            }
            set {
                if ((object.ReferenceEquals(this.Address_address2Field, value) != true)) {
                    this.Address_address2Field = value;
                    this.RaisePropertyChanged("Address_address2");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string Address_address3 {
            get {
                return this.Address_address3Field;
            }
            set {
                if ((object.ReferenceEquals(this.Address_address3Field, value) != true)) {
                    this.Address_address3Field = value;
                    this.RaisePropertyChanged("Address_address3");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string Address_address4 {
            get {
                return this.Address_address4Field;
            }
            set {
                if ((object.ReferenceEquals(this.Address_address4Field, value) != true)) {
                    this.Address_address4Field = value;
                    this.RaisePropertyChanged("Address_address4");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string Address_postal_code {
            get {
                return this.Address_postal_codeField;
            }
            set {
                if ((object.ReferenceEquals(this.Address_postal_codeField, value) != true)) {
                    this.Address_postal_codeField = value;
                    this.RaisePropertyChanged("Address_postal_code");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string Address_city {
            get {
                return this.Address_cityField;
            }
            set {
                if ((object.ReferenceEquals(this.Address_cityField, value) != true)) {
                    this.Address_cityField = value;
                    this.RaisePropertyChanged("Address_city");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public string Address_country {
            get {
                return this.Address_countryField;
            }
            set {
                if ((object.ReferenceEquals(this.Address_countryField, value) != true)) {
                    this.Address_countryField = value;
                    this.RaisePropertyChanged("Address_country");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EventInfo", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class EventInfo : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int EventIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventResumeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventStagingField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventDistributionField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventDurationField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EventScheduleField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int EventId {
            get {
                return this.EventIdField;
            }
            set {
                if ((this.EventIdField.Equals(value) != true)) {
                    this.EventIdField = value;
                    this.RaisePropertyChanged("EventId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string EventResume {
            get {
                return this.EventResumeField;
            }
            set {
                if ((object.ReferenceEquals(this.EventResumeField, value) != true)) {
                    this.EventResumeField = value;
                    this.RaisePropertyChanged("EventResume");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string EventStaging {
            get {
                return this.EventStagingField;
            }
            set {
                if ((object.ReferenceEquals(this.EventStagingField, value) != true)) {
                    this.EventStagingField = value;
                    this.RaisePropertyChanged("EventStaging");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string EventDistribution {
            get {
                return this.EventDistributionField;
            }
            set {
                if ((object.ReferenceEquals(this.EventDistributionField, value) != true)) {
                    this.EventDistributionField = value;
                    this.RaisePropertyChanged("EventDistribution");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string EventDuration {
            get {
                return this.EventDurationField;
            }
            set {
                if ((object.ReferenceEquals(this.EventDurationField, value) != true)) {
                    this.EventDurationField = value;
                    this.RaisePropertyChanged("EventDuration");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string EventSchedule {
            get {
                return this.EventScheduleField;
            }
            set {
                if ((object.ReferenceEquals(this.EventScheduleField, value) != true)) {
                    this.EventScheduleField = value;
                    this.RaisePropertyChanged("EventSchedule");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EventsGenreEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class EventsGenreEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int GenreIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string GenreNameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string GenreCodeField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int GenreId {
            get {
                return this.GenreIdField;
            }
            set {
                if ((this.GenreIdField.Equals(value) != true)) {
                    this.GenreIdField = value;
                    this.RaisePropertyChanged("GenreId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string GenreName {
            get {
                return this.GenreNameField;
            }
            set {
                if ((object.ReferenceEquals(this.GenreNameField, value) != true)) {
                    this.GenreNameField = value;
                    this.RaisePropertyChanged("GenreName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string GenreCode {
            get {
                return this.GenreCodeField;
            }
            set {
                if ((object.ReferenceEquals(this.GenreCodeField, value) != true)) {
                    this.GenreCodeField = value;
                    this.RaisePropertyChanged("GenreCode");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EventsSousGenreEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class EventsSousGenreEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int SousGenreIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string SousGenreNameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string SousGenreCodeField;
        
        private int GenreIdField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int SousGenreId {
            get {
                return this.SousGenreIdField;
            }
            set {
                if ((this.SousGenreIdField.Equals(value) != true)) {
                    this.SousGenreIdField = value;
                    this.RaisePropertyChanged("SousGenreId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string SousGenreName {
            get {
                return this.SousGenreNameField;
            }
            set {
                if ((object.ReferenceEquals(this.SousGenreNameField, value) != true)) {
                    this.SousGenreNameField = value;
                    this.RaisePropertyChanged("SousGenreName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string SousGenreCode {
            get {
                return this.SousGenreCodeField;
            }
            set {
                if ((object.ReferenceEquals(this.SousGenreCodeField, value) != true)) {
                    this.SousGenreCodeField = value;
                    this.RaisePropertyChanged("SousGenreCode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=3)]
        public int GenreId {
            get {
                return this.GenreIdField;
            }
            set {
                if ((this.GenreIdField.Equals(value) != true)) {
                    this.GenreIdField = value;
                    this.RaisePropertyChanged("GenreId");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SessionEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class SessionEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int SessionIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string SessionDecriptionField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.CategoryEntity[] ListCategoriesField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SeatUnitSalesEntity[] ListSeatsUnitSalesField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SeatAboEntity[] ListSeatsAboField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SeatAboMultiEntity[] ListSeatsAboMultiField;
        
        private System.DateTime SessionStartDateField;
        
        private System.DateTime SessionEndDateField;
        
        private System.DateTime sessionStartSaleDateField;
        
        private System.DateTime sessionEndSaleDateField;
        
        private bool isForSaleField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string sSessionStartDateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string sSessionEndDateField;
        
        private int EventIdField;
        
        private int dispoField;
        
        private int dispoTotalField;
        
        private int lieuIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuNameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuCodeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuRue1Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuRue2Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuRue3Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuRue4Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuCodePostalField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuVilleField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuRegionField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuPaysField;
        
        private int lieuConfigIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string lieuConfigNameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string tva_nameField;
        
        private decimal tva_tauxField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.ZoneEntity[] listZonesField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.GestionPlaceEntity[] listGpsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.EventEntity eventOfThisField;
        
        private int ciblesIdPower2Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.ArrayOfInt ciblesField;
        
        private bool IsLockField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.LigneGrilleTarifEntity[] listLigneGrilleTarifField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int SessionId {
            get {
                return this.SessionIdField;
            }
            set {
                if ((this.SessionIdField.Equals(value) != true)) {
                    this.SessionIdField = value;
                    this.RaisePropertyChanged("SessionId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string SessionDecription {
            get {
                return this.SessionDecriptionField;
            }
            set {
                if ((object.ReferenceEquals(this.SessionDecriptionField, value) != true)) {
                    this.SessionDecriptionField = value;
                    this.RaisePropertyChanged("SessionDecription");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public customerArea.wcf_WsThemis.CategoryEntity[] ListCategories {
            get {
                return this.ListCategoriesField;
            }
            set {
                if ((object.ReferenceEquals(this.ListCategoriesField, value) != true)) {
                    this.ListCategoriesField = value;
                    this.RaisePropertyChanged("ListCategories");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public customerArea.wcf_WsThemis.SeatUnitSalesEntity[] ListSeatsUnitSales {
            get {
                return this.ListSeatsUnitSalesField;
            }
            set {
                if ((object.ReferenceEquals(this.ListSeatsUnitSalesField, value) != true)) {
                    this.ListSeatsUnitSalesField = value;
                    this.RaisePropertyChanged("ListSeatsUnitSales");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public customerArea.wcf_WsThemis.SeatAboEntity[] ListSeatsAbo {
            get {
                return this.ListSeatsAboField;
            }
            set {
                if ((object.ReferenceEquals(this.ListSeatsAboField, value) != true)) {
                    this.ListSeatsAboField = value;
                    this.RaisePropertyChanged("ListSeatsAbo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public customerArea.wcf_WsThemis.SeatAboMultiEntity[] ListSeatsAboMulti {
            get {
                return this.ListSeatsAboMultiField;
            }
            set {
                if ((object.ReferenceEquals(this.ListSeatsAboMultiField, value) != true)) {
                    this.ListSeatsAboMultiField = value;
                    this.RaisePropertyChanged("ListSeatsAboMulti");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=6)]
        public System.DateTime SessionStartDate {
            get {
                return this.SessionStartDateField;
            }
            set {
                if ((this.SessionStartDateField.Equals(value) != true)) {
                    this.SessionStartDateField = value;
                    this.RaisePropertyChanged("SessionStartDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=7)]
        public System.DateTime SessionEndDate {
            get {
                return this.SessionEndDateField;
            }
            set {
                if ((this.SessionEndDateField.Equals(value) != true)) {
                    this.SessionEndDateField = value;
                    this.RaisePropertyChanged("SessionEndDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=8)]
        public System.DateTime sessionStartSaleDate {
            get {
                return this.sessionStartSaleDateField;
            }
            set {
                if ((this.sessionStartSaleDateField.Equals(value) != true)) {
                    this.sessionStartSaleDateField = value;
                    this.RaisePropertyChanged("sessionStartSaleDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=9)]
        public System.DateTime sessionEndSaleDate {
            get {
                return this.sessionEndSaleDateField;
            }
            set {
                if ((this.sessionEndSaleDateField.Equals(value) != true)) {
                    this.sessionEndSaleDateField = value;
                    this.RaisePropertyChanged("sessionEndSaleDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=10)]
        public bool isForSale {
            get {
                return this.isForSaleField;
            }
            set {
                if ((this.isForSaleField.Equals(value) != true)) {
                    this.isForSaleField = value;
                    this.RaisePropertyChanged("isForSale");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public string sSessionStartDate {
            get {
                return this.sSessionStartDateField;
            }
            set {
                if ((object.ReferenceEquals(this.sSessionStartDateField, value) != true)) {
                    this.sSessionStartDateField = value;
                    this.RaisePropertyChanged("sSessionStartDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string sSessionEndDate {
            get {
                return this.sSessionEndDateField;
            }
            set {
                if ((object.ReferenceEquals(this.sSessionEndDateField, value) != true)) {
                    this.sSessionEndDateField = value;
                    this.RaisePropertyChanged("sSessionEndDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=13)]
        public int EventId {
            get {
                return this.EventIdField;
            }
            set {
                if ((this.EventIdField.Equals(value) != true)) {
                    this.EventIdField = value;
                    this.RaisePropertyChanged("EventId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=14)]
        public int dispo {
            get {
                return this.dispoField;
            }
            set {
                if ((this.dispoField.Equals(value) != true)) {
                    this.dispoField = value;
                    this.RaisePropertyChanged("dispo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=15)]
        public int dispoTotal {
            get {
                return this.dispoTotalField;
            }
            set {
                if ((this.dispoTotalField.Equals(value) != true)) {
                    this.dispoTotalField = value;
                    this.RaisePropertyChanged("dispoTotal");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=16)]
        public int lieuId {
            get {
                return this.lieuIdField;
            }
            set {
                if ((this.lieuIdField.Equals(value) != true)) {
                    this.lieuIdField = value;
                    this.RaisePropertyChanged("lieuId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=17)]
        public string lieuName {
            get {
                return this.lieuNameField;
            }
            set {
                if ((object.ReferenceEquals(this.lieuNameField, value) != true)) {
                    this.lieuNameField = value;
                    this.RaisePropertyChanged("lieuName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=18)]
        public string lieuCode {
            get {
                return this.lieuCodeField;
            }
            set {
                if ((object.ReferenceEquals(this.lieuCodeField, value) != true)) {
                    this.lieuCodeField = value;
                    this.RaisePropertyChanged("lieuCode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=19)]
        public string lieuRue1 {
            get {
                return this.lieuRue1Field;
            }
            set {
                if ((object.ReferenceEquals(this.lieuRue1Field, value) != true)) {
                    this.lieuRue1Field = value;
                    this.RaisePropertyChanged("lieuRue1");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=20)]
        public string lieuRue2 {
            get {
                return this.lieuRue2Field;
            }
            set {
                if ((object.ReferenceEquals(this.lieuRue2Field, value) != true)) {
                    this.lieuRue2Field = value;
                    this.RaisePropertyChanged("lieuRue2");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=21)]
        public string lieuRue3 {
            get {
                return this.lieuRue3Field;
            }
            set {
                if ((object.ReferenceEquals(this.lieuRue3Field, value) != true)) {
                    this.lieuRue3Field = value;
                    this.RaisePropertyChanged("lieuRue3");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=22)]
        public string lieuRue4 {
            get {
                return this.lieuRue4Field;
            }
            set {
                if ((object.ReferenceEquals(this.lieuRue4Field, value) != true)) {
                    this.lieuRue4Field = value;
                    this.RaisePropertyChanged("lieuRue4");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=23)]
        public string lieuCodePostal {
            get {
                return this.lieuCodePostalField;
            }
            set {
                if ((object.ReferenceEquals(this.lieuCodePostalField, value) != true)) {
                    this.lieuCodePostalField = value;
                    this.RaisePropertyChanged("lieuCodePostal");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=24)]
        public string lieuVille {
            get {
                return this.lieuVilleField;
            }
            set {
                if ((object.ReferenceEquals(this.lieuVilleField, value) != true)) {
                    this.lieuVilleField = value;
                    this.RaisePropertyChanged("lieuVille");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=25)]
        public string lieuRegion {
            get {
                return this.lieuRegionField;
            }
            set {
                if ((object.ReferenceEquals(this.lieuRegionField, value) != true)) {
                    this.lieuRegionField = value;
                    this.RaisePropertyChanged("lieuRegion");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=26)]
        public string lieuPays {
            get {
                return this.lieuPaysField;
            }
            set {
                if ((object.ReferenceEquals(this.lieuPaysField, value) != true)) {
                    this.lieuPaysField = value;
                    this.RaisePropertyChanged("lieuPays");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=27)]
        public int lieuConfigId {
            get {
                return this.lieuConfigIdField;
            }
            set {
                if ((this.lieuConfigIdField.Equals(value) != true)) {
                    this.lieuConfigIdField = value;
                    this.RaisePropertyChanged("lieuConfigId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=28)]
        public string lieuConfigName {
            get {
                return this.lieuConfigNameField;
            }
            set {
                if ((object.ReferenceEquals(this.lieuConfigNameField, value) != true)) {
                    this.lieuConfigNameField = value;
                    this.RaisePropertyChanged("lieuConfigName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=29)]
        public string tva_name {
            get {
                return this.tva_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.tva_nameField, value) != true)) {
                    this.tva_nameField = value;
                    this.RaisePropertyChanged("tva_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=30)]
        public decimal tva_taux {
            get {
                return this.tva_tauxField;
            }
            set {
                if ((this.tva_tauxField.Equals(value) != true)) {
                    this.tva_tauxField = value;
                    this.RaisePropertyChanged("tva_taux");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=31)]
        public customerArea.wcf_WsThemis.ZoneEntity[] listZones {
            get {
                return this.listZonesField;
            }
            set {
                if ((object.ReferenceEquals(this.listZonesField, value) != true)) {
                    this.listZonesField = value;
                    this.RaisePropertyChanged("listZones");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=32)]
        public customerArea.wcf_WsThemis.GestionPlaceEntity[] listGps {
            get {
                return this.listGpsField;
            }
            set {
                if ((object.ReferenceEquals(this.listGpsField, value) != true)) {
                    this.listGpsField = value;
                    this.RaisePropertyChanged("listGps");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=33)]
        public customerArea.wcf_WsThemis.EventEntity eventOfThis {
            get {
                return this.eventOfThisField;
            }
            set {
                if ((object.ReferenceEquals(this.eventOfThisField, value) != true)) {
                    this.eventOfThisField = value;
                    this.RaisePropertyChanged("eventOfThis");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=34)]
        public int ciblesIdPower2 {
            get {
                return this.ciblesIdPower2Field;
            }
            set {
                if ((this.ciblesIdPower2Field.Equals(value) != true)) {
                    this.ciblesIdPower2Field = value;
                    this.RaisePropertyChanged("ciblesIdPower2");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=35)]
        public customerArea.wcf_WsThemis.ArrayOfInt cibles {
            get {
                return this.ciblesField;
            }
            set {
                if ((object.ReferenceEquals(this.ciblesField, value) != true)) {
                    this.ciblesField = value;
                    this.RaisePropertyChanged("cibles");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=36)]
        public bool IsLock {
            get {
                return this.IsLockField;
            }
            set {
                if ((this.IsLockField.Equals(value) != true)) {
                    this.IsLockField = value;
                    this.RaisePropertyChanged("IsLock");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=37)]
        public customerArea.wcf_WsThemis.LigneGrilleTarifEntity[] listLigneGrilleTarif {
            get {
                return this.listLigneGrilleTarifField;
            }
            set {
                if ((object.ReferenceEquals(this.listLigneGrilleTarifField, value) != true)) {
                    this.listLigneGrilleTarifField = value;
                    this.RaisePropertyChanged("listLigneGrilleTarif");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CibleEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class CibleEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int cibleIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string cibleNameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string cibleCodeField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int cibleId {
            get {
                return this.cibleIdField;
            }
            set {
                if ((this.cibleIdField.Equals(value) != true)) {
                    this.cibleIdField = value;
                    this.RaisePropertyChanged("cibleId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string cibleName {
            get {
                return this.cibleNameField;
            }
            set {
                if ((object.ReferenceEquals(this.cibleNameField, value) != true)) {
                    this.cibleNameField = value;
                    this.RaisePropertyChanged("cibleName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string cibleCode {
            get {
                return this.cibleCodeField;
            }
            set {
                if ((object.ReferenceEquals(this.cibleCodeField, value) != true)) {
                    this.cibleCodeField = value;
                    this.RaisePropertyChanged("cibleCode");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CategoryEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class CategoryEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int CategIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string Category_nameField;
        
        private int dispoField;
        
        private int dispoTotalField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.GestionPlaceEntity[] listGestionPlaceField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.PriceEntity[] ListPricesField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CategoryCodeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.LigneChoixPrixEntity LigneChoixPrixField;
        
        private int ZoneIdField;
        
        private int FloorIdField;
        
        private int SectionIdField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int CategId {
            get {
                return this.CategIdField;
            }
            set {
                if ((this.CategIdField.Equals(value) != true)) {
                    this.CategIdField = value;
                    this.RaisePropertyChanged("CategId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Category_name {
            get {
                return this.Category_nameField;
            }
            set {
                if ((object.ReferenceEquals(this.Category_nameField, value) != true)) {
                    this.Category_nameField = value;
                    this.RaisePropertyChanged("Category_name");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int dispo {
            get {
                return this.dispoField;
            }
            set {
                if ((this.dispoField.Equals(value) != true)) {
                    this.dispoField = value;
                    this.RaisePropertyChanged("dispo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int dispoTotal {
            get {
                return this.dispoTotalField;
            }
            set {
                if ((this.dispoTotalField.Equals(value) != true)) {
                    this.dispoTotalField = value;
                    this.RaisePropertyChanged("dispoTotal");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public customerArea.wcf_WsThemis.GestionPlaceEntity[] listGestionPlace {
            get {
                return this.listGestionPlaceField;
            }
            set {
                if ((object.ReferenceEquals(this.listGestionPlaceField, value) != true)) {
                    this.listGestionPlaceField = value;
                    this.RaisePropertyChanged("listGestionPlace");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public customerArea.wcf_WsThemis.PriceEntity[] ListPrices {
            get {
                return this.ListPricesField;
            }
            set {
                if ((object.ReferenceEquals(this.ListPricesField, value) != true)) {
                    this.ListPricesField = value;
                    this.RaisePropertyChanged("ListPrices");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string CategoryCode {
            get {
                return this.CategoryCodeField;
            }
            set {
                if ((object.ReferenceEquals(this.CategoryCodeField, value) != true)) {
                    this.CategoryCodeField = value;
                    this.RaisePropertyChanged("CategoryCode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public customerArea.wcf_WsThemis.LigneChoixPrixEntity LigneChoixPrix {
            get {
                return this.LigneChoixPrixField;
            }
            set {
                if ((object.ReferenceEquals(this.LigneChoixPrixField, value) != true)) {
                    this.LigneChoixPrixField = value;
                    this.RaisePropertyChanged("LigneChoixPrix");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=8)]
        public int ZoneId {
            get {
                return this.ZoneIdField;
            }
            set {
                if ((this.ZoneIdField.Equals(value) != true)) {
                    this.ZoneIdField = value;
                    this.RaisePropertyChanged("ZoneId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=9)]
        public int FloorId {
            get {
                return this.FloorIdField;
            }
            set {
                if ((this.FloorIdField.Equals(value) != true)) {
                    this.FloorIdField = value;
                    this.RaisePropertyChanged("FloorId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=10)]
        public int SectionId {
            get {
                return this.SectionIdField;
            }
            set {
                if ((this.SectionIdField.Equals(value) != true)) {
                    this.SectionIdField = value;
                    this.RaisePropertyChanged("SectionId");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ZoneEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class ZoneEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int ZoneIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ZoneNameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.FloorEntity[] listFloorsField;
        
        private int DispoTotaleField;
        
        private int DispoField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int ZoneId {
            get {
                return this.ZoneIdField;
            }
            set {
                if ((this.ZoneIdField.Equals(value) != true)) {
                    this.ZoneIdField = value;
                    this.RaisePropertyChanged("ZoneId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string ZoneName {
            get {
                return this.ZoneNameField;
            }
            set {
                if ((object.ReferenceEquals(this.ZoneNameField, value) != true)) {
                    this.ZoneNameField = value;
                    this.RaisePropertyChanged("ZoneName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public customerArea.wcf_WsThemis.FloorEntity[] listFloors {
            get {
                return this.listFloorsField;
            }
            set {
                if ((object.ReferenceEquals(this.listFloorsField, value) != true)) {
                    this.listFloorsField = value;
                    this.RaisePropertyChanged("listFloors");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=3)]
        public int DispoTotale {
            get {
                return this.DispoTotaleField;
            }
            set {
                if ((this.DispoTotaleField.Equals(value) != true)) {
                    this.DispoTotaleField = value;
                    this.RaisePropertyChanged("DispoTotale");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=4)]
        public int Dispo {
            get {
                return this.DispoField;
            }
            set {
                if ((this.DispoField.Equals(value) != true)) {
                    this.DispoField = value;
                    this.RaisePropertyChanged("Dispo");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LigneGrilleTarifEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class LigneGrilleTarifEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int VtsIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.PriceEntity PriceField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.CategoryEntity CategoryField;
        
        private int vts_Grille1centsField;
        
        private int eventIdField;
        
        private int amountTTCcentsField;
        
        private int amountMaxTTCcentsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SessionEntity sessionField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SeatEntity[] ListSeatsField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int VtsId {
            get {
                return this.VtsIdField;
            }
            set {
                if ((this.VtsIdField.Equals(value) != true)) {
                    this.VtsIdField = value;
                    this.RaisePropertyChanged("VtsId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public customerArea.wcf_WsThemis.PriceEntity Price {
            get {
                return this.PriceField;
            }
            set {
                if ((object.ReferenceEquals(this.PriceField, value) != true)) {
                    this.PriceField = value;
                    this.RaisePropertyChanged("Price");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public customerArea.wcf_WsThemis.CategoryEntity Category {
            get {
                return this.CategoryField;
            }
            set {
                if ((object.ReferenceEquals(this.CategoryField, value) != true)) {
                    this.CategoryField = value;
                    this.RaisePropertyChanged("Category");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=3)]
        public int vts_Grille1cents {
            get {
                return this.vts_Grille1centsField;
            }
            set {
                if ((this.vts_Grille1centsField.Equals(value) != true)) {
                    this.vts_Grille1centsField = value;
                    this.RaisePropertyChanged("vts_Grille1cents");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=4)]
        public int eventId {
            get {
                return this.eventIdField;
            }
            set {
                if ((this.eventIdField.Equals(value) != true)) {
                    this.eventIdField = value;
                    this.RaisePropertyChanged("eventId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=5)]
        public int amountTTCcents {
            get {
                return this.amountTTCcentsField;
            }
            set {
                if ((this.amountTTCcentsField.Equals(value) != true)) {
                    this.amountTTCcentsField = value;
                    this.RaisePropertyChanged("amountTTCcents");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=6)]
        public int amountMaxTTCcents {
            get {
                return this.amountMaxTTCcentsField;
            }
            set {
                if ((this.amountMaxTTCcentsField.Equals(value) != true)) {
                    this.amountMaxTTCcentsField = value;
                    this.RaisePropertyChanged("amountMaxTTCcents");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public customerArea.wcf_WsThemis.SessionEntity session {
            get {
                return this.sessionField;
            }
            set {
                if ((object.ReferenceEquals(this.sessionField, value) != true)) {
                    this.sessionField = value;
                    this.RaisePropertyChanged("session");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public customerArea.wcf_WsThemis.SeatEntity[] ListSeats {
            get {
                return this.ListSeatsField;
            }
            set {
                if ((object.ReferenceEquals(this.ListSeatsField, value) != true)) {
                    this.ListSeatsField = value;
                    this.RaisePropertyChanged("ListSeats");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LigneChoixPrixEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class LigneChoixPrixEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SectionEntity SectionField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.FloorEntity FloorField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.ZoneEntity ZoneField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.LigneGrilleTarifEntity ligneGrilleTarifField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public customerArea.wcf_WsThemis.SectionEntity Section {
            get {
                return this.SectionField;
            }
            set {
                if ((object.ReferenceEquals(this.SectionField, value) != true)) {
                    this.SectionField = value;
                    this.RaisePropertyChanged("Section");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public customerArea.wcf_WsThemis.FloorEntity Floor {
            get {
                return this.FloorField;
            }
            set {
                if ((object.ReferenceEquals(this.FloorField, value) != true)) {
                    this.FloorField = value;
                    this.RaisePropertyChanged("Floor");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public customerArea.wcf_WsThemis.ZoneEntity Zone {
            get {
                return this.ZoneField;
            }
            set {
                if ((object.ReferenceEquals(this.ZoneField, value) != true)) {
                    this.ZoneField = value;
                    this.RaisePropertyChanged("Zone");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public customerArea.wcf_WsThemis.LigneGrilleTarifEntity ligneGrilleTarif {
            get {
                return this.ligneGrilleTarifField;
            }
            set {
                if ((object.ReferenceEquals(this.ligneGrilleTarifField, value) != true)) {
                    this.ligneGrilleTarifField = value;
                    this.RaisePropertyChanged("ligneGrilleTarif");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SectionEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class SectionEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int SectionIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string SectionNameField;
        
        private int DispoTotaleField;
        
        private int DispoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.CategoryEntity[] listCategoriesField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int SectionId {
            get {
                return this.SectionIdField;
            }
            set {
                if ((this.SectionIdField.Equals(value) != true)) {
                    this.SectionIdField = value;
                    this.RaisePropertyChanged("SectionId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string SectionName {
            get {
                return this.SectionNameField;
            }
            set {
                if ((object.ReferenceEquals(this.SectionNameField, value) != true)) {
                    this.SectionNameField = value;
                    this.RaisePropertyChanged("SectionName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=2)]
        public int DispoTotale {
            get {
                return this.DispoTotaleField;
            }
            set {
                if ((this.DispoTotaleField.Equals(value) != true)) {
                    this.DispoTotaleField = value;
                    this.RaisePropertyChanged("DispoTotale");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=3)]
        public int Dispo {
            get {
                return this.DispoField;
            }
            set {
                if ((this.DispoField.Equals(value) != true)) {
                    this.DispoField = value;
                    this.RaisePropertyChanged("Dispo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public customerArea.wcf_WsThemis.CategoryEntity[] listCategories {
            get {
                return this.listCategoriesField;
            }
            set {
                if ((object.ReferenceEquals(this.listCategoriesField, value) != true)) {
                    this.listCategoriesField = value;
                    this.RaisePropertyChanged("listCategories");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="FloorEntity", Namespace="http://tempuri.org/")]
    [System.SerializableAttribute()]
    public partial class FloorEntity : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private int FloorIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string FloorNameField;
        
        private int DispoTotaleField;
        
        private int DispoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private customerArea.wcf_WsThemis.SectionEntity[] listSectionsField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int FloorId {
            get {
                return this.FloorIdField;
            }
            set {
                if ((this.FloorIdField.Equals(value) != true)) {
                    this.FloorIdField = value;
                    this.RaisePropertyChanged("FloorId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string FloorName {
            get {
                return this.FloorNameField;
            }
            set {
                if ((object.ReferenceEquals(this.FloorNameField, value) != true)) {
                    this.FloorNameField = value;
                    this.RaisePropertyChanged("FloorName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=2)]
        public int DispoTotale {
            get {
                return this.DispoTotaleField;
            }
            set {
                if ((this.DispoTotaleField.Equals(value) != true)) {
                    this.DispoTotaleField = value;
                    this.RaisePropertyChanged("DispoTotale");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=3)]
        public int Dispo {
            get {
                return this.DispoField;
            }
            set {
                if ((this.DispoField.Equals(value) != true)) {
                    this.DispoField = value;
                    this.RaisePropertyChanged("Dispo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public customerArea.wcf_WsThemis.SectionEntity[] listSections {
            get {
                return this.listSectionsField;
            }
            set {
                if ((object.ReferenceEquals(this.listSectionsField, value) != true)) {
                    this.listSectionsField = value;
                    this.RaisePropertyChanged("listSections");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="wcf_WsThemis.Iwcf_wsThemis")]
    public interface Iwcf_wsThemis {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetInfoCompIDList", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetInfoCompIDListResponse")]
        ws_DTO.IdentityInfoCompEntity[] GetInfoCompIDList(string StructureID, string userID, string listInfoCompId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetInfoCompIDList", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetInfoCompIDListResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentityInfoCompEntity[]> GetInfoCompIDListAsync(string StructureID, string userID, string listInfoCompId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetListInfoCompOnIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetListInfoCompOnIdentiteResponse")]
        ws_DTO.InfoCompEntity[] GetListInfoCompOnIdentite(string structureId, int identiteId, int[] listInfoCompId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetListInfoCompOnIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetListInfoCompOnIdentiteResponse")]
        System.Threading.Tasks.Task<ws_DTO.InfoCompEntity[]> GetListInfoCompOnIdentiteAsync(string structureId, int identiteId, int[] listInfoCompId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/ProfilAcheteurLogin", ReplyAction="http://tempuri.org/Iwcf_wsThemis/ProfilAcheteurLoginResponse")]
        ws_DTO.ProfilAcheteurEntity ProfilAcheteurLogin(string idStructure, string login, string passWord);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/ProfilAcheteurLogin", ReplyAction="http://tempuri.org/Iwcf_wsThemis/ProfilAcheteurLoginResponse")]
        System.Threading.Tasks.Task<ws_DTO.ProfilAcheteurEntity> ProfilAcheteurLoginAsync(string idStructure, string login, string passWord);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerAllPurchaseHistoryDetails", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetCustomerAllPurchaseHistoryDetailsResponse")]
        System.Data.DataSet GetCustomerAllPurchaseHistoryDetails(string idStructure, long identiteId, int languageId, bool WebTracing);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerAllPurchaseHistoryDetails", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetCustomerAllPurchaseHistoryDetailsResponse")]
        System.Threading.Tasks.Task<System.Data.DataSet> GetCustomerAllPurchaseHistoryDetailsAsync(string idStructure, long identiteId, int languageId, bool WebTracing);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetProfilAcheteurPurchaseHistory", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetProfilAcheteurPurchaseHistoryResponse")]
        System.Data.DataSet GetProfilAcheteurPurchaseHistory(int idStructure, int buyerProfilId, System.DateTime dateDeb, System.DateTime dateFin);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetProfilAcheteurPurchaseHistory", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetProfilAcheteurPurchaseHistoryResponse")]
        System.Threading.Tasks.Task<System.Data.DataSet> GetProfilAcheteurPurchaseHistoryAsync(int idStructure, int buyerProfilId, System.DateTime dateDeb, System.DateTime dateFin);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdateCustomerProfile", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdateCustomerProfileResponse")]
        bool UpdateCustomerProfile(string idStructure, ws_DTO.IdentiteEntity identiteToUpdated, string ficheSupprimer);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdateCustomerProfile", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdateCustomerProfileResponse")]
        System.Threading.Tasks.Task<bool> UpdateCustomerProfileAsync(string idStructure, ws_DTO.IdentiteEntity identiteToUpdated, string ficheSupprimer);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfId", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfIdResponse")]
        ws_DTO.IdentiteEntity GetCustomerProfileOfId(string idStructure, string identiteId, int colEmail, string ficheSupprimer);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfId", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfIdResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> GetCustomerProfileOfIdAsync(string idStructure, string identiteId, int colEmail, string ficheSupprimer);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadCivilityNaming", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadCivilityNamingResponse")]
        ws_DTO.GlobalAppellationEntity[] LoadCivilityNaming(string idStructure);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadCivilityNaming", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadCivilityNamingResponse")]
        System.Threading.Tasks.Task<ws_DTO.GlobalAppellationEntity[]> LoadCivilityNamingAsync(string idStructure);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadFunctions", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadFunctionsResponse")]
        ws_DTO.GlobalTitreEntity[] LoadFunctions(string structureId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadFunctions", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadFunctionsResponse")]
        System.Threading.Tasks.Task<ws_DTO.GlobalTitreEntity[]> LoadFunctionsAsync(string structureId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdateListInfoCompOnIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdateListInfoCompOnIdentiteResponse")]
        bool UpdateListInfoCompOnIdentite(string structureId, int identiteId, ws_DTO.InfoCompEntity[] lstInfoComps);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdateListInfoCompOnIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdateListInfoCompOnIdentiteResponse")]
        System.Threading.Tasks.Task<bool> UpdateListInfoCompOnIdentiteAsync(string structureId, int identiteId, ws_DTO.InfoCompEntity[] lstInfoComps);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdateInfoComp", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdateInfoCompResponse")]
        bool UpdateInfoComp(string structureId, int identiteId, int infoCompId, string action);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdateInfoComp", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdateInfoCompResponse")]
        System.Threading.Tasks.Task<bool> UpdateInfoCompAsync(string structureId, int identiteId, int infoCompId, string action);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfEmailOrIdentiteId", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfEmailOrIdentiteIdResponse")]
        ws_DTO.IdentiteEntity GetCustomerProfileOfEmailOrIdentiteId(string idStructure, string email, string passwCrypte, string identiteId, int numPostal, string ficheSupprimer);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfEmailOrIdentiteId", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfEmailOrIdentiteIdResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> GetCustomerProfileOfEmailOrIdentiteIdAsync(string idStructure, string email, string passwCrypte, string identiteId, int numPostal, string ficheSupprimer);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/CreateCustomerProfileAndReturnID", ReplyAction="http://tempuri.org/Iwcf_wsThemis/CreateCustomerProfileAndReturnIDResponse")]
        int CreateCustomerProfileAndReturnID(string idStructure, ws_DTO.IdentiteEntity identiteToCreate, byte[] encrypted, bool CheckEMailUnicity, string numPostal, int operateurId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/CreateCustomerProfileAndReturnID", ReplyAction="http://tempuri.org/Iwcf_wsThemis/CreateCustomerProfileAndReturnIDResponse")]
        System.Threading.Tasks.Task<int> CreateCustomerProfileAndReturnIDAsync(string idStructure, ws_DTO.IdentiteEntity identiteToCreate, byte[] encrypted, bool CheckEMailUnicity, string numPostal, int operateurId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadDevise", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadDeviseResponse")]
        ws_DTO.DeviseEntity[] LoadDevise(string idStructure);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadDevise", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadDeviseResponse")]
        System.Threading.Tasks.Task<ws_DTO.DeviseEntity[]> LoadDeviseAsync(string idStructure);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsListWaitList", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsListWaitListResponse")]
        ws_DTO.EventEntity[] LoadEventsListWaitList(string idStructure, string userLang);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsListWaitList", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsListWaitListResponse")]
        System.Threading.Tasks.Task<ws_DTO.EventEntity[]> LoadEventsListWaitListAsync(string idStructure, string userLang);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/InsertWaitList", ReplyAction="http://tempuri.org/Iwcf_wsThemis/InsertWaitListResponse")]
        bool InsertWaitList(string idStructure, ws_DTO.ListeAttenteEntity listeAttente);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/InsertWaitList", ReplyAction="http://tempuri.org/Iwcf_wsThemis/InsertWaitListResponse")]
        System.Threading.Tasks.Task<bool> InsertWaitListAsync(string idStructure, ws_DTO.ListeAttenteEntity listeAttente);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetWaitListOfIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetWaitListOfIdentiteResponse")]
        ws_DTO.ListeAttenteEntity[] GetWaitListOfIdentite(string idStructure, int identiteId, string language);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetWaitListOfIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetWaitListOfIdentiteResponse")]
        System.Threading.Tasks.Task<ws_DTO.ListeAttenteEntity[]> GetWaitListOfIdentiteAsync(string idStructure, int identiteId, string language);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/DeleteIdentiteOfWaitList", ReplyAction="http://tempuri.org/Iwcf_wsThemis/DeleteIdentiteOfWaitListResponse")]
        bool DeleteIdentiteOfWaitList(string structureId, int waitListId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/DeleteIdentiteOfWaitList", ReplyAction="http://tempuri.org/Iwcf_wsThemis/DeleteIdentiteOfWaitListResponse")]
        System.Threading.Tasks.Task<bool> DeleteIdentiteOfWaitListAsync(string structureId, int waitListId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsListOfToday", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsListOfTodayResponse")]
        ws_DTO.EventEntity[] LoadEventsListOfToday(int structureId, string userLang, int duration);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsListOfToday", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsListOfTodayResponse")]
        System.Threading.Tasks.Task<ws_DTO.EventEntity[]> LoadEventsListOfTodayAsync(int structureId, string userLang, int duration);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetListCoupeFileCommandes", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetListCoupeFileCommandesResponse")]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.EventEntityOfFormula))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.SessionEntityOfFormula))]
        ws_DTO.CoupeFileEntity[] GetListCoupeFileCommandes(int structureId, string userLang, ws_DTO.CoupeFileEntity coupeFile);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetListCoupeFileCommandes", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetListCoupeFileCommandesResponse")]
        System.Threading.Tasks.Task<ws_DTO.CoupeFileEntity[]> GetListCoupeFileCommandesAsync(int structureId, string userLang, ws_DTO.CoupeFileEntity coupeFile);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdateDossierCommentaire", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdateDossierCommentaireResponse")]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.DossierEntree))]
        bool UpdateDossierCommentaire(int structureId, string userLang, ws_DTO.Dossier dossierToUpdate);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdateDossierCommentaire", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdateDossierCommentaireResponse")]
        System.Threading.Tasks.Task<bool> UpdateDossierCommentaireAsync(int structureId, string userLang, ws_DTO.Dossier dossierToUpdate);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmdCoupeFile", ReplyAction="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmdCoupeFileResponse")]
        string[] TAH_MakePDF_FromCmdCoupeFile(int structureId, int cmdId, int filiereid, int operateurid, int identiteId, string pathPdf, string consommateurname, string langIso, string nomCmd, int[] lstDossierId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmdCoupeFile", ReplyAction="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmdCoupeFileResponse")]
        System.Threading.Tasks.Task<string[]> TAH_MakePDF_FromCmdCoupeFileAsync(int structureId, int cmdId, int filiereid, int operateurid, int identiteId, string pathPdf, string consommateurname, string langIso, string nomCmd, int[] lstDossierId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetCurrentBasketAbonnement", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetCurrentBasketAbonnementResponse")]
        ws_DTO.BasketEntity GetCurrentBasketAbonnement(int structureId, int identiteID, int webUserID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetCurrentBasketAbonnement", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetCurrentBasketAbonnementResponse")]
        System.Threading.Tasks.Task<ws_DTO.BasketEntity> GetCurrentBasketAbonnementAsync(int structureId, int identiteID, int webUserID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetCurrentCustomBasketAbonnement", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetCurrentCustomBasketAbonnementResponse")]
        ws_DTO.objets_liaisons.CustomBasket GetCurrentCustomBasketAbonnement(int structureId, int identiteID, int basketID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetCurrentCustomBasketAbonnement", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetCurrentCustomBasketAbonnementResponse")]
        System.Threading.Tasks.Task<ws_DTO.objets_liaisons.CustomBasket> GetCurrentCustomBasketAbonnementAsync(int structureId, int identiteID, int basketID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadLanguages", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadLanguagesResponse")]
        ws_DTO.LanguageEntity[] LoadLanguages(int structureId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadLanguages", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadLanguagesResponse")]
        System.Threading.Tasks.Task<ws_DTO.LanguageEntity[]> LoadLanguagesAsync(int structureId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetLangueIdOfLangCode", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetLangueIdOfLangCodeResponse")]
        int GetLangueIdOfLangCode(int structureId, string langCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetLangueIdOfLangCode", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetLangueIdOfLangCodeResponse")]
        System.Threading.Tasks.Task<int> GetLangueIdOfLangCodeAsync(int structureId, string langCode);
        
        // CODEGEN : La génération du contrat de message depuis le nom d'élément structureId de l'espace de noms http://tempuri.org/ n'est pas marqué nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/nextDelayRefresh", ReplyAction="http://tempuri.org/Iwcf_wsThemis/nextDelayRefreshResponse")]
        customerArea.wcf_WsThemis.nextDelayRefreshResponse nextDelayRefresh(customerArea.wcf_WsThemis.nextDelayRefreshRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/nextDelayRefresh", ReplyAction="http://tempuri.org/Iwcf_wsThemis/nextDelayRefreshResponse")]
        System.Threading.Tasks.Task<customerArea.wcf_WsThemis.nextDelayRefreshResponse> nextDelayRefreshAsync(customerArea.wcf_WsThemis.nextDelayRefreshRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/DeleteMyIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/DeleteMyIdentiteResponse")]
        int DeleteMyIdentite(string structureId, int identiteId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/DeleteMyIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/DeleteMyIdentiteResponse")]
        System.Threading.Tasks.Task<int> DeleteMyIdentiteAsync(string structureId, int identiteId);
        
        // CODEGEN : La génération du contrat de message depuis le nom d'élément langCode de l'espace de noms http://tempuri.org/ n'est pas marqué nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEvents", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsResponse")]
        customerArea.wcf_WsThemis.LoadEventsResponse LoadEvents(customerArea.wcf_WsThemis.LoadEventsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEvents", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsResponse")]
        System.Threading.Tasks.Task<customerArea.wcf_WsThemis.LoadEventsResponse> LoadEventsAsync(customerArea.wcf_WsThemis.LoadEventsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsInGp", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsInGpResponse")]
        ws_DTO.EventEntity[] LoadEventsInGp(int structureId, string langCode, int[] listEvents);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsInGp", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsInGpResponse")]
        System.Threading.Tasks.Task<ws_DTO.EventEntity[]> LoadEventsInGpAsync(int structureId, string langCode, int[] listEvents);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFiliere", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFiliereResponse")]
        ws_DTO.EventEntity[] LoadEventsOfFiliere(int structureId, string langCode, int[] listFiliereId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFiliere", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFiliereResponse")]
        System.Threading.Tasks.Task<ws_DTO.EventEntity[]> LoadEventsOfFiliereAsync(int structureId, string langCode, int[] listFiliereId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadDispoFuturesSessions", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadDispoFuturesSessionsResponse")]
        ws_DTO.SessionEntity[] LoadDispoFuturesSessions(int structureId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadDispoFuturesSessions", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadDispoFuturesSessionsResponse")]
        System.Threading.Tasks.Task<ws_DTO.SessionEntity[]> LoadDispoFuturesSessionsAsync(int structureId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdatePasswordIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdatePasswordIdentiteResponse")]
        bool UpdatePasswordIdentite(string idStructure, int _identiteId, string _password);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdatePasswordIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdatePasswordIdentiteResponse")]
        System.Threading.Tasks.Task<bool> UpdatePasswordIdentiteAsync(string idStructure, int _identiteId, string _password);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadIdentiteResponse")]
        ws_DTO.IdentiteEntity LoadIdentite(int structureId, string email, int id, int colPostalTel);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadIdentiteResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> LoadIdentiteAsync(int structureId, string email, int id, int colPostalTel);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/CheckIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/CheckIdentiteResponse")]
        string CheckIdentite(int structureId, ws_DTO.IdentiteEntity ident);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/CheckIdentite", ReplyAction="http://tempuri.org/Iwcf_wsThemis/CheckIdentiteResponse")]
        System.Threading.Tasks.Task<string> CheckIdentiteAsync(int structureId, ws_DTO.IdentiteEntity ident);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FillDroitsFacture", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FillDroitsFactureResponse")]
        ws_DTO.IdentiteEntity FillDroitsFacture(int idstructure, int infocompSeuilBlocage, ws_DTO.IdentiteEntity identity);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FillDroitsFacture", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FillDroitsFactureResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> FillDroitsFactureAsync(int idstructure, int infocompSeuilBlocage, ws_DTO.IdentiteEntity identity);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FillAcompte", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FillAcompteResponse")]
        ws_DTO.IdentiteEntity FillAcompte(int idstructure, int[] listModesPaiementsPrisEnCompte, ws_DTO.IdentiteEntity identity);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FillAcompte", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FillAcompteResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> FillAcompteAsync(int idstructure, int[] listModesPaiementsPrisEnCompte, ws_DTO.IdentiteEntity identity);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadCommandes", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadCommandesResponse")]
        ws_DTO.Commande[] LoadCommandes(int structureId, int identiteId, int[] colCommandeId, int[] colFormuleId, string[] colEtat);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadCommandes", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadCommandesResponse")]
        System.Threading.Tasks.Task<ws_DTO.Commande[]> LoadCommandesAsync(int structureId, int identiteId, int[] colCommandeId, int[] colFormuleId, string[] colEtat);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadIdentiteChild", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadIdentiteChildResponse")]
        ws_DTO.IdentiteEntity[] FanCard_LoadIdentiteChild(int structureId, int identiteIdParent, string listFormules, string listEtats);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadIdentiteChild", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadIdentiteChildResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentiteEntity[]> FanCard_LoadIdentiteChildAsync(int structureId, int identiteIdParent, string listFormules, string listEtats);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FanCard_AddRelation", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FanCard_AddRelationResponse")]
        ws_DTO.IdentiteEntity FanCard_AddRelation(int structureId, int identiteIdParent, string checkValue);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FanCard_AddRelation", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FanCard_AddRelationResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> FanCard_AddRelationAsync(int structureId, int identiteIdParent, string checkValue);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FanCard_UpdateRelation", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FanCard_UpdateRelationResponse")]
        bool FanCard_UpdateRelation(int structureId, int identiteIdParent, string checkValue);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FanCard_UpdateRelation", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FanCard_UpdateRelationResponse")]
        System.Threading.Tasks.Task<bool> FanCard_UpdateRelationAsync(int structureId, int identiteIdParent, string checkValue);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadAllIdentiteCommandesFans", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadAllIdentiteCommandesFansResponse")]
        ws_DTO.objets_liaisons.DossierIdentiteEntity[] FanCard_LoadAllIdentiteCommandesFans(int structureId, int identiteIdParent, string listFormules, string listEtats, bool commandValidOnly, int manifReference);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadAllIdentiteCommandesFans", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadAllIdentiteCommandesFansResponse")]
        System.Threading.Tasks.Task<ws_DTO.objets_liaisons.DossierIdentiteEntity[]> FanCard_LoadAllIdentiteCommandesFansAsync(int structureId, int identiteIdParent, string listFormules, string listEtats, bool commandValidOnly, int manifReference);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/IsGestionnaireReaboFans", ReplyAction="http://tempuri.org/Iwcf_wsThemis/IsGestionnaireReaboFansResponse")]
        bool IsGestionnaireReaboFans(int structureId, int identiteId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/IsGestionnaireReaboFans", ReplyAction="http://tempuri.org/Iwcf_wsThemis/IsGestionnaireReaboFansResponse")]
        System.Threading.Tasks.Task<bool> IsGestionnaireReaboFansAsync(int structureId, int identiteId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FanCard_DeleteRelation", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FanCard_DeleteRelationResponse")]
        ws_DTO.IdentiteEntity FanCard_DeleteRelation(int structureId, int identiteIdParent, int identiteIdChild, string identiteIdChildCheckValue);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FanCard_DeleteRelation", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FanCard_DeleteRelationResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> FanCard_DeleteRelationAsync(int structureId, int identiteIdParent, int identiteIdChild, string identiteIdChildCheckValue);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetDemandResetPasswordById", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetDemandResetPasswordByIdResponse")]
        ws_DTO.wt.DemandPasswordResetEntity GetDemandResetPasswordById(int idStructure, int _demandResetPasswordId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetDemandResetPasswordById", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetDemandResetPasswordByIdResponse")]
        System.Threading.Tasks.Task<ws_DTO.wt.DemandPasswordResetEntity> GetDemandResetPasswordByIdAsync(int idStructure, int _demandResetPasswordId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdateUseDateDemandResetPassword", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdateUseDateDemandResetPasswordResponse")]
        bool UpdateUseDateDemandResetPassword(int idStructure, int _demandResetPasswordId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UpdateUseDateDemandResetPassword", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UpdateUseDateDemandResetPasswordResponse")]
        System.Threading.Tasks.Task<bool> UpdateUseDateDemandResetPasswordAsync(int idStructure, int _demandResetPasswordId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/AddDemandResetPassword", ReplyAction="http://tempuri.org/Iwcf_wsThemis/AddDemandResetPasswordResponse")]
        int AddDemandResetPassword(int idStructure, string _ipAddress, int _identityID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/AddDemandResetPassword", ReplyAction="http://tempuri.org/Iwcf_wsThemis/AddDemandResetPasswordResponse")]
        System.Threading.Tasks.Task<int> AddDemandResetPasswordAsync(int idStructure, string _ipAddress, int _identityID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/getConsommateursForSeances", ReplyAction="http://tempuri.org/Iwcf_wsThemis/getConsommateursForSeancesResponse")]
        ws_DTO.IdentiteEntity[] getConsommateursForSeances(int structureId, int identiteId, string langCode, int ipostalTelEmail, int[] listSessions);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/getConsommateursForSeances", ReplyAction="http://tempuri.org/Iwcf_wsThemis/getConsommateursForSeancesResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentiteEntity[]> getConsommateursForSeancesAsync(int structureId, int identiteId, string langCode, int ipostalTelEmail, int[] listSessions);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/getConsommateurs", ReplyAction="http://tempuri.org/Iwcf_wsThemis/getConsommateursResponse")]
        ws_DTO.IdentiteEntity[] getConsommateurs(int structureId, int identiteId, string langCode, int ipostalTelEmail);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/getConsommateurs", ReplyAction="http://tempuri.org/Iwcf_wsThemis/getConsommateursResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentiteEntity[]> getConsommateursAsync(int structureId, int identiteId, string langCode, int ipostalTelEmail);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/AddLinkConsumers", ReplyAction="http://tempuri.org/Iwcf_wsThemis/AddLinkConsumersResponse")]
        int AddLinkConsumers(int structureId, int identiteId, int consumerId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/AddLinkConsumers", ReplyAction="http://tempuri.org/Iwcf_wsThemis/AddLinkConsumersResponse")]
        System.Threading.Tasks.Task<int> AddLinkConsumersAsync(int structureId, int identiteId, int consumerId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UnLinkConsumers", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UnLinkConsumersResponse")]
        bool UnLinkConsumers(int structureId, int identiteId, int consumerId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UnLinkConsumers", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UnLinkConsumersResponse")]
        System.Threading.Tasks.Task<bool> UnLinkConsumersAsync(int structureId, int identiteId, int consumerId);
        
        // CODEGEN : La génération du contrat de message depuis le nom d'élément listZonesId de l'espace de noms http://tempuri.org/ n'est pas marqué nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifs", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsResponse")]
        customerArea.wcf_WsThemis.LoadGrilleTarifsResponse LoadGrilleTarifs(customerArea.wcf_WsThemis.LoadGrilleTarifsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifs", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsResponse")]
        System.Threading.Tasks.Task<customerArea.wcf_WsThemis.LoadGrilleTarifsResponse> LoadGrilleTarifsAsync(customerArea.wcf_WsThemis.LoadGrilleTarifsRequest request);
        
        // CODEGEN : La génération du contrat de message depuis le nom d'élément listZonesId de l'espace de noms http://tempuri.org/ n'est pas marqué nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsPlacement", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsPlacementResponse")]
        customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementResponse LoadGrilleTarifsPlacement(customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsPlacement", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsPlacementResponse")]
        System.Threading.Tasks.Task<customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementResponse> LoadGrilleTarifsPlacementAsync(customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadAllInternetGrilleTarifs", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadAllInternetGrilleTarifsResponse")]
        ws_DTO.GestionPlaceEntity[] LoadAllInternetGrilleTarifs(int structureId, string langCode, int identiteId, int profilAcheteurId, int eventId, int sessionId, int[] listCategoriesId, int[] listTarifsId, int offerId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadAllInternetGrilleTarifs", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadAllInternetGrilleTarifsResponse")]
        System.Threading.Tasks.Task<ws_DTO.GestionPlaceEntity[]> LoadAllInternetGrilleTarifsAsync(int structureId, string langCode, int identiteId, int profilAcheteurId, int eventId, int sessionId, int[] listCategoriesId, int[] listTarifsId, int offerId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadAmounts", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadAmountsResponse")]
        ws_DTO.LigneGrilleTarifEntity[] LoadAmounts(int structureId, string langCode, int eventId, int[] listsessionId, int[] listCategoriesId, int[] listTarifsId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadAmounts", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadAmountsResponse")]
        System.Threading.Tasks.Task<ws_DTO.LigneGrilleTarifEntity[]> LoadAmountsAsync(int structureId, string langCode, int eventId, int[] listsessionId, int[] listCategoriesId, int[] listTarifsId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadFormulas", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadFormulasResponse")]
        ws_DTO.FormulaEntity[] LoadFormulas(int structureId, string langCode, int identiteId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadFormulas", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadFormulasResponse")]
        System.Threading.Tasks.Task<ws_DTO.FormulaEntity[]> LoadFormulasAsync(int structureId, string langCode, int identiteId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFormulas", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFormulasResponse")]
        ws_DTO.EventEntityOfFormula[] LoadEventsOfFormulas(int structureId, string langCode, int identiteId, int formulaId, int[] listTarifId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFormulas", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFormulasResponse")]
        System.Threading.Tasks.Task<ws_DTO.EventEntityOfFormula[]> LoadEventsOfFormulasAsync(int structureId, string langCode, int identiteId, int formulaId, int[] listTarifId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadContraintesOfFormulas", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadContraintesOfFormulasResponse")]
        ws_DTO.FormulaContraintesGroupeOpenEntity[] LoadContraintesOfFormulas(int structureId, string langCode, int identiteId, int formulaId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadContraintesOfFormulas", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadContraintesOfFormulasResponse")]
        System.Threading.Tasks.Task<ws_DTO.FormulaContraintesGroupeOpenEntity[]> LoadContraintesOfFormulasAsync(int structureId, string langCode, int identiteId, int formulaId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsHorsAbo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsHorsAboResponse")]
        ws_DTO.EventEntity[] LoadEventsHorsAbo(int structureId, string langCode, int identiteId, int[] listFormulas, int offreId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsHorsAbo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsHorsAboResponse")]
        System.Threading.Tasks.Task<ws_DTO.EventEntity[]> LoadEventsHorsAboAsync(int structureId, string langCode, int identiteId, int[] listFormulas, int offreId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsHorsAbo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsHorsAboResponse")]
        ws_DTO.GestionPlaceEntity[] LoadGrilleTarifsHorsAbo(int structureId, string langCode, int identiteId, int profilAcheteurId, int eventId, int sessionId, int[] listZonesId, int[] listFloorsId, int[] listSectionsId, int[] listCategoriesId, string panierFormate, int[] listFormulas, int offreId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsHorsAbo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsHorsAboResponse")]
        System.Threading.Tasks.Task<ws_DTO.GestionPlaceEntity[]> LoadGrilleTarifsHorsAboAsync(int structureId, string langCode, int identiteId, int profilAcheteurId, int eventId, int sessionId, int[] listZonesId, int[] listFloorsId, int[] listSectionsId, int[] listCategoriesId, string panierFormate, int[] listFormulas, int offreId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadProductsOfFormulas", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadProductsOfFormulasResponse")]
        ws_DTO.ProductEntity[] LoadProductsOfFormulas(int structureId, string langCode, int identiteId, int[] listFormulas);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadProductsOfFormulas", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadProductsOfFormulasResponse")]
        System.Threading.Tasks.Task<ws_DTO.ProductEntity[]> LoadProductsOfFormulasAsync(int structureId, string langCode, int identiteId, int[] listFormulas);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadFraisProductsOfFormulas", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadFraisProductsOfFormulasResponse")]
        ws_DTO.ProductEntity[] LoadFraisProductsOfFormulas(int structureId, string langCode, int identiteId, int[] listFormulas);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadFraisProductsOfFormulas", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadFraisProductsOfFormulasResponse")]
        System.Threading.Tasks.Task<ws_DTO.ProductEntity[]> LoadFraisProductsOfFormulasAsync(int structureId, string langCode, int identiteId, int[] listFormulas);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetListeReserveForFormula", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetListeReserveForFormulaResponse")]
        ws_DTO.ReserveEntity[] GetListeReserveForFormula(int structureId, string langCode, int formulaId, int tarifid, int eventId, int sessionId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/GetListeReserveForFormula", ReplyAction="http://tempuri.org/Iwcf_wsThemis/GetListeReserveForFormulaResponse")]
        System.Threading.Tasks.Task<ws_DTO.ReserveEntity[]> GetListeReserveForFormulaAsync(int structureId, string langCode, int formulaId, int tarifid, int eventId, int sessionId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadFraisEnvoisReabo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadFraisEnvoisReaboResponse")]
        ws_DTO.Produit[] LoadFraisEnvoisReabo(int structureId, int langId, int aboId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadFraisEnvoisReabo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadFraisEnvoisReaboResponse")]
        System.Threading.Tasks.Task<ws_DTO.Produit[]> LoadFraisEnvoisReaboAsync(int structureId, int langId, int aboId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadProducts", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadProductsResponse")]
        ws_DTO.Produit[] LoadProducts(int structureId, int langId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadProducts", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadProductsResponse")]
        System.Threading.Tasks.Task<ws_DTO.Produit[]> LoadProductsAsync(int structureId, int langId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadCommonsMO_ListFormulaTarifSeance_abo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadCommonsMO_ListFormulaTarifSeance_aboResponse" +
            "")]
        ws_DTO.ProductEntity[] LoadCommonsMO_ListFormulaTarifSeance_abo(int structureId, string langCode, int[] listFormulas, int[] listTarifs, int[] listSessions);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadCommonsMO_ListFormulaTarifSeance_abo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadCommonsMO_ListFormulaTarifSeance_aboResponse" +
            "")]
        System.Threading.Tasks.Task<ws_DTO.ProductEntity[]> LoadCommonsMO_ListFormulaTarifSeance_aboAsync(int structureId, string langCode, int[] listFormulas, int[] listTarifs, int[] listSessions);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadMaquettesOfMoGp", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadMaquettesOfMoGpResponse")]
        System.Collections.Generic.Dictionary<int, int> LoadMaquettesOfMoGp(int structureId, int[] listGps, int moId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadMaquettesOfMoGp", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadMaquettesOfMoGpResponse")]
        System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<int, int>> LoadMaquettesOfMoGpAsync(int structureId, int[] listGps, int moId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/Basket_FillFromOpen", ReplyAction="http://tempuri.org/Iwcf_wsThemis/Basket_FillFromOpenResponse")]
        ws_DTO.BasketEntity Basket_FillFromOpen(int structureid, ws_DTO.BasketEntity bask);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/Basket_FillFromOpen", ReplyAction="http://tempuri.org/Iwcf_wsThemis/Basket_FillFromOpenResponse")]
        System.Threading.Tasks.Task<ws_DTO.BasketEntity> Basket_FillFromOpenAsync(int structureid, ws_DTO.BasketEntity bask);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/BasketFillFromOpen", ReplyAction="http://tempuri.org/Iwcf_wsThemis/BasketFillFromOpenResponse")]
        ws_DTO.BasketEntity BasketFillFromOpen(int structureId, int basketId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/BasketFillFromOpen", ReplyAction="http://tempuri.org/Iwcf_wsThemis/BasketFillFromOpenResponse")]
        System.Threading.Tasks.Task<ws_DTO.BasketEntity> BasketFillFromOpenAsync(int structureId, int basketId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/BasketTransformation", ReplyAction="http://tempuri.org/Iwcf_wsThemis/BasketTransformationResponse")]
        bool BasketTransformation(int structureId, int basketId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/BasketTransformation", ReplyAction="http://tempuri.org/Iwcf_wsThemis/BasketTransformationResponse")]
        System.Threading.Tasks.Task<bool> BasketTransformationAsync(int structureId, int basketId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmd", ReplyAction="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmdResponse")]
        string[] TAH_MakePDF_FromCmd(int structureid, int cmdId, int filiereid, int operateurid, int identiteId, string pathPdf, string consommateurname, string langIso, string nomCmd);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmd", ReplyAction="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmdResponse")]
        System.Threading.Tasks.Task<string[]> TAH_MakePDF_FromCmdAsync(int structureid, int cmdId, int filiereid, int operateurid, int identiteId, string pathPdf, string consommateurname, string langIso, string nomCmd);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/EditerCommande", ReplyAction="http://tempuri.org/Iwcf_wsThemis/EditerCommandeResponse")]
        bool EditerCommande(string _structureid, ws_DTO.Entree[] _listeentrees, int identite_id, int operateurid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/EditerCommande", ReplyAction="http://tempuri.org/Iwcf_wsThemis/EditerCommandeResponse")]
        System.Threading.Tasks.Task<bool> EditerCommandeAsync(string _structureid, ws_DTO.Entree[] _listeentrees, int identite_id, int operateurid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_ispossible", ReplyAction="http://tempuri.org/Iwcf_wsThemis/DepotVente_ispossibleResponse")]
        bool DepotVente_ispossible(int structureid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_ispossible", ReplyAction="http://tempuri.org/Iwcf_wsThemis/DepotVente_ispossibleResponse")]
        System.Threading.Tasks.Task<bool> DepotVente_ispossibleAsync(int structureid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_GetMontantReprise", ReplyAction="http://tempuri.org/Iwcf_wsThemis/DepotVente_GetMontantRepriseResponse")]
        ws_DTO.DepotVenteEntity DepotVente_GetMontantReprise(int structureid, int IdentityId, int OrderId, int EventId, string SeatsId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_GetMontantReprise", ReplyAction="http://tempuri.org/Iwcf_wsThemis/DepotVente_GetMontantRepriseResponse")]
        System.Threading.Tasks.Task<ws_DTO.DepotVenteEntity> DepotVente_GetMontantRepriseAsync(int structureid, int IdentityId, int OrderId, int EventId, string SeatsId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_put", ReplyAction="http://tempuri.org/Iwcf_wsThemis/DepotVente_putResponse")]
        ws_DTO.DepotVenteEntity DepotVente_put(int structureid, int IdentityId, int OrderId, int EventId, string SeatsId, int OperatorId, int amount, int NbrDepotsAutorisesGroupeManif);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_put", ReplyAction="http://tempuri.org/Iwcf_wsThemis/DepotVente_putResponse")]
        System.Threading.Tasks.Task<ws_DTO.DepotVenteEntity> DepotVente_putAsync(int structureid, int IdentityId, int OrderId, int EventId, string SeatsId, int OperatorId, int amount, int NbrDepotsAutorisesGroupeManif);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/MarquerConsommateur", ReplyAction="http://tempuri.org/Iwcf_wsThemis/MarquerConsommateurResponse")]
        int MarquerConsommateur(string _structureid, ws_DTO.Entree[] _listeentrees, int identite_id);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/MarquerConsommateur", ReplyAction="http://tempuri.org/Iwcf_wsThemis/MarquerConsommateurResponse")]
        System.Threading.Tasks.Task<int> MarquerConsommateurAsync(string _structureid, ws_DTO.Entree[] _listeentrees, int identite_id);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadIdentityRIB", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadIdentityRIBResponse")]
        ws_DTO.IdentityRIBEntity LoadIdentityRIB(int idStructure, int identityID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadIdentityRIB", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadIdentityRIBResponse")]
        System.Threading.Tasks.Task<ws_DTO.IdentityRIBEntity> LoadIdentityRIBAsync(int idStructure, int identityID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/AddIdentityRIB", ReplyAction="http://tempuri.org/Iwcf_wsThemis/AddIdentityRIBResponse")]
        bool AddIdentityRIB(int idStructure, ws_DTO.IdentityRIBEntity identityRIB);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/AddIdentityRIB", ReplyAction="http://tempuri.org/Iwcf_wsThemis/AddIdentityRIBResponse")]
        System.Threading.Tasks.Task<bool> AddIdentityRIBAsync(int idStructure, ws_DTO.IdentityRIBEntity identityRIB);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FlagAuto", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FlagAutoResponse")]
        ws_DTO.SeatEntity[] FlagAuto(int structureId, int eventId, int sessionId, System.Collections.Generic.Dictionary<string, int> keyFormulaTarif_nbr, System.Collections.Generic.Dictionary<string, int> keyGpId_nbr_forHA, int nbrTotal, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FlagAuto", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FlagAutoResponse")]
        System.Threading.Tasks.Task<ws_DTO.SeatEntity[]> FlagAutoAsync(int structureId, int eventId, int sessionId, System.Collections.Generic.Dictionary<string, int> keyFormulaTarif_nbr, System.Collections.Generic.Dictionary<string, int> keyGpId_nbr_forHA, int nbrTotal, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FlagManuel_tempo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FlagManuel_tempoResponse")]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.SeatUnitSalesEntity))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.SeatAboEntity))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.SeatAboMultiEntity))]
        ws_DTO.SeatEntity FlagManuel_tempo(int structureId, int eventId, int sessionId, int seatId, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/FlagManuel_tempo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/FlagManuel_tempoResponse")]
        System.Threading.Tasks.Task<ws_DTO.SeatEntity> FlagManuel_tempoAsync(int structureId, int eventId, int sessionId, int seatId, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UnFlagManuel_tempo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UnFlagManuel_tempoResponse")]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.SeatUnitSalesEntity))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.SeatAboEntity))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.SeatAboMultiEntity))]
        ws_DTO.SeatEntity UnFlagManuel_tempo(int structureId, int eventId, int sessionId, int seatId, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UnFlagManuel_tempo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UnFlagManuel_tempoResponse")]
        System.Threading.Tasks.Task<ws_DTO.SeatEntity> UnFlagManuel_tempoAsync(int structureId, int eventId, int sessionId, int seatId, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeats", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsResponse")]
        bool UnFlagSeats(int structureId, int eventId, int sessionId, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeats", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsResponse")]
        System.Threading.Tasks.Task<bool> UnFlagSeatsAsync(int structureId, int eventId, int sessionId, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel_tempo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel_tempoResponse")]
        ws_DTO.SeatEntity[] UnFlagSeatsManuel_tempo(int structureId, int eventId, int sessionId, int[] seatIds, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel_tempo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel_tempoResponse")]
        System.Threading.Tasks.Task<ws_DTO.SeatEntity[]> UnFlagSeatsManuel_tempoAsync(int structureId, int eventId, int sessionId, int[] seatIds, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuelResponse")]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.SeatUnitSalesEntity))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.SeatAboEntity))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ws_DTO.SeatAboMultiEntity))]
        ws_DTO.SeatEntity UnFlagSeatsManuel(int structureId, int eventId, int sessionId, int seatId, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel", ReplyAction="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuelResponse")]
        System.Threading.Tasks.Task<ws_DTO.SeatEntity> UnFlagSeatsManuelAsync(int structureId, int eventId, int sessionId, int seatId, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/ReFlagSeatsManuel_tempo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/ReFlagSeatsManuel_tempoResponse")]
        ws_DTO.SeatEntity[] ReFlagSeatsManuel_tempo(int structureId, int eventId, int sessionId, int[] seatIds, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/ReFlagSeatsManuel_tempo", ReplyAction="http://tempuri.org/Iwcf_wsThemis/ReFlagSeatsManuel_tempoResponse")]
        System.Threading.Tasks.Task<ws_DTO.SeatEntity[]> ReFlagSeatsManuel_tempoAsync(int structureId, int eventId, int sessionId, int[] seatIds, int userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadSeats", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadSeatsResponse")]
        ws_DTO.SeatEntity[] LoadSeats(int structureId, int eventId, int sessionId, int identiteId, string myFlag, string langCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadSeats", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadSeatsResponse")]
        System.Threading.Tasks.Task<ws_DTO.SeatEntity[]> LoadSeatsAsync(int structureId, int eventId, int sessionId, int identiteId, string myFlag, string langCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadSeatsTexts", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadSeatsTextsResponse")]
        ws_DTO.TextEntity[] LoadSeatsTexts(int structureId, int eventId, int sessionId, int zoneId, int floorId, int sectionId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadSeatsTexts", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadSeatsTextsResponse")]
        System.Threading.Tasks.Task<ws_DTO.TextEntity[]> LoadSeatsTextsAsync(int structureId, int eventId, int sessionId, int zoneId, int floorId, int sectionId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadInfoComp", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadInfoCompResponse")]
        ws_DTO.InfoCompEntity[] LoadInfoComp(string idStructure);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Iwcf_wsThemis/LoadInfoComp", ReplyAction="http://tempuri.org/Iwcf_wsThemis/LoadInfoCompResponse")]
        System.Threading.Tasks.Task<ws_DTO.InfoCompEntity[]> LoadInfoCompAsync(string idStructure);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/Iwcf_wsThemis/LoadInfoComp", ReplyAction = "http://tempuri.org/Iwcf_wsThemis/LoadInfoCompResponse")]
        ws_DTO.ReservationEntity[] LoadReservationList(int structureId, int identityId);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/Iwcf_wsThemis/LoadInfoComp", ReplyAction = "http://tempuri.org/Iwcf_wsThemis/LoadInfoCompResponse")]
        System.Threading.Tasks.Task<ws_DTO.ReservationEntity[]> LoadReservationListAsync(int structureId, int identityId);

    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class nextDelayRefreshRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="nextDelayRefresh", Namespace="http://tempuri.org/", Order=0)]
        public customerArea.wcf_WsThemis.nextDelayRefreshRequestBody Body;
        
        public nextDelayRefreshRequest() {
        }
        
        public nextDelayRefreshRequest(customerArea.wcf_WsThemis.nextDelayRefreshRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class nextDelayRefreshRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string structureId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public int defaultDelayS;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string typeSite;
        
        public nextDelayRefreshRequestBody() {
        }
        
        public nextDelayRefreshRequestBody(string structureId, int defaultDelayS, string typeSite) {
            this.structureId = structureId;
            this.defaultDelayS = defaultDelayS;
            this.typeSite = typeSite;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class nextDelayRefreshResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="nextDelayRefreshResponse", Namespace="http://tempuri.org/", Order=0)]
        public customerArea.wcf_WsThemis.nextDelayRefreshResponseBody Body;
        
        public nextDelayRefreshResponse() {
        }
        
        public nextDelayRefreshResponse(customerArea.wcf_WsThemis.nextDelayRefreshResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class nextDelayRefreshResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public int nextDelayRefreshResult;
        
        public nextDelayRefreshResponseBody() {
        }
        
        public nextDelayRefreshResponseBody(int nextDelayRefreshResult) {
            this.nextDelayRefreshResult = nextDelayRefreshResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class LoadEventsRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="LoadEvents", Namespace="http://tempuri.org/", Order=0)]
        public customerArea.wcf_WsThemis.LoadEventsRequestBody Body;
        
        public LoadEventsRequest() {
        }
        
        public LoadEventsRequest(customerArea.wcf_WsThemis.LoadEventsRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class LoadEventsRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public int structureId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string langCode;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public int identiteId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public int paId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public customerArea.wcf_WsThemis.ArrayOfInt listOffres;
        
        public LoadEventsRequestBody() {
        }
        
        public LoadEventsRequestBody(int structureId, string langCode, int identiteId, int paId, customerArea.wcf_WsThemis.ArrayOfInt listOffres) {
            this.structureId = structureId;
            this.langCode = langCode;
            this.identiteId = identiteId;
            this.paId = paId;
            this.listOffres = listOffres;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class LoadEventsResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="LoadEventsResponse", Namespace="http://tempuri.org/", Order=0)]
        public customerArea.wcf_WsThemis.LoadEventsResponseBody Body;
        
        public LoadEventsResponse() {
        }
        
        public LoadEventsResponse(customerArea.wcf_WsThemis.LoadEventsResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class LoadEventsResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public System.Data.DataSet LoadEventsResult;
        
        public LoadEventsResponseBody() {
        }
        
        public LoadEventsResponseBody(System.Data.DataSet LoadEventsResult) {
            this.LoadEventsResult = LoadEventsResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class LoadGrilleTarifsRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="LoadGrilleTarifs", Namespace="http://tempuri.org/", Order=0)]
        public customerArea.wcf_WsThemis.LoadGrilleTarifsRequestBody Body;
        
        public LoadGrilleTarifsRequest() {
        }
        
        public LoadGrilleTarifsRequest(customerArea.wcf_WsThemis.LoadGrilleTarifsRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class LoadGrilleTarifsRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public int structureId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public int langId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public int identiteId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public int profilAcheteurId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public int eventId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=5)]
        public int sessionId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public customerArea.wcf_WsThemis.ArrayOfInt listZonesId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public customerArea.wcf_WsThemis.ArrayOfInt listFloorsId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public customerArea.wcf_WsThemis.ArrayOfInt listSectionsId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public customerArea.wcf_WsThemis.ArrayOfInt listCategoriesId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string panierFormate;
        
        public LoadGrilleTarifsRequestBody() {
        }
        
        public LoadGrilleTarifsRequestBody(int structureId, int langId, int identiteId, int profilAcheteurId, int eventId, int sessionId, customerArea.wcf_WsThemis.ArrayOfInt listZonesId, customerArea.wcf_WsThemis.ArrayOfInt listFloorsId, customerArea.wcf_WsThemis.ArrayOfInt listSectionsId, customerArea.wcf_WsThemis.ArrayOfInt listCategoriesId, string panierFormate) {
            this.structureId = structureId;
            this.langId = langId;
            this.identiteId = identiteId;
            this.profilAcheteurId = profilAcheteurId;
            this.eventId = eventId;
            this.sessionId = sessionId;
            this.listZonesId = listZonesId;
            this.listFloorsId = listFloorsId;
            this.listSectionsId = listSectionsId;
            this.listCategoriesId = listCategoriesId;
            this.panierFormate = panierFormate;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class LoadGrilleTarifsResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="LoadGrilleTarifsResponse", Namespace="http://tempuri.org/", Order=0)]
        public customerArea.wcf_WsThemis.LoadGrilleTarifsResponseBody Body;
        
        public LoadGrilleTarifsResponse() {
        }
        
        public LoadGrilleTarifsResponse(customerArea.wcf_WsThemis.LoadGrilleTarifsResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class LoadGrilleTarifsResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public customerArea.wcf_WsThemis.GestionPlaceEntity[] LoadGrilleTarifsResult;
        
        public LoadGrilleTarifsResponseBody() {
        }
        
        public LoadGrilleTarifsResponseBody(customerArea.wcf_WsThemis.GestionPlaceEntity[] LoadGrilleTarifsResult) {
            this.LoadGrilleTarifsResult = LoadGrilleTarifsResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class LoadGrilleTarifsPlacementRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="LoadGrilleTarifsPlacement", Namespace="http://tempuri.org/", Order=0)]
        public customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequestBody Body;
        
        public LoadGrilleTarifsPlacementRequest() {
        }
        
        public LoadGrilleTarifsPlacementRequest(customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class LoadGrilleTarifsPlacementRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public int structureId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public int langId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public int identiteId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public int profilAcheteurId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public int eventId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=5)]
        public int sessionId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public customerArea.wcf_WsThemis.ArrayOfInt listZonesId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public customerArea.wcf_WsThemis.ArrayOfInt listFloorsId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public customerArea.wcf_WsThemis.ArrayOfInt listSectionsId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public customerArea.wcf_WsThemis.ArrayOfInt listCategoriesId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string panierFormate;
        
        public LoadGrilleTarifsPlacementRequestBody() {
        }
        
        public LoadGrilleTarifsPlacementRequestBody(int structureId, int langId, int identiteId, int profilAcheteurId, int eventId, int sessionId, customerArea.wcf_WsThemis.ArrayOfInt listZonesId, customerArea.wcf_WsThemis.ArrayOfInt listFloorsId, customerArea.wcf_WsThemis.ArrayOfInt listSectionsId, customerArea.wcf_WsThemis.ArrayOfInt listCategoriesId, string panierFormate) {
            this.structureId = structureId;
            this.langId = langId;
            this.identiteId = identiteId;
            this.profilAcheteurId = profilAcheteurId;
            this.eventId = eventId;
            this.sessionId = sessionId;
            this.listZonesId = listZonesId;
            this.listFloorsId = listFloorsId;
            this.listSectionsId = listSectionsId;
            this.listCategoriesId = listCategoriesId;
            this.panierFormate = panierFormate;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class LoadGrilleTarifsPlacementResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="LoadGrilleTarifsPlacementResponse", Namespace="http://tempuri.org/", Order=0)]
        public customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementResponseBody Body;
        
        public LoadGrilleTarifsPlacementResponse() {
        }
        
        public LoadGrilleTarifsPlacementResponse(customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class LoadGrilleTarifsPlacementResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public customerArea.wcf_WsThemis.EventEntity[] LoadGrilleTarifsPlacementResult;
        
        public LoadGrilleTarifsPlacementResponseBody() {
        }
        
        public LoadGrilleTarifsPlacementResponseBody(customerArea.wcf_WsThemis.EventEntity[] LoadGrilleTarifsPlacementResult) {
            this.LoadGrilleTarifsPlacementResult = LoadGrilleTarifsPlacementResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface Iwcf_wsThemisChannel : customerArea.wcf_WsThemis.Iwcf_wsThemis, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class Iwcf_wsThemisClient : System.ServiceModel.ClientBase<customerArea.wcf_WsThemis.Iwcf_wsThemis>, customerArea.wcf_WsThemis.Iwcf_wsThemis {
        
        public Iwcf_wsThemisClient() {
        }
        
        public Iwcf_wsThemisClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public Iwcf_wsThemisClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public Iwcf_wsThemisClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public Iwcf_wsThemisClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public ws_DTO.IdentityInfoCompEntity[] GetInfoCompIDList(string StructureID, string userID, string listInfoCompId) {
            return base.Channel.GetInfoCompIDList(StructureID, userID, listInfoCompId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentityInfoCompEntity[]> GetInfoCompIDListAsync(string StructureID, string userID, string listInfoCompId) {
            return base.Channel.GetInfoCompIDListAsync(StructureID, userID, listInfoCompId);
        }
        
        public ws_DTO.InfoCompEntity[] GetListInfoCompOnIdentite(string structureId, int identiteId, int[] listInfoCompId) {
            return base.Channel.GetListInfoCompOnIdentite(structureId, identiteId, listInfoCompId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.InfoCompEntity[]> GetListInfoCompOnIdentiteAsync(string structureId, int identiteId, int[] listInfoCompId) {
            return base.Channel.GetListInfoCompOnIdentiteAsync(structureId, identiteId, listInfoCompId);
        }
        
        public ws_DTO.ProfilAcheteurEntity ProfilAcheteurLogin(string idStructure, string login, string passWord) {
            return base.Channel.ProfilAcheteurLogin(idStructure, login, passWord);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.ProfilAcheteurEntity> ProfilAcheteurLoginAsync(string idStructure, string login, string passWord) {
            return base.Channel.ProfilAcheteurLoginAsync(idStructure, login, passWord);
        }
        
        public System.Data.DataSet GetCustomerAllPurchaseHistoryDetails(string idStructure, long identiteId, int languageId, bool WebTracing) {
            return base.Channel.GetCustomerAllPurchaseHistoryDetails(idStructure, identiteId, languageId, WebTracing);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataSet> GetCustomerAllPurchaseHistoryDetailsAsync(string idStructure, long identiteId, int languageId, bool WebTracing) {
            return base.Channel.GetCustomerAllPurchaseHistoryDetailsAsync(idStructure, identiteId, languageId, WebTracing);
        }
        
        public System.Data.DataSet GetProfilAcheteurPurchaseHistory(int idStructure, int buyerProfilId, System.DateTime dateDeb, System.DateTime dateFin) {
            return base.Channel.GetProfilAcheteurPurchaseHistory(idStructure, buyerProfilId, dateDeb, dateFin);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataSet> GetProfilAcheteurPurchaseHistoryAsync(int idStructure, int buyerProfilId, System.DateTime dateDeb, System.DateTime dateFin) {
            return base.Channel.GetProfilAcheteurPurchaseHistoryAsync(idStructure, buyerProfilId, dateDeb, dateFin);
        }
        
        public bool UpdateCustomerProfile(string idStructure, ws_DTO.IdentiteEntity identiteToUpdated, string ficheSupprimer) {
            return base.Channel.UpdateCustomerProfile(idStructure, identiteToUpdated, ficheSupprimer);
        }
        
        public System.Threading.Tasks.Task<bool> UpdateCustomerProfileAsync(string idStructure, ws_DTO.IdentiteEntity identiteToUpdated, string ficheSupprimer) {
            return base.Channel.UpdateCustomerProfileAsync(idStructure, identiteToUpdated, ficheSupprimer);
        }
        
        public ws_DTO.IdentiteEntity GetCustomerProfileOfId(string idStructure, string identiteId, int colEmail, string ficheSupprimer) {
            return base.Channel.GetCustomerProfileOfId(idStructure, identiteId, colEmail, ficheSupprimer);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> GetCustomerProfileOfIdAsync(string idStructure, string identiteId, int colEmail, string ficheSupprimer) {
            return base.Channel.GetCustomerProfileOfIdAsync(idStructure, identiteId, colEmail, ficheSupprimer);
        }
        
        public ws_DTO.GlobalAppellationEntity[] LoadCivilityNaming(string idStructure) {
            return base.Channel.LoadCivilityNaming(idStructure);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.GlobalAppellationEntity[]> LoadCivilityNamingAsync(string idStructure) {
            return base.Channel.LoadCivilityNamingAsync(idStructure);
        }
        
        public ws_DTO.GlobalTitreEntity[] LoadFunctions(string structureId) {
            return base.Channel.LoadFunctions(structureId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.GlobalTitreEntity[]> LoadFunctionsAsync(string structureId) {
            return base.Channel.LoadFunctionsAsync(structureId);
        }
        
        public bool UpdateListInfoCompOnIdentite(string structureId, int identiteId, ws_DTO.InfoCompEntity[] lstInfoComps) {
            return base.Channel.UpdateListInfoCompOnIdentite(structureId, identiteId, lstInfoComps);
        }
        
        public System.Threading.Tasks.Task<bool> UpdateListInfoCompOnIdentiteAsync(string structureId, int identiteId, ws_DTO.InfoCompEntity[] lstInfoComps) {
            return base.Channel.UpdateListInfoCompOnIdentiteAsync(structureId, identiteId, lstInfoComps);
        }
        
        public bool UpdateInfoComp(string structureId, int identiteId, int infoCompId, string action) {
            return base.Channel.UpdateInfoComp(structureId, identiteId, infoCompId, action);
        }
        
        public System.Threading.Tasks.Task<bool> UpdateInfoCompAsync(string structureId, int identiteId, int infoCompId, string action) {
            return base.Channel.UpdateInfoCompAsync(structureId, identiteId, infoCompId, action);
        }
        
        public ws_DTO.IdentiteEntity GetCustomerProfileOfEmailOrIdentiteId(string idStructure, string email, string passwCrypte, string identiteId, int numPostal, string ficheSupprimer) {
            return base.Channel.GetCustomerProfileOfEmailOrIdentiteId(idStructure, email, passwCrypte, identiteId, numPostal, ficheSupprimer);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> GetCustomerProfileOfEmailOrIdentiteIdAsync(string idStructure, string email, string passwCrypte, string identiteId, int numPostal, string ficheSupprimer) {
            return base.Channel.GetCustomerProfileOfEmailOrIdentiteIdAsync(idStructure, email, passwCrypte, identiteId, numPostal, ficheSupprimer);
        }
        
        public int CreateCustomerProfileAndReturnID(string idStructure, ws_DTO.IdentiteEntity identiteToCreate, byte[] encrypted, bool CheckEMailUnicity, string numPostal, int operateurId) {
            return base.Channel.CreateCustomerProfileAndReturnID(idStructure, identiteToCreate, encrypted, CheckEMailUnicity, numPostal, operateurId);
        }
        
        public System.Threading.Tasks.Task<int> CreateCustomerProfileAndReturnIDAsync(string idStructure, ws_DTO.IdentiteEntity identiteToCreate, byte[] encrypted, bool CheckEMailUnicity, string numPostal, int operateurId) {
            return base.Channel.CreateCustomerProfileAndReturnIDAsync(idStructure, identiteToCreate, encrypted, CheckEMailUnicity, numPostal, operateurId);
        }
        
        public ws_DTO.DeviseEntity[] LoadDevise(string idStructure) {
            return base.Channel.LoadDevise(idStructure);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.DeviseEntity[]> LoadDeviseAsync(string idStructure) {
            return base.Channel.LoadDeviseAsync(idStructure);
        }
        
        public ws_DTO.EventEntity[] LoadEventsListWaitList(string idStructure, string userLang) {
            return base.Channel.LoadEventsListWaitList(idStructure, userLang);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.EventEntity[]> LoadEventsListWaitListAsync(string idStructure, string userLang) {
            return base.Channel.LoadEventsListWaitListAsync(idStructure, userLang);
        }
        
        public bool InsertWaitList(string idStructure, ws_DTO.ListeAttenteEntity listeAttente) {
            return base.Channel.InsertWaitList(idStructure, listeAttente);
        }
        
        public System.Threading.Tasks.Task<bool> InsertWaitListAsync(string idStructure, ws_DTO.ListeAttenteEntity listeAttente) {
            return base.Channel.InsertWaitListAsync(idStructure, listeAttente);
        }
        
        public ws_DTO.ListeAttenteEntity[] GetWaitListOfIdentite(string idStructure, int identiteId, string language) {
            return base.Channel.GetWaitListOfIdentite(idStructure, identiteId, language);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.ListeAttenteEntity[]> GetWaitListOfIdentiteAsync(string idStructure, int identiteId, string language) {
            return base.Channel.GetWaitListOfIdentiteAsync(idStructure, identiteId, language);
        }
        
        public bool DeleteIdentiteOfWaitList(string structureId, int waitListId) {
            return base.Channel.DeleteIdentiteOfWaitList(structureId, waitListId);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteIdentiteOfWaitListAsync(string structureId, int waitListId) {
            return base.Channel.DeleteIdentiteOfWaitListAsync(structureId, waitListId);
        }
        
        public ws_DTO.EventEntity[] LoadEventsListOfToday(int structureId, string userLang, int duration) {
            return base.Channel.LoadEventsListOfToday(structureId, userLang, duration);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.EventEntity[]> LoadEventsListOfTodayAsync(int structureId, string userLang, int duration) {
            return base.Channel.LoadEventsListOfTodayAsync(structureId, userLang, duration);
        }
        
        public ws_DTO.CoupeFileEntity[] GetListCoupeFileCommandes(int structureId, string userLang, ws_DTO.CoupeFileEntity coupeFile) {
            return base.Channel.GetListCoupeFileCommandes(structureId, userLang, coupeFile);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.CoupeFileEntity[]> GetListCoupeFileCommandesAsync(int structureId, string userLang, ws_DTO.CoupeFileEntity coupeFile) {
            return base.Channel.GetListCoupeFileCommandesAsync(structureId, userLang, coupeFile);
        }
        
        public bool UpdateDossierCommentaire(int structureId, string userLang, ws_DTO.Dossier dossierToUpdate) {
            return base.Channel.UpdateDossierCommentaire(structureId, userLang, dossierToUpdate);
        }
        
        public System.Threading.Tasks.Task<bool> UpdateDossierCommentaireAsync(int structureId, string userLang, ws_DTO.Dossier dossierToUpdate) {
            return base.Channel.UpdateDossierCommentaireAsync(structureId, userLang, dossierToUpdate);
        }
        
        public string[] TAH_MakePDF_FromCmdCoupeFile(int structureId, int cmdId, int filiereid, int operateurid, int identiteId, string pathPdf, string consommateurname, string langIso, string nomCmd, int[] lstDossierId) {
            return base.Channel.TAH_MakePDF_FromCmdCoupeFile(structureId, cmdId, filiereid, operateurid, identiteId, pathPdf, consommateurname, langIso, nomCmd, lstDossierId);
        }
        
        public System.Threading.Tasks.Task<string[]> TAH_MakePDF_FromCmdCoupeFileAsync(int structureId, int cmdId, int filiereid, int operateurid, int identiteId, string pathPdf, string consommateurname, string langIso, string nomCmd, int[] lstDossierId) {
            return base.Channel.TAH_MakePDF_FromCmdCoupeFileAsync(structureId, cmdId, filiereid, operateurid, identiteId, pathPdf, consommateurname, langIso, nomCmd, lstDossierId);
        }
        
        public ws_DTO.BasketEntity GetCurrentBasketAbonnement(int structureId, int identiteID, int webUserID) {
            return base.Channel.GetCurrentBasketAbonnement(structureId, identiteID, webUserID);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.BasketEntity> GetCurrentBasketAbonnementAsync(int structureId, int identiteID, int webUserID) {
            return base.Channel.GetCurrentBasketAbonnementAsync(structureId, identiteID, webUserID);
        }
        
        public ws_DTO.objets_liaisons.CustomBasket GetCurrentCustomBasketAbonnement(int structureId, int identiteID, int basketID) {
            return base.Channel.GetCurrentCustomBasketAbonnement(structureId, identiteID, basketID);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.objets_liaisons.CustomBasket> GetCurrentCustomBasketAbonnementAsync(int structureId, int identiteID, int basketID) {
            return base.Channel.GetCurrentCustomBasketAbonnementAsync(structureId, identiteID, basketID);
        }
        
        public ws_DTO.LanguageEntity[] LoadLanguages(int structureId) {
            return base.Channel.LoadLanguages(structureId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.LanguageEntity[]> LoadLanguagesAsync(int structureId) {
            return base.Channel.LoadLanguagesAsync(structureId);
        }
        
        public int GetLangueIdOfLangCode(int structureId, string langCode) {
            return base.Channel.GetLangueIdOfLangCode(structureId, langCode);
        }
        
        public System.Threading.Tasks.Task<int> GetLangueIdOfLangCodeAsync(int structureId, string langCode) {
            return base.Channel.GetLangueIdOfLangCodeAsync(structureId, langCode);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        customerArea.wcf_WsThemis.nextDelayRefreshResponse customerArea.wcf_WsThemis.Iwcf_wsThemis.nextDelayRefresh(customerArea.wcf_WsThemis.nextDelayRefreshRequest request) {
            return base.Channel.nextDelayRefresh(request);
        }
        
        public int nextDelayRefresh(string structureId, int defaultDelayS, string typeSite) {
            customerArea.wcf_WsThemis.nextDelayRefreshRequest inValue = new customerArea.wcf_WsThemis.nextDelayRefreshRequest();
            inValue.Body = new customerArea.wcf_WsThemis.nextDelayRefreshRequestBody();
            inValue.Body.structureId = structureId;
            inValue.Body.defaultDelayS = defaultDelayS;
            inValue.Body.typeSite = typeSite;
            customerArea.wcf_WsThemis.nextDelayRefreshResponse retVal = ((customerArea.wcf_WsThemis.Iwcf_wsThemis)(this)).nextDelayRefresh(inValue);
            return retVal.Body.nextDelayRefreshResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<customerArea.wcf_WsThemis.nextDelayRefreshResponse> customerArea.wcf_WsThemis.Iwcf_wsThemis.nextDelayRefreshAsync(customerArea.wcf_WsThemis.nextDelayRefreshRequest request) {
            return base.Channel.nextDelayRefreshAsync(request);
        }
        
        public System.Threading.Tasks.Task<customerArea.wcf_WsThemis.nextDelayRefreshResponse> nextDelayRefreshAsync(string structureId, int defaultDelayS, string typeSite) {
            customerArea.wcf_WsThemis.nextDelayRefreshRequest inValue = new customerArea.wcf_WsThemis.nextDelayRefreshRequest();
            inValue.Body = new customerArea.wcf_WsThemis.nextDelayRefreshRequestBody();
            inValue.Body.structureId = structureId;
            inValue.Body.defaultDelayS = defaultDelayS;
            inValue.Body.typeSite = typeSite;
            return ((customerArea.wcf_WsThemis.Iwcf_wsThemis)(this)).nextDelayRefreshAsync(inValue);
        }
        
        public int DeleteMyIdentite(string structureId, int identiteId) {
            return base.Channel.DeleteMyIdentite(structureId, identiteId);
        }
        
        public System.Threading.Tasks.Task<int> DeleteMyIdentiteAsync(string structureId, int identiteId) {
            return base.Channel.DeleteMyIdentiteAsync(structureId, identiteId);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        customerArea.wcf_WsThemis.LoadEventsResponse customerArea.wcf_WsThemis.Iwcf_wsThemis.LoadEvents(customerArea.wcf_WsThemis.LoadEventsRequest request) {
            return base.Channel.LoadEvents(request);
        }
        
        public System.Data.DataSet LoadEvents(int structureId, string langCode, int identiteId, int paId, customerArea.wcf_WsThemis.ArrayOfInt listOffres) {
            customerArea.wcf_WsThemis.LoadEventsRequest inValue = new customerArea.wcf_WsThemis.LoadEventsRequest();
            inValue.Body = new customerArea.wcf_WsThemis.LoadEventsRequestBody();
            inValue.Body.structureId = structureId;
            inValue.Body.langCode = langCode;
            inValue.Body.identiteId = identiteId;
            inValue.Body.paId = paId;
            inValue.Body.listOffres = listOffres;
            customerArea.wcf_WsThemis.LoadEventsResponse retVal = ((customerArea.wcf_WsThemis.Iwcf_wsThemis)(this)).LoadEvents(inValue);
            return retVal.Body.LoadEventsResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<customerArea.wcf_WsThemis.LoadEventsResponse> customerArea.wcf_WsThemis.Iwcf_wsThemis.LoadEventsAsync(customerArea.wcf_WsThemis.LoadEventsRequest request) {
            return base.Channel.LoadEventsAsync(request);
        }
        
        public System.Threading.Tasks.Task<customerArea.wcf_WsThemis.LoadEventsResponse> LoadEventsAsync(int structureId, string langCode, int identiteId, int paId, customerArea.wcf_WsThemis.ArrayOfInt listOffres) {
            customerArea.wcf_WsThemis.LoadEventsRequest inValue = new customerArea.wcf_WsThemis.LoadEventsRequest();
            inValue.Body = new customerArea.wcf_WsThemis.LoadEventsRequestBody();
            inValue.Body.structureId = structureId;
            inValue.Body.langCode = langCode;
            inValue.Body.identiteId = identiteId;
            inValue.Body.paId = paId;
            inValue.Body.listOffres = listOffres;
            return ((customerArea.wcf_WsThemis.Iwcf_wsThemis)(this)).LoadEventsAsync(inValue);
        }
        
        public ws_DTO.EventEntity[] LoadEventsInGp(int structureId, string langCode, int[] listEvents) {
            return base.Channel.LoadEventsInGp(structureId, langCode, listEvents);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.EventEntity[]> LoadEventsInGpAsync(int structureId, string langCode, int[] listEvents) {
            return base.Channel.LoadEventsInGpAsync(structureId, langCode, listEvents);
        }
        
        public ws_DTO.EventEntity[] LoadEventsOfFiliere(int structureId, string langCode, int[] listFiliereId) {
            return base.Channel.LoadEventsOfFiliere(structureId, langCode, listFiliereId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.EventEntity[]> LoadEventsOfFiliereAsync(int structureId, string langCode, int[] listFiliereId) {
            return base.Channel.LoadEventsOfFiliereAsync(structureId, langCode, listFiliereId);
        }
        
        public ws_DTO.SessionEntity[] LoadDispoFuturesSessions(int structureId) {
            return base.Channel.LoadDispoFuturesSessions(structureId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.SessionEntity[]> LoadDispoFuturesSessionsAsync(int structureId) {
            return base.Channel.LoadDispoFuturesSessionsAsync(structureId);
        }
        
        public bool UpdatePasswordIdentite(string idStructure, int _identiteId, string _password) {
            return base.Channel.UpdatePasswordIdentite(idStructure, _identiteId, _password);
        }
        
        public System.Threading.Tasks.Task<bool> UpdatePasswordIdentiteAsync(string idStructure, int _identiteId, string _password) {
            return base.Channel.UpdatePasswordIdentiteAsync(idStructure, _identiteId, _password);
        }
        
        public ws_DTO.IdentiteEntity LoadIdentite(int structureId, string email, int id, int colPostalTel) {
            return base.Channel.LoadIdentite(structureId, email, id, colPostalTel);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> LoadIdentiteAsync(int structureId, string email, int id, int colPostalTel) {
            return base.Channel.LoadIdentiteAsync(structureId, email, id, colPostalTel);
        }
        
        public string CheckIdentite(int structureId, ws_DTO.IdentiteEntity ident) {
            return base.Channel.CheckIdentite(structureId, ident);
        }
        
        public System.Threading.Tasks.Task<string> CheckIdentiteAsync(int structureId, ws_DTO.IdentiteEntity ident) {
            return base.Channel.CheckIdentiteAsync(structureId, ident);
        }
        
        public ws_DTO.IdentiteEntity FillDroitsFacture(int idstructure, int infocompSeuilBlocage, ws_DTO.IdentiteEntity identity) {
            return base.Channel.FillDroitsFacture(idstructure, infocompSeuilBlocage, identity);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> FillDroitsFactureAsync(int idstructure, int infocompSeuilBlocage, ws_DTO.IdentiteEntity identity) {
            return base.Channel.FillDroitsFactureAsync(idstructure, infocompSeuilBlocage, identity);
        }
        
        public ws_DTO.IdentiteEntity FillAcompte(int idstructure, int[] listModesPaiementsPrisEnCompte, ws_DTO.IdentiteEntity identity) {
            return base.Channel.FillAcompte(idstructure, listModesPaiementsPrisEnCompte, identity);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> FillAcompteAsync(int idstructure, int[] listModesPaiementsPrisEnCompte, ws_DTO.IdentiteEntity identity) {
            return base.Channel.FillAcompteAsync(idstructure, listModesPaiementsPrisEnCompte, identity);
        }
        
        public ws_DTO.Commande[] LoadCommandes(int structureId, int identiteId, int[] colCommandeId, int[] colFormuleId, string[] colEtat) {
            return base.Channel.LoadCommandes(structureId, identiteId, colCommandeId, colFormuleId, colEtat);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.Commande[]> LoadCommandesAsync(int structureId, int identiteId, int[] colCommandeId, int[] colFormuleId, string[] colEtat) {
            return base.Channel.LoadCommandesAsync(structureId, identiteId, colCommandeId, colFormuleId, colEtat);
        }
        
        public ws_DTO.IdentiteEntity[] FanCard_LoadIdentiteChild(int structureId, int identiteIdParent, string listFormules, string listEtats) {
            return base.Channel.FanCard_LoadIdentiteChild(structureId, identiteIdParent, listFormules, listEtats);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentiteEntity[]> FanCard_LoadIdentiteChildAsync(int structureId, int identiteIdParent, string listFormules, string listEtats) {
            return base.Channel.FanCard_LoadIdentiteChildAsync(structureId, identiteIdParent, listFormules, listEtats);
        }
        
        public ws_DTO.IdentiteEntity FanCard_AddRelation(int structureId, int identiteIdParent, string checkValue) {
            return base.Channel.FanCard_AddRelation(structureId, identiteIdParent, checkValue);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> FanCard_AddRelationAsync(int structureId, int identiteIdParent, string checkValue) {
            return base.Channel.FanCard_AddRelationAsync(structureId, identiteIdParent, checkValue);
        }
        
        public bool FanCard_UpdateRelation(int structureId, int identiteIdParent, string checkValue) {
            return base.Channel.FanCard_UpdateRelation(structureId, identiteIdParent, checkValue);
        }
        
        public System.Threading.Tasks.Task<bool> FanCard_UpdateRelationAsync(int structureId, int identiteIdParent, string checkValue) {
            return base.Channel.FanCard_UpdateRelationAsync(structureId, identiteIdParent, checkValue);
        }
        
        public ws_DTO.objets_liaisons.DossierIdentiteEntity[] FanCard_LoadAllIdentiteCommandesFans(int structureId, int identiteIdParent, string listFormules, string listEtats, bool commandValidOnly, int manifReference) {
            return base.Channel.FanCard_LoadAllIdentiteCommandesFans(structureId, identiteIdParent, listFormules, listEtats, commandValidOnly, manifReference);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.objets_liaisons.DossierIdentiteEntity[]> FanCard_LoadAllIdentiteCommandesFansAsync(int structureId, int identiteIdParent, string listFormules, string listEtats, bool commandValidOnly, int manifReference) {
            return base.Channel.FanCard_LoadAllIdentiteCommandesFansAsync(structureId, identiteIdParent, listFormules, listEtats, commandValidOnly, manifReference);
        }
        
        public bool IsGestionnaireReaboFans(int structureId, int identiteId) {
            return base.Channel.IsGestionnaireReaboFans(structureId, identiteId);
        }
        
        public System.Threading.Tasks.Task<bool> IsGestionnaireReaboFansAsync(int structureId, int identiteId) {
            return base.Channel.IsGestionnaireReaboFansAsync(structureId, identiteId);
        }
        
        public ws_DTO.IdentiteEntity FanCard_DeleteRelation(int structureId, int identiteIdParent, int identiteIdChild, string identiteIdChildCheckValue) {
            return base.Channel.FanCard_DeleteRelation(structureId, identiteIdParent, identiteIdChild, identiteIdChildCheckValue);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentiteEntity> FanCard_DeleteRelationAsync(int structureId, int identiteIdParent, int identiteIdChild, string identiteIdChildCheckValue) {
            return base.Channel.FanCard_DeleteRelationAsync(structureId, identiteIdParent, identiteIdChild, identiteIdChildCheckValue);
        }
        
        public ws_DTO.wt.DemandPasswordResetEntity GetDemandResetPasswordById(int idStructure, int _demandResetPasswordId) {
            return base.Channel.GetDemandResetPasswordById(idStructure, _demandResetPasswordId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.wt.DemandPasswordResetEntity> GetDemandResetPasswordByIdAsync(int idStructure, int _demandResetPasswordId) {
            return base.Channel.GetDemandResetPasswordByIdAsync(idStructure, _demandResetPasswordId);
        }
        
        public bool UpdateUseDateDemandResetPassword(int idStructure, int _demandResetPasswordId) {
            return base.Channel.UpdateUseDateDemandResetPassword(idStructure, _demandResetPasswordId);
        }
        
        public System.Threading.Tasks.Task<bool> UpdateUseDateDemandResetPasswordAsync(int idStructure, int _demandResetPasswordId) {
            return base.Channel.UpdateUseDateDemandResetPasswordAsync(idStructure, _demandResetPasswordId);
        }
        
        public int AddDemandResetPassword(int idStructure, string _ipAddress, int _identityID) {
            return base.Channel.AddDemandResetPassword(idStructure, _ipAddress, _identityID);
        }
        
        public System.Threading.Tasks.Task<int> AddDemandResetPasswordAsync(int idStructure, string _ipAddress, int _identityID) {
            return base.Channel.AddDemandResetPasswordAsync(idStructure, _ipAddress, _identityID);
        }
        
        public ws_DTO.IdentiteEntity[] getConsommateursForSeances(int structureId, int identiteId, string langCode, int ipostalTelEmail, int[] listSessions) {
            return base.Channel.getConsommateursForSeances(structureId, identiteId, langCode, ipostalTelEmail, listSessions);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentiteEntity[]> getConsommateursForSeancesAsync(int structureId, int identiteId, string langCode, int ipostalTelEmail, int[] listSessions) {
            return base.Channel.getConsommateursForSeancesAsync(structureId, identiteId, langCode, ipostalTelEmail, listSessions);
        }
        
        public ws_DTO.IdentiteEntity[] getConsommateurs(int structureId, int identiteId, string langCode, int ipostalTelEmail) {
            return base.Channel.getConsommateurs(structureId, identiteId, langCode, ipostalTelEmail);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentiteEntity[]> getConsommateursAsync(int structureId, int identiteId, string langCode, int ipostalTelEmail) {
            return base.Channel.getConsommateursAsync(structureId, identiteId, langCode, ipostalTelEmail);
        }
        
        public int AddLinkConsumers(int structureId, int identiteId, int consumerId) {
            return base.Channel.AddLinkConsumers(structureId, identiteId, consumerId);
        }
        
        public System.Threading.Tasks.Task<int> AddLinkConsumersAsync(int structureId, int identiteId, int consumerId) {
            return base.Channel.AddLinkConsumersAsync(structureId, identiteId, consumerId);
        }
        
        public bool UnLinkConsumers(int structureId, int identiteId, int consumerId) {
            return base.Channel.UnLinkConsumers(structureId, identiteId, consumerId);
        }
        
        public System.Threading.Tasks.Task<bool> UnLinkConsumersAsync(int structureId, int identiteId, int consumerId) {
            return base.Channel.UnLinkConsumersAsync(structureId, identiteId, consumerId);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        customerArea.wcf_WsThemis.LoadGrilleTarifsResponse customerArea.wcf_WsThemis.Iwcf_wsThemis.LoadGrilleTarifs(customerArea.wcf_WsThemis.LoadGrilleTarifsRequest request) {
            return base.Channel.LoadGrilleTarifs(request);
        }
        
        public customerArea.wcf_WsThemis.GestionPlaceEntity[] LoadGrilleTarifs(int structureId, int langId, int identiteId, int profilAcheteurId, int eventId, int sessionId, customerArea.wcf_WsThemis.ArrayOfInt listZonesId, customerArea.wcf_WsThemis.ArrayOfInt listFloorsId, customerArea.wcf_WsThemis.ArrayOfInt listSectionsId, customerArea.wcf_WsThemis.ArrayOfInt listCategoriesId, string panierFormate) {
            customerArea.wcf_WsThemis.LoadGrilleTarifsRequest inValue = new customerArea.wcf_WsThemis.LoadGrilleTarifsRequest();
            inValue.Body = new customerArea.wcf_WsThemis.LoadGrilleTarifsRequestBody();
            inValue.Body.structureId = structureId;
            inValue.Body.langId = langId;
            inValue.Body.identiteId = identiteId;
            inValue.Body.profilAcheteurId = profilAcheteurId;
            inValue.Body.eventId = eventId;
            inValue.Body.sessionId = sessionId;
            inValue.Body.listZonesId = listZonesId;
            inValue.Body.listFloorsId = listFloorsId;
            inValue.Body.listSectionsId = listSectionsId;
            inValue.Body.listCategoriesId = listCategoriesId;
            inValue.Body.panierFormate = panierFormate;
            customerArea.wcf_WsThemis.LoadGrilleTarifsResponse retVal = ((customerArea.wcf_WsThemis.Iwcf_wsThemis)(this)).LoadGrilleTarifs(inValue);
            return retVal.Body.LoadGrilleTarifsResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<customerArea.wcf_WsThemis.LoadGrilleTarifsResponse> customerArea.wcf_WsThemis.Iwcf_wsThemis.LoadGrilleTarifsAsync(customerArea.wcf_WsThemis.LoadGrilleTarifsRequest request) {
            return base.Channel.LoadGrilleTarifsAsync(request);
        }
        
        public System.Threading.Tasks.Task<customerArea.wcf_WsThemis.LoadGrilleTarifsResponse> LoadGrilleTarifsAsync(int structureId, int langId, int identiteId, int profilAcheteurId, int eventId, int sessionId, customerArea.wcf_WsThemis.ArrayOfInt listZonesId, customerArea.wcf_WsThemis.ArrayOfInt listFloorsId, customerArea.wcf_WsThemis.ArrayOfInt listSectionsId, customerArea.wcf_WsThemis.ArrayOfInt listCategoriesId, string panierFormate) {
            customerArea.wcf_WsThemis.LoadGrilleTarifsRequest inValue = new customerArea.wcf_WsThemis.LoadGrilleTarifsRequest();
            inValue.Body = new customerArea.wcf_WsThemis.LoadGrilleTarifsRequestBody();
            inValue.Body.structureId = structureId;
            inValue.Body.langId = langId;
            inValue.Body.identiteId = identiteId;
            inValue.Body.profilAcheteurId = profilAcheteurId;
            inValue.Body.eventId = eventId;
            inValue.Body.sessionId = sessionId;
            inValue.Body.listZonesId = listZonesId;
            inValue.Body.listFloorsId = listFloorsId;
            inValue.Body.listSectionsId = listSectionsId;
            inValue.Body.listCategoriesId = listCategoriesId;
            inValue.Body.panierFormate = panierFormate;
            return ((customerArea.wcf_WsThemis.Iwcf_wsThemis)(this)).LoadGrilleTarifsAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementResponse customerArea.wcf_WsThemis.Iwcf_wsThemis.LoadGrilleTarifsPlacement(customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequest request) {
            return base.Channel.LoadGrilleTarifsPlacement(request);
        }
        
        public customerArea.wcf_WsThemis.EventEntity[] LoadGrilleTarifsPlacement(int structureId, int langId, int identiteId, int profilAcheteurId, int eventId, int sessionId, customerArea.wcf_WsThemis.ArrayOfInt listZonesId, customerArea.wcf_WsThemis.ArrayOfInt listFloorsId, customerArea.wcf_WsThemis.ArrayOfInt listSectionsId, customerArea.wcf_WsThemis.ArrayOfInt listCategoriesId, string panierFormate) {
            customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequest inValue = new customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequest();
            inValue.Body = new customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequestBody();
            inValue.Body.structureId = structureId;
            inValue.Body.langId = langId;
            inValue.Body.identiteId = identiteId;
            inValue.Body.profilAcheteurId = profilAcheteurId;
            inValue.Body.eventId = eventId;
            inValue.Body.sessionId = sessionId;
            inValue.Body.listZonesId = listZonesId;
            inValue.Body.listFloorsId = listFloorsId;
            inValue.Body.listSectionsId = listSectionsId;
            inValue.Body.listCategoriesId = listCategoriesId;
            inValue.Body.panierFormate = panierFormate;
            customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementResponse retVal = ((customerArea.wcf_WsThemis.Iwcf_wsThemis)(this)).LoadGrilleTarifsPlacement(inValue);
            return retVal.Body.LoadGrilleTarifsPlacementResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementResponse> customerArea.wcf_WsThemis.Iwcf_wsThemis.LoadGrilleTarifsPlacementAsync(customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequest request) {
            return base.Channel.LoadGrilleTarifsPlacementAsync(request);
        }
        
        public System.Threading.Tasks.Task<customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementResponse> LoadGrilleTarifsPlacementAsync(int structureId, int langId, int identiteId, int profilAcheteurId, int eventId, int sessionId, customerArea.wcf_WsThemis.ArrayOfInt listZonesId, customerArea.wcf_WsThemis.ArrayOfInt listFloorsId, customerArea.wcf_WsThemis.ArrayOfInt listSectionsId, customerArea.wcf_WsThemis.ArrayOfInt listCategoriesId, string panierFormate) {
            customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequest inValue = new customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequest();
            inValue.Body = new customerArea.wcf_WsThemis.LoadGrilleTarifsPlacementRequestBody();
            inValue.Body.structureId = structureId;
            inValue.Body.langId = langId;
            inValue.Body.identiteId = identiteId;
            inValue.Body.profilAcheteurId = profilAcheteurId;
            inValue.Body.eventId = eventId;
            inValue.Body.sessionId = sessionId;
            inValue.Body.listZonesId = listZonesId;
            inValue.Body.listFloorsId = listFloorsId;
            inValue.Body.listSectionsId = listSectionsId;
            inValue.Body.listCategoriesId = listCategoriesId;
            inValue.Body.panierFormate = panierFormate;
            return ((customerArea.wcf_WsThemis.Iwcf_wsThemis)(this)).LoadGrilleTarifsPlacementAsync(inValue);
        }
        
        public ws_DTO.GestionPlaceEntity[] LoadAllInternetGrilleTarifs(int structureId, string langCode, int identiteId, int profilAcheteurId, int eventId, int sessionId, int[] listCategoriesId, int[] listTarifsId, int offerId) {
            return base.Channel.LoadAllInternetGrilleTarifs(structureId, langCode, identiteId, profilAcheteurId, eventId, sessionId, listCategoriesId, listTarifsId, offerId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.GestionPlaceEntity[]> LoadAllInternetGrilleTarifsAsync(int structureId, string langCode, int identiteId, int profilAcheteurId, int eventId, int sessionId, int[] listCategoriesId, int[] listTarifsId, int offerId) {
            return base.Channel.LoadAllInternetGrilleTarifsAsync(structureId, langCode, identiteId, profilAcheteurId, eventId, sessionId, listCategoriesId, listTarifsId, offerId);
        }
        
        public ws_DTO.LigneGrilleTarifEntity[] LoadAmounts(int structureId, string langCode, int eventId, int[] listsessionId, int[] listCategoriesId, int[] listTarifsId) {
            return base.Channel.LoadAmounts(structureId, langCode, eventId, listsessionId, listCategoriesId, listTarifsId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.LigneGrilleTarifEntity[]> LoadAmountsAsync(int structureId, string langCode, int eventId, int[] listsessionId, int[] listCategoriesId, int[] listTarifsId) {
            return base.Channel.LoadAmountsAsync(structureId, langCode, eventId, listsessionId, listCategoriesId, listTarifsId);
        }
        
        public ws_DTO.FormulaEntity[] LoadFormulas(int structureId, string langCode, int identiteId) {
            return base.Channel.LoadFormulas(structureId, langCode, identiteId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.FormulaEntity[]> LoadFormulasAsync(int structureId, string langCode, int identiteId) {
            return base.Channel.LoadFormulasAsync(structureId, langCode, identiteId);
        }
        
        public ws_DTO.EventEntityOfFormula[] LoadEventsOfFormulas(int structureId, string langCode, int identiteId, int formulaId, int[] listTarifId) {
            return base.Channel.LoadEventsOfFormulas(structureId, langCode, identiteId, formulaId, listTarifId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.EventEntityOfFormula[]> LoadEventsOfFormulasAsync(int structureId, string langCode, int identiteId, int formulaId, int[] listTarifId) {
            return base.Channel.LoadEventsOfFormulasAsync(structureId, langCode, identiteId, formulaId, listTarifId);
        }
        
        public ws_DTO.FormulaContraintesGroupeOpenEntity[] LoadContraintesOfFormulas(int structureId, string langCode, int identiteId, int formulaId) {
            return base.Channel.LoadContraintesOfFormulas(structureId, langCode, identiteId, formulaId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.FormulaContraintesGroupeOpenEntity[]> LoadContraintesOfFormulasAsync(int structureId, string langCode, int identiteId, int formulaId) {
            return base.Channel.LoadContraintesOfFormulasAsync(structureId, langCode, identiteId, formulaId);
        }
        
        public ws_DTO.EventEntity[] LoadEventsHorsAbo(int structureId, string langCode, int identiteId, int[] listFormulas, int offreId) {
            return base.Channel.LoadEventsHorsAbo(structureId, langCode, identiteId, listFormulas, offreId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.EventEntity[]> LoadEventsHorsAboAsync(int structureId, string langCode, int identiteId, int[] listFormulas, int offreId) {
            return base.Channel.LoadEventsHorsAboAsync(structureId, langCode, identiteId, listFormulas, offreId);
        }
        
        public ws_DTO.GestionPlaceEntity[] LoadGrilleTarifsHorsAbo(int structureId, string langCode, int identiteId, int profilAcheteurId, int eventId, int sessionId, int[] listZonesId, int[] listFloorsId, int[] listSectionsId, int[] listCategoriesId, string panierFormate, int[] listFormulas, int offreId) {
            return base.Channel.LoadGrilleTarifsHorsAbo(structureId, langCode, identiteId, profilAcheteurId, eventId, sessionId, listZonesId, listFloorsId, listSectionsId, listCategoriesId, panierFormate, listFormulas, offreId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.GestionPlaceEntity[]> LoadGrilleTarifsHorsAboAsync(int structureId, string langCode, int identiteId, int profilAcheteurId, int eventId, int sessionId, int[] listZonesId, int[] listFloorsId, int[] listSectionsId, int[] listCategoriesId, string panierFormate, int[] listFormulas, int offreId) {
            return base.Channel.LoadGrilleTarifsHorsAboAsync(structureId, langCode, identiteId, profilAcheteurId, eventId, sessionId, listZonesId, listFloorsId, listSectionsId, listCategoriesId, panierFormate, listFormulas, offreId);
        }
        
        public ws_DTO.ProductEntity[] LoadProductsOfFormulas(int structureId, string langCode, int identiteId, int[] listFormulas) {
            return base.Channel.LoadProductsOfFormulas(structureId, langCode, identiteId, listFormulas);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.ProductEntity[]> LoadProductsOfFormulasAsync(int structureId, string langCode, int identiteId, int[] listFormulas) {
            return base.Channel.LoadProductsOfFormulasAsync(structureId, langCode, identiteId, listFormulas);
        }
        
        public ws_DTO.ProductEntity[] LoadFraisProductsOfFormulas(int structureId, string langCode, int identiteId, int[] listFormulas) {
            return base.Channel.LoadFraisProductsOfFormulas(structureId, langCode, identiteId, listFormulas);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.ProductEntity[]> LoadFraisProductsOfFormulasAsync(int structureId, string langCode, int identiteId, int[] listFormulas) {
            return base.Channel.LoadFraisProductsOfFormulasAsync(structureId, langCode, identiteId, listFormulas);
        }
        
        public ws_DTO.ReserveEntity[] GetListeReserveForFormula(int structureId, string langCode, int formulaId, int tarifid, int eventId, int sessionId) {
            return base.Channel.GetListeReserveForFormula(structureId, langCode, formulaId, tarifid, eventId, sessionId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.ReserveEntity[]> GetListeReserveForFormulaAsync(int structureId, string langCode, int formulaId, int tarifid, int eventId, int sessionId) {
            return base.Channel.GetListeReserveForFormulaAsync(structureId, langCode, formulaId, tarifid, eventId, sessionId);
        }
        
        public ws_DTO.Produit[] LoadFraisEnvoisReabo(int structureId, int langId, int aboId) {
            return base.Channel.LoadFraisEnvoisReabo(structureId, langId, aboId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.Produit[]> LoadFraisEnvoisReaboAsync(int structureId, int langId, int aboId) {
            return base.Channel.LoadFraisEnvoisReaboAsync(structureId, langId, aboId);
        }
        
        public ws_DTO.Produit[] LoadProducts(int structureId, int langId) {
            return base.Channel.LoadProducts(structureId, langId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.Produit[]> LoadProductsAsync(int structureId, int langId) {
            return base.Channel.LoadProductsAsync(structureId, langId);
        }
        
        public ws_DTO.ProductEntity[] LoadCommonsMO_ListFormulaTarifSeance_abo(int structureId, string langCode, int[] listFormulas, int[] listTarifs, int[] listSessions) {
            return base.Channel.LoadCommonsMO_ListFormulaTarifSeance_abo(structureId, langCode, listFormulas, listTarifs, listSessions);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.ProductEntity[]> LoadCommonsMO_ListFormulaTarifSeance_aboAsync(int structureId, string langCode, int[] listFormulas, int[] listTarifs, int[] listSessions) {
            return base.Channel.LoadCommonsMO_ListFormulaTarifSeance_aboAsync(structureId, langCode, listFormulas, listTarifs, listSessions);
        }
        
        public System.Collections.Generic.Dictionary<int, int> LoadMaquettesOfMoGp(int structureId, int[] listGps, int moId) {
            return base.Channel.LoadMaquettesOfMoGp(structureId, listGps, moId);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<int, int>> LoadMaquettesOfMoGpAsync(int structureId, int[] listGps, int moId) {
            return base.Channel.LoadMaquettesOfMoGpAsync(structureId, listGps, moId);
        }
        
        public ws_DTO.BasketEntity Basket_FillFromOpen(int structureid, ws_DTO.BasketEntity bask) {
            return base.Channel.Basket_FillFromOpen(structureid, bask);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.BasketEntity> Basket_FillFromOpenAsync(int structureid, ws_DTO.BasketEntity bask) {
            return base.Channel.Basket_FillFromOpenAsync(structureid, bask);
        }
        
        public ws_DTO.BasketEntity BasketFillFromOpen(int structureId, int basketId) {
            return base.Channel.BasketFillFromOpen(structureId, basketId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.BasketEntity> BasketFillFromOpenAsync(int structureId, int basketId) {
            return base.Channel.BasketFillFromOpenAsync(structureId, basketId);
        }
        
        public bool BasketTransformation(int structureId, int basketId) {
            return base.Channel.BasketTransformation(structureId, basketId);
        }
        
        public System.Threading.Tasks.Task<bool> BasketTransformationAsync(int structureId, int basketId) {
            return base.Channel.BasketTransformationAsync(structureId, basketId);
        }
        
        public string[] TAH_MakePDF_FromCmd(int structureid, int cmdId, int filiereid, int operateurid, int identiteId, string pathPdf, string consommateurname, string langIso, string nomCmd) {
            return base.Channel.TAH_MakePDF_FromCmd(structureid, cmdId, filiereid, operateurid, identiteId, pathPdf, consommateurname, langIso, nomCmd);
        }
        
        public System.Threading.Tasks.Task<string[]> TAH_MakePDF_FromCmdAsync(int structureid, int cmdId, int filiereid, int operateurid, int identiteId, string pathPdf, string consommateurname, string langIso, string nomCmd) {
            return base.Channel.TAH_MakePDF_FromCmdAsync(structureid, cmdId, filiereid, operateurid, identiteId, pathPdf, consommateurname, langIso, nomCmd);
        }
        
        public bool EditerCommande(string _structureid, ws_DTO.Entree[] _listeentrees, int identite_id, int operateurid) {
            return base.Channel.EditerCommande(_structureid, _listeentrees, identite_id, operateurid);
        }
        
        public System.Threading.Tasks.Task<bool> EditerCommandeAsync(string _structureid, ws_DTO.Entree[] _listeentrees, int identite_id, int operateurid) {
            return base.Channel.EditerCommandeAsync(_structureid, _listeentrees, identite_id, operateurid);
        }
        
        public bool DepotVente_ispossible(int structureid) {
            return base.Channel.DepotVente_ispossible(structureid);
        }
        
        public System.Threading.Tasks.Task<bool> DepotVente_ispossibleAsync(int structureid) {
            return base.Channel.DepotVente_ispossibleAsync(structureid);
        }
        
        public ws_DTO.DepotVenteEntity DepotVente_GetMontantReprise(int structureid, int IdentityId, int OrderId, int EventId, string SeatsId) {
            return base.Channel.DepotVente_GetMontantReprise(structureid, IdentityId, OrderId, EventId, SeatsId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.DepotVenteEntity> DepotVente_GetMontantRepriseAsync(int structureid, int IdentityId, int OrderId, int EventId, string SeatsId) {
            return base.Channel.DepotVente_GetMontantRepriseAsync(structureid, IdentityId, OrderId, EventId, SeatsId);
        }
        
        public ws_DTO.DepotVenteEntity DepotVente_put(int structureid, int IdentityId, int OrderId, int EventId, string SeatsId, int OperatorId, int amount, int NbrDepotsAutorisesGroupeManif) {
            return base.Channel.DepotVente_put(structureid, IdentityId, OrderId, EventId, SeatsId, OperatorId, amount, NbrDepotsAutorisesGroupeManif);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.DepotVenteEntity> DepotVente_putAsync(int structureid, int IdentityId, int OrderId, int EventId, string SeatsId, int OperatorId, int amount, int NbrDepotsAutorisesGroupeManif) {
            return base.Channel.DepotVente_putAsync(structureid, IdentityId, OrderId, EventId, SeatsId, OperatorId, amount, NbrDepotsAutorisesGroupeManif);
        }
        
        public int MarquerConsommateur(string _structureid, ws_DTO.Entree[] _listeentrees, int identite_id) {
            return base.Channel.MarquerConsommateur(_structureid, _listeentrees, identite_id);
        }
        
        public System.Threading.Tasks.Task<int> MarquerConsommateurAsync(string _structureid, ws_DTO.Entree[] _listeentrees, int identite_id) {
            return base.Channel.MarquerConsommateurAsync(_structureid, _listeentrees, identite_id);
        }
        
        public ws_DTO.IdentityRIBEntity LoadIdentityRIB(int idStructure, int identityID) {
            return base.Channel.LoadIdentityRIB(idStructure, identityID);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.IdentityRIBEntity> LoadIdentityRIBAsync(int idStructure, int identityID) {
            return base.Channel.LoadIdentityRIBAsync(idStructure, identityID);
        }
        
        public bool AddIdentityRIB(int idStructure, ws_DTO.IdentityRIBEntity identityRIB) {
            return base.Channel.AddIdentityRIB(idStructure, identityRIB);
        }
        
        public System.Threading.Tasks.Task<bool> AddIdentityRIBAsync(int idStructure, ws_DTO.IdentityRIBEntity identityRIB) {
            return base.Channel.AddIdentityRIBAsync(idStructure, identityRIB);
        }
        
        public ws_DTO.SeatEntity[] FlagAuto(int structureId, int eventId, int sessionId, System.Collections.Generic.Dictionary<string, int> keyFormulaTarif_nbr, System.Collections.Generic.Dictionary<string, int> keyGpId_nbr_forHA, int nbrTotal, int userid) {
            return base.Channel.FlagAuto(structureId, eventId, sessionId, keyFormulaTarif_nbr, keyGpId_nbr_forHA, nbrTotal, userid);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.SeatEntity[]> FlagAutoAsync(int structureId, int eventId, int sessionId, System.Collections.Generic.Dictionary<string, int> keyFormulaTarif_nbr, System.Collections.Generic.Dictionary<string, int> keyGpId_nbr_forHA, int nbrTotal, int userid) {
            return base.Channel.FlagAutoAsync(structureId, eventId, sessionId, keyFormulaTarif_nbr, keyGpId_nbr_forHA, nbrTotal, userid);
        }
        
        public ws_DTO.SeatEntity FlagManuel_tempo(int structureId, int eventId, int sessionId, int seatId, int userid) {
            return base.Channel.FlagManuel_tempo(structureId, eventId, sessionId, seatId, userid);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.SeatEntity> FlagManuel_tempoAsync(int structureId, int eventId, int sessionId, int seatId, int userid) {
            return base.Channel.FlagManuel_tempoAsync(structureId, eventId, sessionId, seatId, userid);
        }
        
        public ws_DTO.SeatEntity UnFlagManuel_tempo(int structureId, int eventId, int sessionId, int seatId, int userid) {
            return base.Channel.UnFlagManuel_tempo(structureId, eventId, sessionId, seatId, userid);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.SeatEntity> UnFlagManuel_tempoAsync(int structureId, int eventId, int sessionId, int seatId, int userid) {
            return base.Channel.UnFlagManuel_tempoAsync(structureId, eventId, sessionId, seatId, userid);
        }
        
        public bool UnFlagSeats(int structureId, int eventId, int sessionId, int userid) {
            return base.Channel.UnFlagSeats(structureId, eventId, sessionId, userid);
        }
        
        public System.Threading.Tasks.Task<bool> UnFlagSeatsAsync(int structureId, int eventId, int sessionId, int userid) {
            return base.Channel.UnFlagSeatsAsync(structureId, eventId, sessionId, userid);
        }
        
        public ws_DTO.SeatEntity[] UnFlagSeatsManuel_tempo(int structureId, int eventId, int sessionId, int[] seatIds, int userid) {
            return base.Channel.UnFlagSeatsManuel_tempo(structureId, eventId, sessionId, seatIds, userid);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.SeatEntity[]> UnFlagSeatsManuel_tempoAsync(int structureId, int eventId, int sessionId, int[] seatIds, int userid) {
            return base.Channel.UnFlagSeatsManuel_tempoAsync(structureId, eventId, sessionId, seatIds, userid);
        }
        
        public ws_DTO.SeatEntity UnFlagSeatsManuel(int structureId, int eventId, int sessionId, int seatId, int userid) {
            return base.Channel.UnFlagSeatsManuel(structureId, eventId, sessionId, seatId, userid);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.SeatEntity> UnFlagSeatsManuelAsync(int structureId, int eventId, int sessionId, int seatId, int userid) {
            return base.Channel.UnFlagSeatsManuelAsync(structureId, eventId, sessionId, seatId, userid);
        }
        
        public ws_DTO.SeatEntity[] ReFlagSeatsManuel_tempo(int structureId, int eventId, int sessionId, int[] seatIds, int userid) {
            return base.Channel.ReFlagSeatsManuel_tempo(structureId, eventId, sessionId, seatIds, userid);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.SeatEntity[]> ReFlagSeatsManuel_tempoAsync(int structureId, int eventId, int sessionId, int[] seatIds, int userid) {
            return base.Channel.ReFlagSeatsManuel_tempoAsync(structureId, eventId, sessionId, seatIds, userid);
        }
        
        public ws_DTO.SeatEntity[] LoadSeats(int structureId, int eventId, int sessionId, int identiteId, string myFlag, string langCode) {
            return base.Channel.LoadSeats(structureId, eventId, sessionId, identiteId, myFlag, langCode);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.SeatEntity[]> LoadSeatsAsync(int structureId, int eventId, int sessionId, int identiteId, string myFlag, string langCode) {
            return base.Channel.LoadSeatsAsync(structureId, eventId, sessionId, identiteId, myFlag, langCode);
        }
        
        public ws_DTO.TextEntity[] LoadSeatsTexts(int structureId, int eventId, int sessionId, int zoneId, int floorId, int sectionId) {
            return base.Channel.LoadSeatsTexts(structureId, eventId, sessionId, zoneId, floorId, sectionId);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.TextEntity[]> LoadSeatsTextsAsync(int structureId, int eventId, int sessionId, int zoneId, int floorId, int sectionId) {
            return base.Channel.LoadSeatsTextsAsync(structureId, eventId, sessionId, zoneId, floorId, sectionId);
        }
        
        public ws_DTO.InfoCompEntity[] LoadInfoComp(string idStructure) {
            return base.Channel.LoadInfoComp(idStructure);
        }
        
        public System.Threading.Tasks.Task<ws_DTO.InfoCompEntity[]> LoadInfoCompAsync(string idStructure) {
            return base.Channel.LoadInfoCompAsync(idStructure);
        }




        public ws_DTO.ReservationEntity[] LoadReservationList(int structureId, int identityId)
        {
            return base.Channel.LoadReservationList(structureId, identityId);
        }

        public System.Threading.Tasks.Task<ws_DTO.ReservationEntity[]> LoadReservationListAsync(int structureId, int identityId)
        {
            return base.Channel.LoadReservationListAsync(structureId, identityId);
        }
    }
}
