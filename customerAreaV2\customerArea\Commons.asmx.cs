﻿using customerArea.App_Code;
using customerArea.classes;
using log4net;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Policy;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Configuration;
using System.Web.Http.Cors;
using System.Web.Script.Services;
using System.Web.Services;
using System.Xml.Linq;
using utilitaires2010;
using utilitaires2010.crypto;
using utilitaires2010.Extensions;
using utilitaires2010.sql.sqlserver;
using ws_bll;
using ws_bll.WT;
using ws_DTO;
using ws_DTO.objets_liaisons;

namespace customerArea
{
    /// <summary>
    /// Summary description for Commons
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [System.ComponentModel.ToolboxItem(false)]
    // [EnableCors(origins: "http://localhost:4646,http://localhost:8768", headers: "*", methods: "*")]
    //[EnableCors(origins: "https://indiv.themisweb.fr,http://localhost:4646,http://localhost:8768", headers: "*", methods: "*", SupportsCredentials = true)]
    [EnableCors(origins: "*", headers: "*", methods: "*")]
    // To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line. 
    [System.Web.Script.Services.ScriptService]
    //[EnableCors(origins:"http://localhost:4646,http://localhost:8768", headers:"*", methods: "post,get")]
    public class Commons : System.Web.Services.WebService
    {

        public static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        [WebMethod(EnableSession = true)]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json, UseHttpGet = false)]
        public string CheckSession()
        {
            return System.Web.HttpContext.Current.Session.SessionID;
        }

        /// <summary>
        /// deconnection du customer area (appellé depuis customArea ou depuis les sites tiers)
        /// </summary>
        /// <returns></returns>
        [WebMethod(EnableSession = true)]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json, UseHttpGet = false)]
        public string DisconnectUser()
        {
            try
            {
                logger.Debug("DisconnectUser()...");


                //string structureId = System.Web.HttpContext.Current.Request.QueryString["idstructure"].ToString();
                // System.Web.HttpContext.Current.Response.Redirect("login.aspx?idstructure=" + structureId);

                System.Web.HttpContext.Current.Session.Clear();
                System.Web.HttpContext.Current.Session.Abandon();

                System.Web.HttpContext.Current.Session.RemoveAll();


                logger.Debug("nb Session  " + System.Web.HttpContext.Current.Session.Count);
                return "ok";
            }
            catch (Exception ex)
            {
                logger.Error("dans DisconnectUser()" + ex.Message + " " + ex.StackTrace);
                return "dans disconnect:" + ex.Message;
            }
        }


        /// <summary>
        /// renvoie l'identité si elle est connecté sur le customer area
        /// </summary>
        /// <param name="idStucture"></param>
        /// <returns></returns>
        [WebMethod(EnableSession = true)]
        // [EnableCors(origins: "*", headers: "*", methods: "*")]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public string IsConnected(int idStructure, string idSession, int curruserid)
        {
            try
            {
                logger.Debug("IsConnected(" + idStructure + ")...");

                string typeRun = MyConfigurationManager.AppSettings("TypeRun");

                int identiteId = WebUserManager.GetIdentiteIdFromSessionId(typeRun, idSession, idStructure, curruserid);

                if (identiteId > 0)
                {
                    string cryptoKey = App_Code.Initialisations.GetKeyAppSettings("CryptoKey");
                    Sha1 sha1 = new Sha1(string.Format("{0,4:0000}", idStructure) + "|" + identiteId + cryptoKey);
                    string hash = sha1.getSha1();
                    return "true:" + identiteId.ToString() + ":" + hash;
                }
                return "false";
            }
            catch (Exception ex)
            {
                logger.Error("dans IsConnected(" + idStructure + ")" + ex.Message + " " + ex.StackTrace);
                throw new Exception(ex.Message);
            }
        }


        [WebMethod(EnableSession = true)]
        // [EnableCors(origins: "*", headers: "*", methods: "*")]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public string DeleteMyAccount()
        {
            int structureId = 0;
            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                structureId = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());
            }
            else
            {
                throw new Exception("Aucune structure en session");
            }

            try
            {
                int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

                int identiteId = 0;
                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");
                }

                logger.Debug("DeleteMyAccount(" + structureId + " " + identiteId + ")");


                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                int result = wcfThemis.DeleteMyIdentite(string.Format("{0,4:0000}", structureId), identiteId);

                if (result > 0)
                {
                    DisconnectUser();

                    return "success:msg_success_identity_deleted";
                }
                return "danger:msg_error_identity_not_deleted";
            }
            catch (Exception ex)
            {
                logger.Error("dans DeleteMyAccount(" + structureId + ")" + ex.Message + " " + ex.StackTrace);
                throw new Exception(ex.Message);
            }
        }



        /// <summary>
        /// Retourne la signature Widget
        /// </summary>
        /// <returns></returns>
        [WebMethod(EnableSession = true)]
        public List<string> GetWidgetSignature(int structureId)
        {
            string method = "GET";
            string signature = "";

            string toEncode = HttpContext.Current.Request.Url.Scheme + "://" +
               HttpContext.Current.Request.Url.Host + "/$" + method;

            if (HttpContext.Current.Request.Url.Host.Contains("localhost"))
                {
                toEncode = HttpContext.Current.Request.Url.Scheme + "://" +
               HttpContext.Current.Request.Url.Host + ":" + HttpContext.Current.Request.Url.Port + "/$" + method;
            }

            string widgetPartner = WebConfigurationManager.AppSettings["WidgetPartner"].ToString();
            string cachName = "widget_signature_" + method + "_" + structureId + "_" + widgetPartner + "_" + toEncode;
            if (HttpContext.Current.Cache[cachName] != null)
            {
                signature = (string)HttpContext.Current.Cache[cachName];
            }
            else
            {

                //toEncode = HttpContext.Current.Request.ApplicationPath + "/$" + method;

                string typeConn = WebConfigurationManager.AppSettings["TypeRun"].ToString();


                SqlServerConnexion cnx = DBFunctions.ConnectWSAdmin(typeConn);

                string secretKey = PartnerManager.GetSecretKey(cnx, widgetPartner);

                if (secretKey == "")
                {
                    GestionTraceManager.WriteLogError(structureId, "dans GetWidgetSignature : ");

                }

                Encoding encoding = Encoding.UTF8;
                //var md = System.Security.Cryptography.MD5CryptoServiceProvider.Create();
                HMACSHA256 hashObject = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
                byte[] signatureb = hashObject.ComputeHash(Encoding.UTF8.GetBytes(toEncode));
                signature = Convert.ToBase64String(signatureb);

                HttpContext.Current.Cache.Insert(cachName, signature, null, DateTime.Now.AddMinutes(30), TimeSpan.Zero);
            }

            return new List<string>() { widgetPartner, signature };

        }


        /// <summary>
        /// Liste les identités attachés
        /// </summary>
        /// <returns></returns>
        [WebMethod(EnableSession = true)]
        public List<IdentiteEntity> GetMyRelations()
        {
            int identiteId = 0;

            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
            {
                identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
            }
            else
            {
                throw new Exception("Aucune identité en session");
            }

            int structureId = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            try
            {
                // check hash
                string lstFormule = GetListFormules(structureId);

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                List<IdentiteEntity> listChilds = wsThemis.FanCard_LoadIdentiteChild(structureId, identiteId, lstFormule, "").ToList();

                string cryptoKey = WebConfigurationManager.AppSettings["CryptoKey"].ToString();

                foreach (IdentiteEntity idi in listChilds)
                {
                    Sha1 sha1 = new Sha1(structureId + "|" + idi.Identite_id + cryptoKey);
                    string hash = sha1.getSha1();
                    idi.hashKey = hash;
                }

                return listChilds;
            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(structureId, "GetMyRelations : " + ex.Message);
                throw ex;
            }
            //return ok;
        }


        [WebMethod(EnableSession = true)]
        public List<IdentiteEntity> GetCommandsList()
        {
            int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());
            try
            {
                int identiteId = 0;
                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");
                }

                string lstFormule = GetListFormules(idStructure);
                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                //  webShopReaboMulti.WsThemis.Iwcf_wsThemis wsThemis = new webShopReaboMulti.WsThemis.Iwcf_wsThemisClient(); // new webShopReaboMulti.WsThemis.Iwcf_wsThemis();
                List<IdentiteEntity> listChilds = wsThemis.FanCard_LoadIdentiteChild(idStructure, identiteId, lstFormule, "").ToList();
                // var colChilds = new Collection<IdentiteEntity>(listChilds);

                string cryptoKey = WebConfigurationManager.AppSettings["CryptoKey"].ToString();
                foreach (IdentiteEntity idi in listChilds)
                {
                    Sha1 sha1 = new Sha1(idStructure + "|" + idi.Identite_id + cryptoKey);
                    string hash = sha1.getSha1();
                    idi.hashKey = hash;
                }
                return listChilds;
            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(idStructure, "GetCommandsList : " + ex.Message);
                throw ex;
            }
        }

        /// <summary>
        /// retourne une identite avec le seuil et le solde de rempli
        /// </summary>
        /// <returns></returns>
        [WebMethod(EnableSession = true)]
        public IdentiteEntity GetDroitsFacture()
        {
            int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            var logsLevel = customerArea.App_Code.Initialisations.GetKeyAppSettings("logsLevel");
            try
            {
                int identiteId = 0;
                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");
                }

                utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
                mySSC = mySSC.GetDictionaryFromCache(idStructure);
                int infocompSeuilBlocage = 0;
                if (mySSC.Contains("FACTUREMODESEUILDEBLOCAGE"))
                {

                    if (logsLevel != LogLevel.NORMAL.ToString())
                    {
                        GestionTraceManager.WriteLog(idStructure, "Find FACTUREMODESEUILDEBLOCAGE in config.ini");
                    }
                    infocompSeuilBlocage = int.Parse(mySSC["FACTUREMODESEUILDEBLOCAGE"].ToString());
                }

                if (infocompSeuilBlocage > 0)
                {
                    customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                    IdentiteEntity identity = new IdentiteEntity()
                    {
                        Identite_id = identiteId
                    };

                    if (logsLevel != LogLevel.NORMAL.ToString())
                    {
                        GestionTraceManager.WriteLog(idStructure, "infocompSeuilBlocage :" + infocompSeuilBlocage + " variable FACTUREMODESEUILDEBLOCAGE dans le config.ini");
                    }
                    return wsThemis.FillDroitsFacture(idStructure, infocompSeuilBlocage, identity);
                }
                else
                {

                    if (logsLevel != LogLevel.NORMAL.ToString())
                    {
                        GestionTraceManager.WriteLog(idStructure, "infocompSeuilBlocage : 0 Vérifier la clé FACTUREMODESEUILDEBLOCAGE dans le config.ini");
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(idStructure, "page_loaded bakndetails");
                throw ex;
            }
        }

        [WebMethod(EnableSession = true)]
        public IdentiteEntity AddChild(string checkValue)
        {
            int structureId = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());
            int identiteId = 0;
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
            {
                identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
            }
            else
            {
                throw new Exception("Aucune identité en session");
            }

            try
            {
                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                IdentiteEntity newIdentite = wsThemis.FanCard_AddRelation(structureId, identiteId, checkValue);

                return newIdentite;
            }
            catch (Exception ex)
            {
                throw ex;
            }

            //return ok;

        }

        [WebMethod(EnableSession = true)]
        public string UpdateChild(string checkValue)
        {
            logger.Debug("UpdateChild : " + checkValue);
            if (System.Web.HttpContext.Current.Session["idstructure"] == null)
            {
                logger.Error("Structure id est null");
            }

            int structureId = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            int identiteId = 0;
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
            {
                identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
            }
            else
            {
                throw new Exception("Aucune identité en session");
            }


            try
            {
                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
                mySSC = mySSC.GetDictionaryFromCache(structureId);

                if (mySSC == null)
                {
                    logger.Error("mySSC est null");
                }

                if (mySSC.Contains("REABOUPDATEFANSONSTRUCTURE"))
                {

                    int droitUpdate = int.Parse(mySSC["REABOUPDATEFANSONSTRUCTURE"].ToString());

                    if (droitUpdate != 1)
                    {
                        GestionTraceManager.WriteLogError(structureId, "Find REABOUPDATEFANS in config.ini " + mySSC["REABOUPDATEFANSONSTRUCTURE"]);
                        logger.Debug("vérifier le config.ini REABOUPDATEFANS" + mySSC["REABOUPDATEFANSONSTRUCTURE"].ToString());
                        return "danger|title_msg_error_droit_update_fans|msg_error_droit_update_fans";
                    }
                }


                if (mySSC.Contains("REABOINTERDIREINFOCOMPUPDATEFANS"))
                {
                    int infocomp = 0;
                    if (!string.IsNullOrEmpty(mySSC["REABOINTERDIREINFOCOMPUPDATEFANS"].ToString()))
                    {
                        infocomp = int.Parse(mySSC["REABOINTERDIREINFOCOMPUPDATEFANS"].ToString());
                    }

                    List<InfoCompEntity> lstIdentInfosComps = wsThemis.LoadInfoComp(string.Format("{0,4:0000}", structureId)).ToList();

                    InfoCompEntity infocompExist = lstIdentInfosComps.FirstOrDefault(ic => ic.infocomp_id == infocomp);

                    if (infocompExist != null)
                    {
                        GestionTraceManager.WriteLogError(structureId, "Find REABOINTERDIREINFOCOMPUPDATEFANS in config.ini " + mySSC["REABOINTERDIREINFOCOMPUPDATEFANS"]);
                        logger.Debug("vérifier l'infocomp REABOINFOCOMPUPDATEFANS" + mySSC["REABOINTERDIREINFOCOMPUPDATEFANS"]);
                        return "danger|title_msg_error_droit_update_fans|msg_error_droit_update_fans";
                    }

                }


                logger.Debug("UpdateChild avant FanCard_UpdateRelation structureId" + structureId + " identiteId " + identiteId + " checkValue" + checkValue);
                bool newIdentite = wsThemis.FanCard_UpdateRelation(structureId, identiteId, checkValue);


                return newIdentite.ToString();
            }
            catch (Exception ex)
            {
                logger.Error("UpdateChild : " + ex.Message + " " + ex.StackTrace);
                throw ex;
            }

            //return ok;

        }


        [WebMethod(EnableSession = true)]
        public IdentiteEntity GetInformationsFanChild(int identiteIdChild)
        {
            int identiteId = 0;
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
            {
                identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
            }
            else
            {
                throw new Exception("Aucune identité en session");
            }

            int structureId = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            string lstFormule = GetListFormules(structureId);

            customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
            List<IdentiteEntity> listChilds = wsThemis.FanCard_LoadIdentiteChild(structureId, identiteId, lstFormule, "").ToList();



            IdentiteEntity test = wsThemis.GetCustomerProfileOfId(structureId.ToString(), identiteIdChild.ToString(), 0, "N");
            IdentiteEntity childToUpdated = listChilds.Where(id => id.Identite_id == identiteIdChild).FirstOrDefault();
            return test;
        }


        [WebMethod(EnableSession = true)]
        public IdentiteEntity DeleteChild(int identiteIdChild)
        {
            // check hash

            int identiteId = 0;
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
            {
                identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
            }
            else
            {
                throw new Exception("Aucune identité en session");
            }


            int structureId = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
            IdentiteEntity childDeleted = wsThemis.FanCard_DeleteRelation(structureId, identiteId, identiteIdChild, "");
            return childDeleted;

            //return ok;
        }


        [WebMethod(EnableSession = true)]
        public IdentiteEntity GetIdentiteAccompte()
        {
            int structureId = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            try
            {
                // check hash

                logger.Info("GetIdentiteAccompte");
                var logsLevel = customerArea.App_Code.Initialisations.GetKeyAppSettings("logsLevel");


                int identiteId = 0;
                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");
                }

                utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
                mySSC = mySSC.GetDictionaryFromCache(structureId);


                if (mySSC["ACOMPTESMODESPAIEMENT"] != null)
                {
                    List<int> sListeMPacompte = mySSC["ACOMPTESMODESPAIEMENT"].Split(',').Select(c => int.Parse(c.ToString())).ToList();

                    if (logsLevel != LogLevel.NORMAL.ToString())
                    {
                        GestionTraceManager.WriteLog(structureId, "ACOMPTESMODESPAIEMENT : " + mySSC["ACOMPTESMODESPAIEMENT"]);
                    }


                    IdentiteEntity identity = new IdentiteEntity()
                    {
                        Identite_id = identiteId
                    };

                    logger.DebugFormat("FillAcompte : structureid {0} - identity {1} " + structureId, identity);

                    customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                    IdentiteEntity accompte = wsThemis.FillAcompte(structureId, sListeMPacompte.ToArray(), identity);
                    if (logsLevel != LogLevel.NORMAL.ToString())
                    {
                        GestionTraceManager.WriteLog(structureId, "accompte : " + accompte);
                    }


                    return accompte;

                }
             
                if (logsLevel != LogLevel.NORMAL.ToString())
                {
                    GestionTraceManager.WriteLog(structureId, "ACOMPTESMODESPAIEMENT est null dans le config.ini");
                }

                return null;

            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(structureId, $"dans GetIdentiteAccompte : { ex.Message}");
                logger.Error("dans GetIdentiteAccompte : " + ex.Message);
                throw new Exception(ex.Message);
            }
        }


        /// <summary>
        /// prends la liste des formules dans le config.ini
        /// </summary>
        /// <returns></returns>
        private string GetListFormules(int idStructure)
        {
            string lstFormule = string.Empty;

            utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(idStructure);

            if (mySSC.Contains("REABOFORMULE"))
            {
                GestionTraceManager.WriteLog(idStructure, "Find REABOFormule in config.ini");
                lstFormule = mySSC["REABOFORMULE"].ToString();
            }
            else
            {
                GestionTraceManager.WriteLog(idStructure, "not found REABOFormule in config.ini");
            }

            return lstFormule;
        }


        /// <summary>
        /// prends la liste des formules dans le config.ini
        /// </summary>
        /// <returns></returns>
        private int GetManifReference(int idStructure)
        {
            int manifReference = 0;
            utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(idStructure);

            if (mySSC.Contains("MANIFREFERENCENEXTSEASON") && !string.IsNullOrEmpty(mySSC["MANIFREFERENCENEXTSEASON"].ToString()))
            {
                GestionTraceManager.WriteLog(idStructure, "Find MANIFREFERENCENEXTSEASON in config.ini");
                manifReference = int.Parse(mySSC["MANIFREFERENCENEXTSEASON"].ToString());
            }
            else
            {
                GestionTraceManager.WriteLog(idStructure, "not found MANIFREFERENCENEXTSEASON in config.ini");
            }

            return manifReference;

        }

        /// <summary>
        /// prends la liste des etats dans le config.ini
        /// </summary>
        /// <returns></returns>
        private string GetListEtats(int idStructure)
        {
            string lstEtats = string.Empty;
            utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(idStructure);

            if (mySSC.Contains("REABODOSSIERETATCHECKABO"))
            {
                GestionTraceManager.WriteLog(idStructure, "Find REABODossierEtatCheckAbo in config.ini");
                lstEtats = mySSC["REABODOSSIERETATCHECKABO"].ToString();
            }
            else
            {
                GestionTraceManager.WriteLog(idStructure, "not found REABODossierEtatCheckAbo in config.ini");
            }

            return lstEtats;
        }

        [WebMethod(EnableSession = true)]
        public List<LanguageEntity> GetLanguesList(int idStructure)
        {
            //  int idStructure = 0;

            try
            {
                logger.Debug("start GetLanguesList ");
                wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                List<LanguageEntity> lstLangues = wsThemis.LoadLanguages(idStructure).ToList();

                List<LanguageEntity> languagesFiltered = new List<LanguageEntity>();

                lstLangues.ForEach(lang =>
                {
                    if (lang.LangCode.IsCultureCode())
                        languagesFiltered.Add(lang);
                });

                return languagesFiltered;

            }
            catch (Exception ex)
            {
                logger.Error("GetLanguesList  " + ex.Message + " " + ex.StackTrace);
                GestionTraceManager.WriteLogError(idStructure, " common.asmx - GetLanguesList" + ex.Message + " " + ex.StackTrace);

                throw ex;
            }


        }


        /// <summary>
        /// Retourne la liste d'export avec les information de l'identité ainsi que celle des formules
        /// siege, place, rang ...
        /// </summary>
        /// <returns></returns>
        [WebMethod(EnableSession = true)]
        public List<IdentiteEntity> GetDatasForExport()
        {
            int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            try
            {
                int identiteId = 0;

                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");

                }

                string lstFormule = GetListFormules(idStructure);


                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                List<IdentiteEntity> listChilds = wsThemis.FanCard_LoadIdentiteChild(idStructure, identiteId, lstFormule, "").ToList();

                string cryptoKey = WebConfigurationManager.AppSettings["CryptoKey"].ToString();
                foreach (IdentiteEntity idi in listChilds)
                {
                    Sha1 sha1 = new Sha1(idStructure + "|" + idi.Identite_id + cryptoKey);
                    string hash = sha1.getSha1();
                    idi.hashKey = hash;
                }

                return listChilds;
            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(idStructure, "GetDatasForExport : " + ex.Message);
                throw ex;
            }
        }



        /// Demande du standart de liege
        /// <summary>
        ///  Les gérants aimeraient pouvoir faire un export excel des données des membres (places, coordonnées, n° abo,…)
        /// </summary>
        /// <returns></returns>
        [WebMethod(EnableSession = true)]
        public List<DossierIdentiteEntity> GetAllIdentiteCommandesFans(string listBastetState, bool isValidPlace)
        {
            int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            try
            {

                int identiteId = 0;
                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");

                }

                string lstFormule = GetListFormules(idStructure);
                string lstEtats = listBastetState;

                if (string.IsNullOrEmpty(listBastetState))
                {
                    lstEtats = GetListEtats(idStructure);  //"R,P,B";
                }

                int manifReference = GetManifReference(idStructure);

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                List<DossierIdentiteEntity> listChilds = wsThemis.FanCard_LoadAllIdentiteCommandesFans(idStructure, identiteId, lstFormule, lstEtats, isValidPlace, manifReference).ToList();

                return listChilds;
            }
            catch (Exception ex)
            {

                GestionTraceManager.WriteLogError(idStructure, "GetAllIdentiteCommandesFans : " + ex.Message);
                throw ex;
            }

        }

        [WebMethod(EnableSession = true)]
        public List<EventEntity> LoadEventsAndSessionsList()
        {
            int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            try
            {

                int identiteId = 0;

                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");

                }


                string userLang = App_Code.Initialisations.GetUserLanguage();

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                //  List<DossierIdentiteEntity> listChilds = wsThemis.even(idStructure, identiteId, lstFormule, lstEtats, isValidPlace).ToList();
                var lstEvts = wsThemis.LoadEventsListWaitList(idStructure.ToString("0000"), userLang);

                if(lstEvts != null)
                {
                    return lstEvts.ToList();
                }
                return new List<EventEntity>();
            }
            catch (Exception ex)
            {

                GestionTraceManager.WriteLogError(idStructure, "LoadEventsAndSessionsList : " + ex.Message);
                throw ex;
            }

        }

        [WebMethod(EnableSession = true)]
        public string InsertWaitList(ListeAttenteEntity waitListObject)
        {
            int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            try
            {

                int identiteId = 0;

                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");

                }


                string userLang = App_Code.Initialisations.GetUserLanguage();

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
                mySSC = mySSC.GetDictionaryFromCache(idStructure);

                int operateurId = int.Parse(mySSC["PAIEMENTWEBOPERATORID"].ToString());


                if (waitListObject.ManifestationID > 0 && waitListObject.Quantite > 0 && waitListObject.Quantite <= 20)
                {

                    ListeAttenteEntity la = new ListeAttenteEntity()
                    {
                        ManifestationID = waitListObject.ManifestationID,
                        SeanceID = waitListObject.SeanceID,
                        CategoryID = waitListObject.CategoryID,
                        Commentaire = waitListObject.Commentaire,
                        Quantite = waitListObject.Quantite,
                        DateCreation = DateTime.Now,
                        IdentiteID = identiteId,
                        OperateurID = operateurId,
                        OkStrap = waitListObject.OkStrap,
                        OkSepare = waitListObject.OkSepare,
                        reponseAvant = "''",
                        ReserveID = 0,
                        Supprime = "'N'",
                        TypeTarifID = 0,
                        DateModification = DateTime.Now
                    };

                    bool isWishListInserted = wsThemis.InsertWaitList(idStructure.ToString("0000"), la);

                    if (isWishListInserted)
                    {
                        return "success:title_msg_success_inserted_waitlist:msg_success_inserted_waitlist";
                    }
                }
                else
                {
                    return "error:title_msg_error_object_waitlist:msg_error_object_waitlist";

                }

                return "error:title_msg_error_inserted_waitlist:msg_error_inserted_waitlist";

            }
            catch (Exception ex)
            {

                GestionTraceManager.WriteLogError(idStructure, "InsertWaitList : " + ex.Message);
                throw ex;
            }

        }

        //[WebMethod(EnableSession = true)]
        //public List<ListeAttenteEntity> GetWaitListOfIdentite()
        //{
        //    int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

        //    try
        //    {

        //        int identiteId = 0;

        //        if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
        //            identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
        //        else
        //        {
        //            throw new Exception("Aucune identité en session");

        //        }


        //        string userLang = Initialisations.GetUserLanguage();

        //        customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

        //        MyDictionary mySSC = new MyDictionary();
        //        mySSC = mySSC.GetDictionaryFromCache(idStructure);

        //        int operateurId = int.Parse(mySSC["PAIEMENTWEBOPERATORID"].ToString());

        //        var lstListAttente = wsThemis.GetWaitListOfIdentite(idStructure.ToString("0000"), identiteId, userLang).ToList();

        //        return lstListAttente;

        //    }
        //    catch (Exception ex)
        //    {

        //        GestionTraceManager.WriteLog(idStructure, "InsertWaitList : " + ex.Message);
        //        throw ex;
        //    }

        //}

        [WebMethod(EnableSession = true)]
        public List<ListeAttenteEntity> GetWaitListOfIdentite()
        {
            int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            try
            {
                logger.Debug("GetWaitListOfIdentite");
                string plateformCode = "";
                if (System.Web.HttpContext.Current.Request.QueryString["plateformCode"] != "" && System.Web.HttpContext.Current.Request.QueryString["plateformCode"] != null)
                {
                    plateformCode = System.Web.HttpContext.Current.Request.QueryString["plateformCode"].ToString();
                    System.Web.HttpContext.Current.Session["plateformCode"] = System.Web.HttpContext.Current.Request.QueryString["plateformCode"].ToString();
                }

                int identiteId = 0;

                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");

                }


                string userLang = App_Code.Initialisations.GetUserLanguage();

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                /*
                MyDictionary mySSC = new MyDictionary();
                mySSC = mySSC.GetDictionaryFromCache(idStructure);

                int operateurId = int.Parse(mySSC["PAIEMENTWEBOPERATORID"].ToString());
                */

                List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
                {
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                    new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
                };

                dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, idStructure, 0, 0, plateformCode, userLang);

                int nbDaysToHistoEvent = int.Parse(globalPlateform.waitlist.nbDaysToHistoEvents.Value.ToString());
                if (globalPlateform.customer != null && globalPlateform.customer.waitlist != null && globalPlateform.customer.waitlist.nbDaysToHistoEvents != null)
                {
                    nbDaysToHistoEvent = globalPlateform.customer.waitlist.nbDaysToHistoEvents.Value;
                }
                var logsLevel = customerArea.App_Code.Initialisations.GetKeyAppSettings("logsLevel");

                if (logsLevel != LogLevel.NORMAL.ToString())
                {
                    GestionTraceManager.WriteLog(idStructure, "GetWaitListOfIdentite : nbDaysToHistoEvent " + nbDaysToHistoEvent);

                }

                logger.Debug("GetWaitListOfIdentite nbDaysToHistoEvent :" + nbDaysToHistoEvent);
                List<ListeAttenteEntity> lstListAttente = wsThemis.GetWaitListOfIdentite(idStructure.ToString("0000"), identiteId, userLang).ToList();

                return lstListAttente;

            }
            catch (Exception ex)
            {

                GestionTraceManager.WriteLogError(idStructure, "GetWaitListOfIdentite : " + ex.Message);
                throw ex;
            }

        }



        [WebMethod(EnableSession = true)]
        public bool IsGestionnaireReabo()
        {

            if (System.Web.HttpContext.Current.Session["idstructure"] == null)
            {
                return false;
            }

            int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            try
            {
                int identiteId = 0;

                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    logger.Debug("IsGestionnaireReabo : Aucune identité en session");
                    //throw new Exception("Aucune identité en session");
                    return false;
                }

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                return wsThemis.IsGestionnaireReaboFans(idStructure, identiteId);

            }
            catch (Exception ex)
            {

                GestionTraceManager.WriteLogError(idStructure, "IsGestionnaireReabo : " + ex.Message);
                throw ex;
            }

        }


        [WebMethod(EnableSession = true)]
        public string DeleteListeAttente(int listAttenteId)
        {

            if (System.Web.HttpContext.Current.Session["idstructure"] == null)
            {
                return "error:title_msg_error_aucune_structure:aucune_structure";
            }

            int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            try
            {

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                bool isDeleted = wsThemis.DeleteIdentiteOfWaitList(idStructure.ToString("0000"), listAttenteId);

                if (isDeleted)
                {
                    return "success:title_msg_success_deleted_waitlist:msg_success_deleted_waitlist";
                }
                else
                {
                    return "error:title_msg_error_deleted_waitlist:msg_error_deleted_waitlist";
                }
            }
            catch (Exception ex)
            {

                GestionTraceManager.WriteLogError(idStructure, "DeleteListeAttente : " + ex.Message);
                throw ex;
            }

        }

        #region InfoComp push 


        [WebMethod(EnableSession = true)]
        public bool PushInfoComps(string chaine)
        {

            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());
                int identiteId = 0;

                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    logger.Debug("PushInfoCompsOfIdentite : Aucune identité en session");
                    //throw new Exception("Aucune identité en session");
                    return false;
                }

                try
                {
                    logger.Debug($"PushInfoComps {chaine}");

                    customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                    string typeConn = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];
                    SqlServerConnexion cnx = utilitaires2010.DBFunctions.ConnectOpen(idStructure, typeConn);

                    logger.Debug($"call InfoCompManager.PushInfoCompsOfIdentite {typeConn}");
                    return InfoCompManager.PushInfoCompsOfIdentite(cnx, idStructure.ToString("0000"), identiteId, chaine, "REABO");

                }
                catch (Exception ex)
                {
                    logger.Error($"call PushInfoCompsOfIdentite {ex.Message}");
                    GestionTraceManager.WriteLogError(idStructure, "PushInfoCompsOfIdentite : " + ex.Message);
                    throw ex;
                }

            }
            logger.Debug("PushInfoCompsOfIdentite : Aucune structure en session");
            return false;

        }
        #endregion

        #region Gestion Consommateurs getlist, add, delete

        [WebMethod(EnableSession = true)]
        public List<IdentiteEntity> GetListLinkConsumer(int structureid, string langCode)
        {
            try
            {

                utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
                mySSC = mySSC.GetDictionaryFromCache(structureid);

                int identiteIdConnected = 0;
                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteIdConnected = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");

                }

                int ipostalTelEmail = int.Parse(mySSC["VARIABLESEMAIL"].ToString());

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                List<IdentiteEntity> lstConsommateursLinked = wcfThemis.getConsommateurs(structureid, identiteIdConnected, langCode, ipostalTelEmail).ToList();

                return lstConsommateursLinked;

            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(structureid, "GetListLinkConsumer : " + ex.Message);

                throw;
            }

        }


        /// <summary>
        /// Créer le compte si il n'existe pas et fait la liaison
        /// </summary>
        /// <param name="structureid"></param>
        /// <param name="eventid"></param>
        /// <param name="customer"></param>
        /// <param name="isexisting">permet de savoir si le compte existe déjà alors on fait que de relier</param>
        /// <returns></returns>
        [WebMethod(EnableSession = true)]
        public string LinkConsumer(int structureid, int eventid, IdentiteEntity customer, bool isexisting)
        {
            utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(structureid);

            //par defaut on autorise pas l'unicité de l'email
            bool bUnicity = true;
            // MyDictionary mySSC = new MyDictionary();
            // mySSC = mySSC.GetDictionaryFromCache(int.Parse(idstructure));


            if (mySSC["CREATEPROFILLINKMAILUNICITY"] == null)
            {
                logger.Debug("CreateLoginStatic CREATEPROFILLINKMAILUNICITY est null");
                GestionTraceManager.WriteLog(structureid, "CreateLoginStatic CREATEPROFILLINKMAILUNICITY est null");

            }
            else if (mySSC["CREATEPROFILLINKMAILUNICITY"] == "-1")
            {
                logger.Debug("CreateLoginStatic CREATEPROFILLINKMAILUNICITY est  -1");
                GestionTraceManager.WriteLog(structureid, "CreateLoginStatic CREATEPROFILLINKMAILUNICITY est -1");

                bUnicity = false;
            }
            if (string.IsNullOrEmpty(customer.Email))
            {
                bUnicity = false;
            }

            int ipostalTelEmail = int.Parse(mySSC["VARIABLESEMAIL"]);
            string postalTelEmail = "postal_tel" + mySSC["VARIABLESEMAIL"].ToString();


            int operateurId = int.Parse(mySSC["PAIEMENTWEBOPERATORID"].ToString());

            int identiteIdConnected = 0;
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
            {
                identiteIdConnected = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
            }
            else
            {
                GestionTraceManager.WriteLogError(structureid, "LinkConsumer : Aucune identité en session");

                throw new Exception("Aucune identité en session");
            }


            if (identiteIdConnected == customer.Identite_id)
            {

                return "error:title_msg_error_link_consumer:msg_error_link_consumer_same_identite_connected";
            }

            try
            {

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                IdentiteEntity identiteisexist = null;
                if (!string.IsNullOrEmpty(customer.Email)) // && !isexisting)
                {
                    if (customer.Identite_id > 0)
                    {
                        identiteisexist = wcfThemis.GetCustomerProfileOfEmailOrIdentiteId(
                            structureid.ToString("0000"),
                            "",
                            MyConfigurationManager.CalculateHashedPassword(customer.Password),
                            customer.Identite_id.ToString(),
                            ipostalTelEmail,
                            "N"
                        );
                    }
                    else
                    {
                        identiteisexist = wcfThemis.GetCustomerProfileOfEmailOrIdentiteId(
                            structureid.ToString("0000"),
                            customer.Email,
                            MyConfigurationManager.CalculateHashedPassword(customer.Password),
                            "",
                            ipostalTelEmail,
                            "N"
                        );
                    }
                }


                //si l'identite n'existe pas on créer et relie ou si on autorise les doublons dans le config.ini
                if (identiteisexist == null || !bUnicity)
                {
                    utilitaires2010.Initialisations.SetValuesOfIdentity(structureid, ref customer, "Portable", customer.MobilePhoneNumber);
                    utilitaires2010.Initialisations.SetValuesOfIdentity(structureid, ref customer, "Telephone", customer.PhoneNumber);
                    utilitaires2010.Initialisations.SetValuesOfIdentity(structureid, ref customer, "Fax", customer.FaxPhoneNumber);
                    utilitaires2010.Initialisations.SetValuesOfIdentity(structureid, ref customer, "Email", customer.Email);


                    //GestionTraceManager.WriteLog(structureid, "add LinkConsumer : SetValuesOfIdentity customer.MobilePhoneNumber " + customer.MobilePhoneNumber);
                    //logger.Debug("add LinkConsumer : SetValuesOfIdentity customer.MobilePhoneNumber " + customer.MobilePhoneNumber);

                    int consumerIdRod = 0;
                    if (isexisting && identiteisexist != null)
                    {
                        //vérifie si ce qui a été entrer est identique à la base de données
                        if (identiteisexist.Identite_id == customer.Identite_id && identiteisexist.Email.ToLower() == customer.Email.ToLower())
                        {
                            consumerIdRod = customer.Identite_id;
                        }
                        else
                        {
                            return "error:title_msg_error_link_consumer:msg_error_link_consumer_not_same_info";
                        }
                    }
                    else
                    {
                        customer.FicheSupprimer = "N";

                        if (ipostalTelEmail == 1)
                        {
                            customer.PhoneNumber1 = customer.Email;
                        }

                        if (ipostalTelEmail == 2)
                        {
                            customer.PhoneNumber2 = customer.Email;
                        }

                        if (ipostalTelEmail == 3)
                        {
                            customer.PhoneNumber3 = customer.Email;
                        }

                        if (ipostalTelEmail == 4)
                        {
                            customer.PhoneNumber4 = customer.Email;
                        }

                        if (ipostalTelEmail == 5)
                        {
                            customer.PhoneNumber5 = customer.Email;
                        }

                        if (ipostalTelEmail == 6)
                        {
                            customer.PhoneNumber6 = customer.Email;
                        }

                        if (ipostalTelEmail == 7)
                        {
                            customer.PhoneNumber7 = customer.Email;
                        }

                        string filiere_id = "0";
                        if (mySSC.Contains("CREATEPROFILWEBFILIEREID"))
                        {
                            filiere_id = mySSC["CREATEPROFILWEBFILIEREID"];
                        }

                        customer.filiereId = int.Parse(filiere_id);

                        consumerIdRod = wcfThemis.CreateCustomerProfileAndReturnID(structureid.ToString("0000"), customer, null, bUnicity, postalTelEmail, operateurId);
                    }


                    int newLinkConsumer = 0;

                    if (consumerIdRod > 0 && identiteIdConnected > 0)
                    {
                        //  wcfThemis.GetCustomerProfileOfEmailOrIdentiteId
                        newLinkConsumer = wcfThemis.AddLinkConsumers(structureid, identiteIdConnected, consumerIdRod);
                    }
                    else
                    {
                        //throw new Exception("consumerIdRod ou identiteIdConnected est null ");
                        return "error:title_msg_error_link_consumer:msg_error_link_consumer_null";
                    }

                    if (newLinkConsumer > 0)
                    {
                        return "success:title_msg_success_link_consumer:msg_success_link_consumer";
                    }
                    else
                    {
                        return "error:title_msg_error_link_consumer:msg_error_link_consumer";
                    }
                }
                else
                {
                    return "error:title_msg_error_link_consumer:msg_error_link_consumer_exist";
                }


            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(structureid, "add LinkConsumer : " + ex.Message + " structureid : " + structureid);
                logger.Error("add LinkConsumer : " + ex.Message + " structureid : " + structureid, ex);

                throw ex;
            }

        }

        /// <summary>
        /// Verifie le compte et fait la liaison
        /// </summary>
        /// <param name="structureid"></param>
        /// <param name="eventid"></param>
        /// <param name="customer"></param>
        /// <param name="isexisting">permet de savoir si le compte existe déjà alors on fait que de relier</param>
        /// <returns></returns>
        [WebMethod(EnableSession = true)]
        public string LinkExistingConsumer(int structureid, int eventid, IdentiteEntity customer, bool isexisting)
        {
            utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(structureid);
            // MyDictionary mySSC = new MyDictionary();
            // mySSC = mySSC.GetDictionaryFromCache(int.Parse(idstructure));


            if (mySSC["CREATEPROFILLINKMAILUNICITY"] == null)
            {


            }
            else if (mySSC["CREATEPROFILLINKMAILUNICITY"] == "-1")
            {
            }
            if (string.IsNullOrEmpty(customer.Email))
            {
            }

            int ipostalTelEmail = int.Parse(mySSC["VARIABLESEMAIL"]);
            string postalTelEmail = "postal_tel" + mySSC["VARIABLESEMAIL"].ToString();


            int operateurId = int.Parse(mySSC["PAIEMENTWEBOPERATORID"].ToString());

            int identiteIdConnected = 0;
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
            {
                identiteIdConnected = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
            }
            else
            {
                throw new Exception("Aucune identité en session");
            }


            if (identiteIdConnected == customer.Identite_id)
            {
                return "error:title_msg_error_link_consumer:msg_error_link_consumer_same_identite_connected";
            }

            try
            {

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                IdentiteEntity identiteisexist = null;
                if (!string.IsNullOrEmpty(customer.Email)) // && !isexisting)
                {
                    if (customer.Identite_id > 0)
                    {
                        identiteisexist = wcfThemis.GetCustomerProfileOfEmailOrIdentiteId(
                            structureid.ToString("0000"),
                            "",
                            "",
                            customer.Identite_id.ToString(),
                            ipostalTelEmail,
                            "N"
                        );
                    }
                    else
                    {
                        identiteisexist = wcfThemis.GetCustomerProfileOfEmailOrIdentiteId(
                            structureid.ToString("0000"),
                            customer.Email,
                            "",
                            "",
                            ipostalTelEmail,
                            "N"
                        );
                    }

                }

                //si l'identite n'existe pas on créer et relie ou si on autorise les doublons dans le config.ini
                if (identiteisexist != null)
                {

                    logger.Debug("add LinkExistingConsumer : SetValuesOfIdentity customer.MobilePhoneNumber " + customer.MobilePhoneNumber);

                    int consumerIdRod = 0;
                    if (identiteisexist != null)
                    {
                        //vérifie si ce qui a été entrer est identique à la base de données
                        if (identiteisexist.Identite_id == customer.Identite_id && identiteisexist.Email.ToLower() == customer.Email.ToLower())
                        {
                            consumerIdRod = customer.Identite_id;
                        }
                        else
                        {
                            return "error:title_msg_error_link_consumer:msg_error_link_consumer_not_same_info";
                        }
                    }
                    else
                    {
                        // ne retrouve pas l'id 
                        return "error:title_msg_error_link_consumer:msg_error_link_consumer_not_same_info";
                    }



                    int newLinkConsumer = 0;

                    if (consumerIdRod > 0 && identiteIdConnected > 0)
                    {
                        //  wcfThemis.GetCustomerProfileOfEmailOrIdentiteId
                        newLinkConsumer = wcfThemis.AddLinkConsumers(structureid, identiteIdConnected, consumerIdRod);
                    }
                    else
                    {
                        //throw new Exception("consumerIdRod ou identiteIdConnected est null ");
                        return "error:title_msg_error_link_consumer:msg_error_link_consumer_null";
                    }

                    if (newLinkConsumer > 0)
                    {
                        return "success:title_msg_success_link_consumer:msg_success_link_consumer";
                    }
                    else
                    {
                        return "error:title_msg_error_link_consumer:msg_error_link_consumer";
                    }
                }
                else
                {
                    return "error:title_msg_error_link_consumer:msg_error_link_consumer_exist";
                }


            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(structureid, "add LinkConsumer : " + ex.Message + " structureid : " + structureid);
                logger.Error("add LinkConsumer : " + ex.Message + " structureid : " + structureid, ex);

                throw ex;
            }

        }





        [WebMethod(EnableSession = true)]
        public string UnlinkConsumer(int consumerId)
        {

            if (System.Web.HttpContext.Current.Session["idstructure"] == null)
            {
                return "error:title_msg_error_aucune_structure:aucune_structure";
            }

            int idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            try
            {
                int identiteIdConnected = 0;
                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
                {
                    identiteIdConnected = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                }
                else
                {
                    throw new Exception("Aucune identité en session");

                }

                customerArea.wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                bool isDeleted = wcfThemis.UnLinkConsumers(idStructure, identiteIdConnected, consumerId);

                if (isDeleted)
                {
                    return "success:title_msg_success_unlink_consumer:msg_success_unlink_consumer";
                }
                else
                {
                    return "error:title_msg_error_unlink_consumer:msg_error_unlink_consumer";
                }
            }
            catch (Exception ex)
            {
                
                GestionTraceManager.WriteLogError(idStructure, "DeleteListeAttente : " + ex.Message);
                throw ex;
            }

        }

        #endregion

        #region Traductions XML



        [WebMethod(EnableSession = true)]
        public string Translate(int structureId, string langCode, int eventId, int profilAcheteurId, bool isDefault)
        {
            try
            {

                logger.Debug("===== Translate start ===== ");
                if (System.Web.HttpContext.Current.Cache["xmlTranslate" + structureId + langCode + isDefault] != null)
                {
                    logger.Debug("cache xmlTranslate");

                    string xml = System.Web.HttpContext.Current.Cache["xmlTranslate" + structureId + langCode + isDefault].ToString();
                    return xml.ToString();
                }
                else
                {
                    logger.Debug("Load xml translates");
                    string XmlTranslateFile = Urls.LoadXmlTranslateFiles(structureId, eventId, profilAcheteurId, "." + langCode, "translate", isDefault);

                    if (!string.IsNullOrEmpty(XmlTranslateFile))
                    {
                        XDocument xml = XDocument.Load(XmlTranslateFile);
                        return xml.ToString();
                    }
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(structureId, "Translate : " + ex.Message);
                throw ex;
            }
        }

        #endregion


    }
}
