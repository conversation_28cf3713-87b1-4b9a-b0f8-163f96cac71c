﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="btnSave" xml:space="preserve">
    <value>registrarse  (GeneralTerm es)</value>
  </data>
  <data name="AucuneStructureSelectionnee" xml:space="preserve">
    <value>cualquier estructura seleccionada (GeneralTerm es)</value>
  </data>
  <data name="AfficherLEtage" xml:space="preserve">
    <value>Afficher l'étage (GeneralTerm es)</value>
  </data>
  <data name="AfficherLEtageModeNormal" xml:space="preserve">
    <value>Afficher l'étage s'il existe (GeneralTerm es)</value>
  </data>
  <data name="AfficherLEtageModezaperLEtage" xml:space="preserve">
    <value>Zapper l'étage (GeneralTerm es)</value>
  </data>
  <data name="AfficherLEtageModezapperLEtage" xml:space="preserve">
    <value>Zapper l'étage (GeneralTerm es)</value>
  </data>
  <data name="AttribuerUneImageSurUneDesSéancesDeCetteManifestation" xml:space="preserve">
    <value>Attribuer une image sur une des séances de cette manifestation (GeneralTerm es)</value>
  </data>
  <data name="CodeLangue" xml:space="preserve">
    <value>Code langue (GeneralTerm es)</value>
  </data>
  <data name="Commentaire" xml:space="preserve">
    <value>Commentaire  (GeneralTerm es)</value>
  </data>
  <data name="CommentaireBasDePage" xml:space="preserve">
    <value>Commentaire bas de page (GeneralTerm es)</value>
  </data>
  <data name="CommentaireHautDePage" xml:space="preserve">
    <value>Commentaire haut de page (GeneralTerm es)</value>
  </data>
  <data name="Copier" xml:space="preserve">
    <value>Copiar (GeneralTerm es)</value>
  </data>
  <data name="CopierLeCommentaire" xml:space="preserve">
    <value>Copier le commentaire (GeneralTerm es)</value>
  </data>
  <data name="DateDeLaSéance" xml:space="preserve">
    <value>Date de la séance (GeneralTerm es)</value>
  </data>
  <data name="delete" xml:space="preserve">
    <value>supprimer  (GeneralTerm es)</value>
  </data>
  <data name="Editer" xml:space="preserve">
    <value>Editer (GeneralTerm es)</value>
  </data>
  <data name="EffacerCommentaire" xml:space="preserve">
    <value>Effacer le commentaire (GeneralTerm es)</value>
  </data>
  <data name="ErrorPlusieursRegles" xml:space="preserve">
    <value>Advertencia !! Hay varias reglas generales (GeneralTerm es)</value>
  </data>
  <data name="fichiervide" xml:space="preserve">
    <value>Le fichier est vide (GeneralTerm es)</value>
  </data>
  <data name="FileUploadedSuccessfully" xml:space="preserve">
    <value>File uploaded successfully (GeneralTerm es)</value>
  </data>
  <data name="go" xml:space="preserve">
    <value>ok  (GeneralTerm es)</value>
  </data>
  <data name="HtmlAttendu" xml:space="preserve">
    <value>.html attendu (GeneralTerm es)</value>
  </data>
  <data name="IdDeLaSéance" xml:space="preserve">
    <value>Id de la séance (GeneralTerm es)</value>
  </data>
  <data name="InvalidFileContent" xml:space="preserve">
    <value>contenu fichier invalide  (GeneralTerm es)</value>
  </data>
  <data name="InvalidFilenameSupplied" xml:space="preserve">
    <value>nom fichier invalide  (GeneralTerm es)</value>
  </data>
  <data name="LangueParDefaut" xml:space="preserve">
    <value>Langue par defaut (GeneralTerm es)</value>
  </data>
  <data name="LeFichierExiste" xml:space="preserve">
    <value>Le fichier existe (GeneralTerm es)</value>
  </data>
  <data name="Manifestations" xml:space="preserve">
    <value>Manifestations (GeneralTerm es)</value>
  </data>
  <data name="MiseAJourProperties" xml:space="preserve">
    <value>Mise à jour  (GeneralTerm es)</value>
  </data>
  <data name="ModeCalendrier" xml:space="preserve">
    <value>Mode Calendrier (GeneralTerm es)</value>
  </data>
  <data name="ModeNormal" xml:space="preserve">
    <value>Mode Normal (GeneralTerm es)</value>
  </data>
  <data name="NomDeLaManifestation" xml:space="preserve">
    <value>Nom de la manifestation (GeneralTerm es)</value>
  </data>
  <data name="PathNotFound" xml:space="preserve">
    <value>Chemin d'accès introuvable  (GeneralTerm es)</value>
  </data>
  <data name="PermissionToUploadFileDenied" xml:space="preserve">
    <value>Permission to upload file denied (GeneralTerm es)</value>
  </data>
  <data name="ReserveAucune" xml:space="preserve">
    <value>Aucune réserve (GeneralTerm es)</value>
  </data>
  <data name="ResultCashStatement" xml:space="preserve">
    <value>Aucune commande selon ces critères (GeneralTerm es)</value>
  </data>
  <data name="SeancesDeLaManif" xml:space="preserve">
    <value>Seances de la manif (GeneralTerm es)</value>
  </data>
  <data name="TypeDeChoixSéance" xml:space="preserve">
    <value>Type de choix séance  (GeneralTerm es)</value>
  </data>
  <data name="TypeLocalResource" xml:space="preserve">
    <value>Type  (GeneralTerm es)</value>
  </data>
  <data name="UnableToUploadFileExceedsMaximumLimit" xml:space="preserve">
    <value>Unable to upload file exceeds maximum limit (GeneralTerm es)</value>
  </data>
  <data name="UpLoad" xml:space="preserve">
    <value>UpLoad (GeneralTerm es)</value>
  </data>
  <data name="UploaderUnFichierHtmlDepuisVotreOrdinateur" xml:space="preserve">
    <value>Uploader un fichier html depuis votre ordinateur (GeneralTerm es)</value>
  </data>
  <data name="UploadLocalResource" xml:space="preserve">
    <value>Upload (GeneralTerm es)</value>
  </data>
  <data name="Vider" xml:space="preserve">
    <value>vider (GeneralTerm es)</value>
  </data>
  <data name="Visualiser" xml:space="preserve">
    <value>Visualiser (GeneralTerm es)</value>
  </data>
  <data name="VisualiserEtModifierLeCommentaire" xml:space="preserve">
    <value>Visualiser et modifier le commentaire (GeneralTerm es)</value>
  </data>
  <data name="VisualiserLocalResource" xml:space="preserve">
    <value>Visualiser (GeneralTerm es)</value>
  </data>
  <data name="VosManifestations" xml:space="preserve">
    <value>Vos manifestations (GeneralTerm es)</value>
  </data>
  <data name="VosSeancesSur" xml:space="preserve">
    <value>Vos seances sur (GeneralTerm es)</value>
  </data>
  <data name="Valider" xml:space="preserve">
    <value>validar (GeneralTerm es)</value>
  </data>
  <data name="DeleteRuleSuccess" xml:space="preserve">
    <value>La regla ha sido borrado (GeneralTerm es)</value>
  </data>
  <data name="DeleteRulesSuccess" xml:space="preserve">
    <value>Se han eliminado las reglas de venta (GeneralTerm es)</value>
  </data>
  <data name="ErrorDeleteRule" xml:space="preserve">
    <value>Se ha producido un error al suprimir (GeneralTerm es)</value>
  </data>
  <data name="ErrorDeleteRules" xml:space="preserve">
    <value>Las condiciones de negocio se han eliminado (GeneralTerm es)</value>
  </data>
  <data name="lblActions" xml:space="preserve">
    <value>acciones (GeneralTerm es)</value>
    <comment>label des datatable Action</comment>
  </data>
  <data name="lblOperationDate" xml:space="preserve">
    <value>fecha de la transacción (GeneralTerm es)</value>
  </data>
  <data name="lblTooltipUpdateOffer" xml:space="preserve">
    <value>Actualizar ofertas (listOffres.aspx.es.resx)</value>
  </data>
  <data name="lblTooltipUpdateProfilAcheteur" xml:space="preserve">
    <value>Actualizar el perfil de comprador (listOffres.aspx.es.resx)</value>
  </data>
  <data name="DeleteRuleError" xml:space="preserve">
    <value>Se ha producido un error al suprimir  (GeneralTerm es)</value>
  </data>
  <data name="SuccessUpdateRules" xml:space="preserve">
    <value>Las reglas de las ventas han sido actualizados (GeneralTerm es)</value>
  </data>
  <data name="deleteContrainte" xml:space="preserve">
    <value>Forzada sido borrado (GeneralTerm es)</value>
  </data>
  <data name="errorInsertContrainte" xml:space="preserve">
    <value>Se produjo un error durante la inserción del estrés (GeneralTerm es)</value>
  </data>
  <data name="successInsertContrainte" xml:space="preserve">
    <value>Forzada ha agregado (GeneralTerm es)</value>
  </data>
  <data name="btnSaveProfilAcheteur" xml:space="preserve">
    <value>registrarse (GeneralTerm es)</value>
  </data>
  <data name="ErrorDeleteOffer" xml:space="preserve">
    <value>Se ha producido un error al suprimir  (GeneralTerm es)</value>
  </data>
  <data name="ErrorUpdateProfilAcheteurForOffer" xml:space="preserve">
    <value>Se produjo un error al actualizar el perfil de comprador para esta oferta (GeneralTerm es)</value>
  </data>
  <data name="lblContrainteName" xml:space="preserve">
    <value>Nom de la contrainte (GeneralTerm es)</value>
  </data>
  <data name="lblEndDate" xml:space="preserve">
    <value>Fecha de finalización (GeneralTerm es)</value>
  </data>
  <data name="lblEndDateOfValidity" xml:space="preserve">
    <value>Fecha final de validez (GeneralTerm es)</value>
  </data>
  <data name="lblEndDateOfValidityInt" xml:space="preserve">
    <value>Validez End Fecha int  (GeneralTerm es)</value>
  </data>
  <data name="lblID" xml:space="preserve">
    <value>ID   (GeneralTerm es)</value>
  </data>
  <data name="lblOfferName" xml:space="preserve">
    <value>Nombre del suministro  (GeneralTerm es)</value>
  </data>
  <data name="lblProfilAcheteurID" xml:space="preserve">
    <value>ID (GeneralTerm es)</value>
  </data>
  <data name="lblProfilAcheteurName" xml:space="preserve">
    <value>Nombre de PA (GeneralTerm es)</value>
  </data>
  <data name="lblSQL" xml:space="preserve">
    <value>SQL (GeneralTerm es)</value>
  </data>
  <data name="lblStartDate" xml:space="preserve">
    <value>Fecha de inicio (GeneralTerm es)</value>
  </data>
  <data name="lblStartDateOfValidity" xml:space="preserve">
    <value>Fecha de inicio de validez  (GeneralTerm es)</value>
  </data>
  <data name="lblStartDateOfValidityInt" xml:space="preserve">
    <value>Validez fecha de inicio int  (GeneralTerm es)</value>
  </data>
  <data name="SuccessDeleteOffer" xml:space="preserve">
    <value>Se eliminará la oferta (GeneralTerm es)</value>
  </data>
  <data name="SuccessInsertOffer" xml:space="preserve">
    <value>Ha añadido la oferta  (GeneralTerm es)</value>
  </data>
  <data name="successUpdateOffer" xml:space="preserve">
    <value>La oferta ha sido actualizado correctamente (GeneralTerm es)</value>
  </data>
  <data name="SuccessUpdateProfilAcheteur" xml:space="preserve">
    <value>La actualización se realizó con éxito para el perfil de comprador (GeneralTerm es)</value>
  </data>
  <data name="SuccessUpdateProfilAcheteurForOffer" xml:space="preserve">
    <value>La actualización de los perfiles de comprador se completó con éxito esta oferta (GeneralTerm es)</value>
  </data>
  <data name="AddRulesSelected" xml:space="preserve">
    <value>Ajouter les règles sur toutes les séances sélectionnées (GeneralTerm es)</value>
  </data>
  <data name="Annuler" xml:space="preserve">
    <value>Annuler (GeneralTerm es)</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Catégorie - Categoría (GeneralTerm es)</value>
  </data>
  <data name="CompteClient" xml:space="preserve">
    <value>Compte client (pour facture) (GeneralTerm es)</value>
  </data>
  <data name="ConsommateurObligatoire" xml:space="preserve">
    <value>Consommateur obligatoire (GeneralTerm es)</value>
  </data>
  <data name="ErrorDeleteProfilAcheteur" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression (GeneralTerm es)</value>
  </data>
  <data name="ErrorInsertProfilAcheteur" xml:space="preserve">
    <value>Une erreur s'est produite pendant l'insertion du profil acheteur (GeneralTerm es)</value>
  </data>
  <data name="ErrorUpdateContrainteForOffer" xml:space="preserve">
    <value>Error al actualizar la oferta para una restricción (GeneralTerm es)</value>
  </data>
  <data name="ErrorUpdateProfilAcheteur" xml:space="preserve">
    <value>Une erreur s'est produite pendant la mise à jour du profil acheteur (GeneralTerm es)</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Prénom (GeneralTerm es)</value>
  </data>
  <data name="Groupe" xml:space="preserve">
    <value>Groupe (GeneralTerm es)</value>
  </data>
  <data name="Libelle" xml:space="preserve">
    <value>Libellé (GeneralTerm es)</value>
  </data>
  <data name="OffreAucune" xml:space="preserve">
    <value>Aucune (GeneralTerm es)</value>
  </data>
  <data name="OperateurName" xml:space="preserve">
    <value>Operateur (GeneralTerm es)</value>
  </data>
  <data name="PaiementMode" xml:space="preserve">
    <value>Mode de reglement (GeneralTerm es)</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Mot de passe (GeneralTerm es)</value>
  </data>
  <data name="Place" xml:space="preserve">
    <value>Lieu (GeneralTerm es)</value>
  </data>
  <data name="Reserve" xml:space="preserve">
    <value>Réserves (GeneralTerm es)</value>
  </data>
  <data name="Resume" xml:space="preserve">
    <value>Résumé  (GeneralTerm es)</value>
  </data>
  <data name="SessionDateTime" xml:space="preserve">
    <value>Séances date/heure (GeneralTerm es)</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status (GeneralTerm es)</value>
  </data>
  <data name="successDeleteProfilAcheteur" xml:space="preserve">
    <value>Le profil d'acheteur à bien été supprimé (GeneralTerm es)</value>
  </data>
  <data name="SuccessInsertProfilAcheteur" xml:space="preserve">
    <value>Le profil acheteur à bien été ajouté (GeneralTerm es)</value>
  </data>
  <data name="SuccessRuleInsert" xml:space="preserve">
    <value>La règle de vente a bien été ajoutée pour la séance  (GeneralTerm es)</value>
  </data>
  <data name="SuccessRulesInsert" xml:space="preserve">
    <value>Les règles de ventes ont bien été ajoutées pour la liste de séances sélectionnées    (GeneralTerm es)</value>
  </data>
  <data name="SuccessUpdateContrainteForOffer" xml:space="preserve">
    <value>La coacción a una oferta sido actualizado  (GeneralTerm es)</value>
  </data>
  <data name="TitleDate" xml:space="preserve">
    <value>Date (GeneralTerm es)</value>
  </data>
  <data name="TitleModeObtention" xml:space="preserve">
    <value>Mode d'obtention (general terms es)</value>
  </data>
  <data name="TitleReserves" xml:space="preserve">
    <value>Réserves (GeneralTerm es)</value>
  </data>
  <data name="TitleSeances" xml:space="preserve">
    <value>Choix des séances  (GeneralTerm es)</value>
  </data>
  <data name="TitleTarifs" xml:space="preserve">
    <value>Choix des tarifs (GeneralTerm es)</value>
  </data>
  <data name="TypeEnvoi" xml:space="preserve">
    <value>Type d'envoi (GeneralTerm es)</value>
  </data>
  <data name="TypeTarif" xml:space="preserve">
    <value>Type de Tarif (GeneralTerm es)</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Por favor, espere mientras que el tratamiento está siendo -  Veuillez patienter, le traitement est en cours (Principale.master.resx)</value>
  </data>
  <data name="NomReserve" xml:space="preserve">
    <value>Nom de la réserve (GeneralTerm es)</value>
  </data>
  <data name="Activer" xml:space="preserve">
    <value>Activer (GeneralTerm es)</value>
  </data>
  <data name="Desactiver" xml:space="preserve">
    <value>Désactiver (GeneralTerm es)</value>
  </data>
  <data name="LegendStart" xml:space="preserve">
    <value>Début (GeneralTerm es)</value>
  </data>
  <data name="Automatique" xml:space="preserve">
    <value>Automatique (GeneralTerm es)</value>
  </data>
  <data name="Heures" xml:space="preserve">
    <value>Heures  (GeneralTerm es)</value>
  </data>
  <data name="Jours" xml:space="preserve">
    <value>Jours  (GeneralTerm es)</value>
  </data>
  <data name="LegendEnd" xml:space="preserve">
    <value>Fin (GeneralTerm es)</value>
  </data>
  <data name="LegendNbPlace" xml:space="preserve">
    <value>Nombre de places  (GeneralTerm es)</value>
  </data>
  <data name="LegendOptions" xml:space="preserve">
    <value>Options (GeneralTerm es)</value>
  </data>
  <data name="MemePlace" xml:space="preserve">
    <value>Même place (GeneralTerm es)</value>
  </data>
  <data name="ModePrisesPlace" xml:space="preserve">
    <value>Mode de prise de places (GeneralTerm es)</value>
  </data>
  <data name="PlacementLibre" xml:space="preserve">
    <value>Placement libre (GeneralTerm es)</value>
  </data>
  <data name="Precedent" xml:space="preserve">
    <value>Précédent (GeneralTerm es)</value>
  </data>
  <data name="Suivant" xml:space="preserve">
    <value>Suivant (GeneralTerm es)</value>
  </data>
  <data name="SurPlan" xml:space="preserve">
    <value>Sur plan (GeneralTerm es)</value>
  </data>
  <data name="TypePlaces" xml:space="preserve">
    <value>Type de prise de places (GeneralTerm es)</value>
  </data>
  <data name="VoirPlaces" xml:space="preserve">
    <value>Voir les places (GeneralTerm es)</value>
  </data>
  <data name="Produit" xml:space="preserve">
    <value>Nom du produit (GeneralTerm es)</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Nom stock (GeneralTerm es)</value>
  </data>
  <data name="Tester" xml:space="preserve">
    <value>Tester (GeneralTerm es)</value>
    <comment>indivUrlmanifs</comment>
  </data>
  <data name="UrlAccessDirect" xml:space="preserve">
    <value>Url pour un accès direct (GeneralTerm es)</value>
    <comment>indivUrlmanifs</comment>
  </data>
  <data name="ErrorDeleteProduct" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression (GeneralTerm es)</value>
  </data>
  <data name="SuccessDeleteProduct" xml:space="preserve">
    <value>Le produit internet a bien été supprimé (GeneralTerm es)</value>
  </data>
  <data name="BddName" xml:space="preserve">
    <value>Nom de la base de données (GeneralTerm es)</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code (GeneralTerm es)</value>
  </data>
  <data name="ConnexionTypeProd" xml:space="preserve">
    <value>Prod (GeneralTerm es)</value>
  </data>
  <data name="ConnexionTypeTest" xml:space="preserve">
    <value>Test (GeneralTerm es)</value>
  </data>
  <data name="ErrorAddConnexion" xml:space="preserve">
    <value>La connexion n'a pu être ajoutée (GeneralTerm es)</value>
  </data>
  <data name="ErrorUpdateConnexion" xml:space="preserve">
    <value>La connexion n'a pu être modifiée (GeneralTerm es)</value>
  </data>
  <data name="ErrorUpdateGroupRights" xml:space="preserve">
    <value>Une erreur s'est produite pendant la mise à jour des droits pour un group d'utilisateurs (GeneralTerm es)</value>
  </data>
  <data name="IPServeur" xml:space="preserve">
    <value>ip du serveur (GeneralTerm es)</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login (GeneralTerm es)</value>
  </data>
  <data name="SuccessAddConnexion" xml:space="preserve">
    <value>La connexion a bien été ajoutée (GeneralTerm es)</value>
  </data>
  <data name="SuccessUpdateConnexion" xml:space="preserve">
    <value>La connexion a bien été modifiée  (GeneralTerm es)</value>
  </data>
  <data name="SuccessUpdateGroupRights" xml:space="preserve">
    <value>La mise à jour des droits pour les groupes d'utilisateurs à bien été effectuée (GeneralTerm es)</value>
  </data>
  <data name="TestConnexion" xml:space="preserve">
    <value>Test connexion (GeneralTerm es)</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Mettre à jour (GeneralTerm es)</value>
  </data>
  <data name="AucuneMaquette" xml:space="preserve">
    <value>Aucune maquette  (GeneralTerm es)</value>
  </data>
  <data name="AucunFichierSauvegarder" xml:space="preserve">
    <value>Aucun fichier récemment sauvegardé (GeneralTerm es)</value>
  </data>
  <data name="GetLastFile" xml:space="preserve">
    <value>Récupérer le dernier fichier sauvegardé (GeneralTerm es)</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>Preview (GeneralTerm es)</value>
  </data>
  <data name="SuccessCssInsert" xml:space="preserve">
    <value>Le fichier CSS à bien été sauvegardé (GeneralTerm es)</value>
  </data>
  <data name="FileNotFound" xml:space="preserve">
    <value>Aucun fichier trouvé default ou dans la structure  (GeneralTerm es)</value>
  </data>
  <data name="ErrorUpdatePropertiesEvents" xml:space="preserve">
    <value>Une erreur est survenue pendant la mise à jour (GeneralTerm es)</value>
  </data>
  <data name="SuccessMailnsert" xml:space="preserve">
    <value>Le texte du mail à bien été sauvegarder (GeneralTerm es)</value>
  </data>
  <data name="SuccessUpdatePropertiesEvents" xml:space="preserve">
    <value>La mise à jour a été exécutée avec succès (GeneralTerm es)</value>
  </data>
  <data name="TabMailConfirmation" xml:space="preserve">
    <value>Confirmation (GeneralTerm es)</value>
  </data>
  <data name="TabMailError" xml:space="preserve">
    <value>Erreur (GeneralTerm es)</value>
  </data>
  <data name="TabMailInscription" xml:space="preserve">
    <value>Inscription (GeneralTerm es)</value>
  </data>
  <data name="TabMailPassword" xml:space="preserve">
    <value>Mot de passe (GeneralTerm es)</value>
  </data>
  <data name="NotSVGDirectoryFound" xml:space="preserve">
    <value>Aucun répertoire SVG trouvé (GeneralTerm es)</value>
  </data>
  <data name="ErrorDeleteSeanceFormule" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression(GeneralTerm es)</value>
  </data>
  <data name="ErrorDeleteStructureOnUser" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression des structures pour l'utilisateur (GeneralTerm es)</value>
  </data>
  <data name="ErrorDeleteUser" xml:space="preserve">
    <value>Une erreur est survenue pendant la suppression de l'utilisateur (GeneralTerm es)</value>
  </data>
  <data name="ErrorEventUser" xml:space="preserve">
    <value>L'utilisateur n'a pu être ajouter (GeneralTerm es)</value>
  </data>
  <data name="ErrorUpdateRule" xml:space="preserve">
    <value>Un problème est survenu pendant la mise à jour  (GeneralTerm es)</value>
  </data>
  <data name="SuccessCommentaireInsert" xml:space="preserve">
    <value>Le commentaire à été enregistré avec succès (GeneralTerm es)</value>
  </data>
  <data name="SuccessDeleteSeanceFormule" xml:space="preserve">
    <value>La séance sur la formule a bien été supprimée(GeneralTerm es)</value>
  </data>
  <data name="SuccessDeleteUser" xml:space="preserve">
    <value>L'utilisateur a bien été supprimé (GeneralTerm es)</value>
  </data>
  <data name="SuccessInsertUser" xml:space="preserve">
    <value>L'utilisateur à bien été créer (GeneralTerm es)</value>
  </data>
  <data name="SuccessUpdateRule" xml:space="preserve">
    <value>La règle à bien été mise à jour (GeneralTerm es)</value>
  </data>
  <data name="SuccessUpdateUser" xml:space="preserve">
    <value>L'utilisateur à bien été mis à jour (GeneralTerm es)</value>
  </data>
  <data name="ErrorDelierConnexion" xml:space="preserve">
    <value>La connexion n'a pu être déliée  (GeneralTerm es)</value>
  </data>
  <data name="ErrorInsertDataBase" xml:space="preserve">
    <value>La base de données n'a pu être créée  (GeneralTerm es)</value>
  </data>
  <data name="ErrorInsertStructure" xml:space="preserve">
    <value>Une erreur est survenue pendant l'insertion de la nouvelle structure (GeneralTerm es)</value>
  </data>
  <data name="ErrorLierConnexion" xml:space="preserve">
    <value>La connexion n'a pu être déliée  (GeneralTerm es)</value>
  </data>
  <data name="SuccessDelierConnexion" xml:space="preserve">
    <value>La connexion à été déliée avec succès  (GeneralTerm es)</value>
  </data>
  <data name="SuccessInsertDataBase" xml:space="preserve">
    <value>La base de données à été créée avec succès  (GeneralTerm es)</value>
  </data>
  <data name="SuccessInsertStructure" xml:space="preserve">
    <value>La nouvelle structure à bien été enregistrée (GeneralTerm es)</value>
  </data>
  <data name="SuccessLierConnexion" xml:space="preserve">
    <value>La connexion à été déliée avec succès  (GeneralTerm es)</value>
  </data>
  <data name="lblBtnShift" xml:space="preserve">
    <value>Vous pouvez sélectionner plusieurs séances en appuyant sur le bouton SHIFT (GeneralTerm es)</value>
  </data>
  <data name="SelectAllManifs" xml:space="preserve">
    <value>Sélectionner toutes les manifestations (GeneralTerm es)</value>
  </data>
  <data name="SelectAllProducts" xml:space="preserve">
    <value>sélectionner tous les produits  (GeneralTerm es)</value>
  </data>
  <data name="SelectAllTarifs" xml:space="preserve">
    <value>Seleccionar todas las tarifas</value>
  </data>
  <data name="DetailComplet" xml:space="preserve">
    <value>Detalle completo</value>
  </data>
  <data name="ReferenceUniqueBillet" xml:space="preserve">
    <value>Referencia única del billete</value>
  </data>
  <data name="DernierEtatPlace" xml:space="preserve">
    <value>Último estado del asiento en el período</value>
  </data>
  <data name="IdFiliereVente" xml:space="preserve">
    <value>ID del canal de venta</value>
  </data>
  <data name="NomFiliereVente" xml:space="preserve">
    <value>Nombre del canal de venta del billete</value>
  </data>
  <data name="IdManifestation" xml:space="preserve">
    <value>ID de la manifestación</value>
  </data>
  <data name="NomManifestation" xml:space="preserve">
    <value>Nombre de la manifestación</value>
  </data>
  <data name="GroupeManifestations" xml:space="preserve">
    <value>Grupo de manifestaciones</value>
  </data>
  <data name="GenreManifestation" xml:space="preserve">
    <value>Género de la manifestación</value>
  </data>
  <data name="SousGenreManifestation" xml:space="preserve">
    <value>Sub-género de la manifestación</value>
  </data>
  <data name="CibleManifestation" xml:space="preserve">
    <value>Objetivo de la manifestación</value>
  </data>
  <data name="IdSeance" xml:space="preserve">
    <value>ID de la sesión</value>
  </data>
  <data name="DateDebutSeance" xml:space="preserve">
    <value>Fecha de inicio de la sesión</value>
  </data>
  <data name="IdCategorie" xml:space="preserve">
    <value>ID de la categoría</value>
  </data>
  <data name="NomCategorie" xml:space="preserve">
    <value>Nombre de la categoría</value>
  </data>
  <data name="IdTarif" xml:space="preserve">
    <value>ID de la tarifa</value>
  </data>
  <data name="NomTarif" xml:space="preserve">
    <value>Nombre de la tarifa</value>
  </data>
  <data name="NumeroCommande" xml:space="preserve">
    <value>Número de pedido</value>
  </data>
  <data name="ModePaiement" xml:space="preserve">
    <value>Modo de pago</value>
  </data>
  <data name="DateOperation" xml:space="preserve">
    <value>Fecha de operación</value>
  </data>
  <data name="IdIdentite" xml:space="preserve">
    <value>ID de identidad</value>
  </data>
  <data name="NomIdentite" xml:space="preserve">
    <value>Nombre de identidad</value>
  </data>
  <data name="PrenomIdentite" xml:space="preserve">
    <value>Apellido de identidad</value>
  </data>
  <data name="Civilite" xml:space="preserve">
    <value>Cortesía</value>
  </data>
  <data name="CodePostal" xml:space="preserve">
    <value>Código Postal</value>
  </data>
  <data name="Ville" xml:space="preserve">
    <value>Ciudad</value>
  </data>
  <data name="Pays" xml:space="preserve">
    <value>País</value>
  </data>
  <data name="DateNaissance" xml:space="preserve">
    <value>Fecha de nacimiento</value>
  </data>
  <data name="FiliereCreationIdentite" xml:space="preserve">
    <value>Canal de creación de identidad</value>
  </data>
  <data name="TelephoneMobile" xml:space="preserve">
    <value>Teléfono móvil</value>
  </data>
  <data name="AdressePostale" xml:space="preserve">
    <value>Dirección postal</value>
  </data>
  <data name="OptIn" xml:space="preserve">
    <value>OPT-IN</value>
  </data>
  <data name="NumeroBillet" xml:space="preserve">
    <value>Número de billete</value>
  </data>
  <data name="TitleMaquettes" xml:space="preserve">
    <value>Maquettes (GeneralTerm es)</value>
  </data>
  <data name="TitleResume" xml:space="preserve">
    <value>Activer le résumé (GeneralTerm es)</value>
  </data>
  <data name="TitleSelectProducts" xml:space="preserve">
    <value>Sélectionnez le ou les produit(s)  (GeneralTerm es)</value>
  </data>
  <data name="lblTitleCreate" xml:space="preserve">
    <value>Création d'une règle de vente pour [manifName] le [sessionDate]   (GeneralTerm es)</value>
  </data>
  <data name="lblTitleDateChoice" xml:space="preserve">
    <value>Choix des dates  (GeneralTerm es)</value>
  </data>
  <data name="lblTitleEdit" xml:space="preserve">
    <value>Edition  pour [manifName] (GeneralTerm es)</value>
  </data>
  <data name="TooltipDeleteSeancesAbonnement" xml:space="preserve">
    <value>Supprimer la règle sur les séances de l'abonnement (GeneralTerm es)</value>
  </data>
  <data name="ErrorSelectManifs" xml:space="preserve">
    <value>Sélectionnez au moins une manifestation  (GeneralTerm es)</value>
  </data>
  <data name="ExportExcel" xml:space="preserve">
    <value>Exporter sous Excel (GeneralTerm es)</value>
  </data>
  <data name="ExportPDF" xml:space="preserve">
    <value>Exporter en PDF (GeneralTerm es)</value>
  </data>
  <data name="TitleCashStatment" xml:space="preserve">
    <value>Etat de caisse (GeneralTerm es)</value>
  </data>
  <data name="KeyConfigNotFound" xml:space="preserve">
    <value>La clé dans le web.config n'existe pas  (GeneralTerm es)</value>
  </data>
  <data name="SvgFileNotExist" xml:space="preserve">
    <value>le dossier svg n'existe pas (GeneralTerm es)</value>
  </data>
  <data name="FileNotFoundSvg" xml:space="preserve">
    <value>Aucun fichier trouvé dans le dossier SVG (GeneralTerm es)</value>
  </data>
  <data name="PlayJob" xml:space="preserve">
    <value>reproducir el trabajo (contraintes_ventes es)</value>
  </data>
</root>