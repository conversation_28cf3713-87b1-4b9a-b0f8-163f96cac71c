{"version": 2, "dgSpecHash": "pYEK1o0aEo0=", "success": true, "projectFilePath": "D:\\WORK\\VIEUXPROJETS\\ws_BLL\\ws_BLL.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\bcrypt.net\\0.1.0\\bcrypt.net.0.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\entityframework\\6.1.3\\entityframework.6.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iso3166\\1.0.4\\iso3166.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\log4net\\3.1.0\\log4net.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\1.1.0\\microsoft.bcl.asyncinterfaces.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\7.0.3\\microsoft.identitymodel.abstractions.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\7.0.3\\microsoft.identitymodel.jsonwebtokens.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\7.0.3\\microsoft.identitymodel.logging.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\7.0.3\\microsoft.identitymodel.tokens.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\restsharp\\106.15.0\\restsharp.106.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.0.3\\system.identitymodel.tokens.jwt.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.7.1\\system.runtime.compilerservices.unsafe.4.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\4.7.2\\system.text.encodings.web.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.2\\system.text.json.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512"], "logs": [{"code": "NU1902", "level": "Warning", "warningLevel": 1, "message": "Le package 'System.IdentityModel.Tokens.Jwt' 7.0.3 présente une vulnérabilité de gravité moyenne connue, https://github.com/advisories/GHSA-59j7-ghrg-fj52.", "libraryId": "System.IdentityModel.Tokens.Jwt", "targetGraphs": [".NETFramework,Version=v4.8", ".NETFramework,Version=v4.8/win", ".NETFramework,Version=v4.8/win-arm64", ".NETFramework,Version=v4.8/win-x64", ".NETFramework,Version=v4.8/win-x86"]}, {"code": "NU1605", "level": "Warning", "warningLevel": 1, "message": "Passage à une version antérieure du package détecté : RestSharp de 112.1.0 à 106.15.0. Référencez le package directement à partir du projet pour sélectionner une version différente. \r\n ws_bll -> Themis.Libraries.Utilities -> RestSharp (>= 112.1.0) \r\n ws_bll -> RestSharp (>= 106.15.0)", "libraryId": "RestSharp", "targetGraphs": [".NETFramework,Version=v4.8", ".NETFramework,Version=v4.8/win", ".NETFramework,Version=v4.8/win-arm64", ".NETFramework,Version=v4.8/win-x64", ".NETFramework,Version=v4.8/win-x86"]}]}