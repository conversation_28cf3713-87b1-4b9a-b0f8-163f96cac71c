﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Principale.Master" AutoEventWireup="true"
    CodeBehind="cash_statement.aspx.cs" Inherits="login.pages_stats.cash_statement" culture="auto" meta:resourcekey="PageResource1" uiculture="auto" %>

<asp:Content ID="Content1" ContentPlaceHolderID="HeadContent" runat="server">
    <!-- Inclusion du fichier de traduction des colonnes -->
    <script src="../assets/js/column-header-translation.js" type="text/javascript"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
    <div id="error">
        <asp:Label ID="lblError" CssClass="hidden" runat="server" 
            meta:resourcekey="lblErrorResource1"></asp:Label>
    </div>
    <div class="widget-box">
        <div class="widget-header widget-header-blue widget-header-flat">
            <h4 class="lighter"> <asp:Label ID="lblTitle" runat="server" Text="Etat de caisse" 
                    meta:resourcekey="lblTitleResource1"></asp:Label></h4>
        </div>
        <div class="widget-body">
            <div class="widget-main">
                <div id="fuelux-wizard" class="row" data-target="#step-container">
                    <ul class="wizard-steps">
                        <li data-target="#step1" class="active"><span class="step">1</span> <span class="title"> 
                            <asp:Label ID="lblTitleManif" runat="server" Text="Manifestations" 
                                meta:resourcekey="lblTitleManifResource1"></asp:Label> </span> </li>
                        <li data-target="#step2"><span class="step">2</span> <span class="title">
                            <asp:Label ID="lblTitleTarif" runat="server" Text="Tarifs" 
                                meta:resourcekey="lblTitleTarifResource1"></asp:Label></span>
                        </li>
                        <li data-target="#step3"><span class="step">3</span> <span class="title">
                            <asp:Label ID="lblTitleDate" runat="server" Text="Date" 
                                meta:resourcekey="lblTitleDateResource1"></asp:Label></span>
                        </li>
                        <li data-target="#step4"><span class="step">4</span> <span class="title">
                            <asp:Label ID="lblTitleResume" runat="server" Text="Résumé" 
                                meta:resourcekey="lblTitleResumeResource1"></asp:Label></span>
                        </li>
                    </ul>
                </div>
                <hr />
                <div class="row wizard-actions">
                  <button class="btn btn-primary btn-export" disabled="disabled" id="btnPdfExport" type="button">
                        <i class="icon-download"></i><asp:Label ID="lblEportPDF" runat="server" 
                            Text="Exporter en PDF" meta:resourcekey="lblEportPDFResource1"></asp:Label>
                    </button>
           

                    <button class="btn btn-primary btn-export" disabled="disabled"  id="btnExcelExport" type="button" >
                        <i class="icon-download"></i><asp:Label ID="lblExportExcel" runat="server" 
                            Text="Exporter sous Excel" meta:resourcekey="lblExportExcelResource1"></asp:Label>
                    </button>

                    <button class="btn btn-prev" disabled="disabled" type="button">
                        <i class="icon-arrow-left"></i><asp:Label ID="lblPrevious" runat="server" 
                            Text="Précédent" meta:resourcekey="lblPreviousResource1"></asp:Label>
                    </button>
                    <button class="btn btn-success btn-next" data-last="Finish" type="button" id="btnNext">
                        <asp:Label ID="lblNext" runat="server" meta:resourcekey="lblNextResource1"></asp:Label> 
                            <i class="icon-arrow-right icon-on-right"></i>
                    </button>
                </div>
                <div class="step-content row position-relative" id="step-container">
                    <div class="step-pane active" id="step1">
                        <div class="col-lg-6  col-lg-offset-1">
                            <div class="widget-box">
                                <div class="widget-header header-color-blue2">
                                    <h4 class="lighter white ">
                                        <asp:Label ID="lblTitleSelectManifs" runat="server" 
                                            Text="Sélectionnez les manifestations" 
                                            meta:resourcekey="lblTitleSelectManifsResource1"></asp:Label></h4>
                                </div>
                                <div class="widget-body">
                                    <div class="widget-main padding-8">
                                      <%--  <input type="text" name="search" value="" id="id_search_list">
                                        --%>
                                        
                                        <!-- tree view -->
                                        <div id="Eventstree">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="step-pane" id="step2">
                        <div class="col-lg-12 ">
                            <div class="widget-box">
                                <div class="widget-header header-color-blue2">
                                    <h4 class="lighter white ">
                                        <asp:Label ID="lblTitleTarifs" runat="server" Text="Tarifs des manifestations" 
                                            meta:resourcekey="lblTitleTarifsResource1"></asp:Label>
                                    </h4>
                                </div>
                                <div class="widget-body">
                                    <div class="widget-main padding-8" id="tarifManifs">
                                        <%-- Ici sera intégrer la liste des manifs avec une checkbox a coté--%>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="step-pane" id="step3">
                        <div class="widget-box">
                            <div class="widget-header header-color-blue2">
                                <h4 class="lighter white" >
                                    <asp:Label ID="lblActivDate" runat="server" Text="Activer les dates" 
                                        meta:resourcekey="lblActivDateResource1"></asp:Label>
                                </h4>
                                <div class="widget-toolbar">
                                    <label>
                                        <small class="lighter white " >
                                            <b>   
                                                <asp:Label ID="lblActiveOptions"  runat="server" Text="Activer"  meta:resourcekey="lblActiveOptionsResource1"></asp:Label>   
                                                <asp:Label ID="lblDesOptions" runat="server" Text="Désactiver"   meta:resourcekey="lblDesOptionsResource1"></asp:Label>  
                                           </b>
                                        </small>
                                        <input id="id-check-horizontal" type="checkbox" class="ace ace-switch ace-switch-6" />
                                        <span class="lbl"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="widget-body">
                                <div class="widget-body-inner" style="display: block;">
                                    <div class="widget-main" id="optionDates">
                                        <div class="form-group col-lg-5">
                                            <asp:Label CssClass="col-lg-4 control-label" for="fromDate" ID="lblFromDate" Text="Date de début"
                                                runat="server" meta:resourcekey="lblFromDateResource1"></asp:Label>
                                            <input type="text" class="controls" id="fromDate" disabled="true" />
                                        </div>
                                        <asp:Label CssClass="col-lg-1 control-label" for="toDate" ID="lblToDate" Text="Date de fin"
                                            runat="server" meta:resourcekey="lblToDateResource1"></asp:Label>
                                        <input type="text" class="controls" id="toDate" disabled="true" />
                                    </div>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="step-pane" id="step4">

            <div id="resume"></div>
            <div class="dataTables">
                <%--ici sera toutes les tables pour les états--%>
            </div>
        </div>
        <hr />
    </div>
    <!-- /widget-main -->
    </div>
    <!-- /widget-body -->
    
    <script src="../assets/js/quickSearch/jquery.quicksearch.js" type="text/javascript"></script>
     <script src="../assets/js/date-time/jquery.datetimepicker.js" type="text/javascript"></script>
    <script src="../assets/js/date-time/dataTimePickerCustom.js" type="text/javascript"></script>
      <script src="../assets/js/fuelux/fuelux.wizard.min.js" type="text/javascript"></script>
    <script src="../assets/js/ace-elements.min.js" type="text/javascript"></script>

  <%--  <script src="../assets/js/date-time/jquery.datetimepicker.js" type="text/javascript"></script>
    <script src="../assets/js/date-time/dataTimePickerCustom.js" type="text/javascript"></script>
    <script src="../assets/js/fuelux/fuelux.wizard.min.js" type="text/javascript"></script>
    <script src="../assets/js/ace.min.js" type="text/javascript"></script>
    <script src="../assets/js/ace-elements.min.js" type="text/javascript"></script>
    <script src="../assets/js/jquery.dataTables.js" type="text/javascript"></script>
    <script src="../assets/js/jquery.dataTables.bootstrap.js" type="text/javascript"></script>--%>
   

    <script type="text/javascript">
       
        var treeDataSource;
        var oTable;
        var sAjaxSourceUrl = "cash_statement.aspx/";
        $(document).ready(function () {

            var currentdate = new Date();
            var startDatetime = getLocaleShortDateString(currentdate) + " 00:00";
            var endDatetime = getLocaleShortDateString(currentdate) + " " +currentdate.timeNow();


            ////            var currentdate = new Date();
            ////            currentdate.toLocaleString();

            ////            var startDatetime = currentdate.today() + " 00:00";
            ////            var endDatetime = currentdate.today() + " " + currentdate.timeNow();

            //rend invisible le label pour activer les dates
            $('#<%= lblActiveOptions.ClientID %>').hide();

            var lang = $("#dateLang").val();
            //convertit le format de date
            $.datepicker.setDefaults($.datepicker.regional[lang]);

            $('#toDate').val(endDatetime);
            $('#fromDate').val(startDatetime);

                        
            $('#toDate').datetimepicker();
            $('#fromDate').datetimepicker();

            $("#id-check-horizontal").on('change', function () {
                if ($("#optionDates input").prop('disabled')) {
                    $('#<%= lblDesOptions.ClientID %>').hide();
                    $('#<%= lblActiveOptions.ClientID %>').show();
                    $('#<%= lblActiveOptions.ClientID %>').text();

                    // $("#id-check-horizontal").parent().find("small").find("b").html("Activer")
                    $("#optionDates input[type='text']").prop("disabled", false);
                } else {
                    $('#<%= lblDesOptions.ClientID %>').show();
                    $('#<%= lblActiveOptions.ClientID %>').hide();
                    $('#<%= lblDesOptions.ClientID %>').text();
                    //$("#id-check-horizontal").parent().find("small").find("b").html("Désactiver")
                    $("#optionDates input[type='text']").prop("disabled", true);
                }
            });



            $.ajax({
                type: "POST",
                url: sAjaxSourceUrl + 'GetEventsData',
                contentType: 'application/json; charset=utf-8',
                dataType: "json",
                success: function (response) {

                    var parsed = $.parseJSON(response.d);
                    var htmlUl = "<input type='text' class='search-input' autocomplete='off' id='id_search_list' placeholder='rechercher ici'><br />";
                    htmlUl += "<ul id='tree' > "

                    $.each(parsed, function (i, jsondata) {
                        //parents
                        htmlUl += "<li> <input type='checkbox' class='ace_custom' idevent='" + jsondata.EventId + "' value='" + jsondata.EventName + "' name='chkEventName' /><label class='parent'> <span class='lbl'>" + jsondata.EventName + "</span> </label> ";
                        if (jsondata.ListSessions.length > 0) {
                            // enfants
                            htmlUl += "<ul  class='child' idevent='" + jsondata.EventId + "'>";
                            $.each(jsondata.ListSessions, function (li, dataSession) {
                                htmlUl += "<li><label > <input name='chkSession' type='checkbox' class='ace' idevent='" + dataSession.EventId + "' value='" + dataSession.SessionId + "' /> <span class='lbl'>" + dataSession.sSessionStartDate + "</span> </label>  </li>";
                            });
                            htmlUl += "</ul>";
                        }

                        htmlUl += "</li>";
                        var x = jsondata;
                    });

                    htmlUl += "</ul>";

                    $('#Eventstree').append(htmlUl);
                    $('ul#tree').checktree();

                    $('.child').hide();
                    $('label.parent').on("click", function () {
                        if ($(this).hasClass("plusimageapply")) {
                            $(this).removeClass('plusimageapply');
                            $(this).parent().find('ul').hide();
                        } else {
                            // $(this).css('list-style-image',
                            $(this).addClass('plusimageapply');
                            $(this).parent().find('ul').show();
                        }
                    });



                    //                    var qs = $('input#id_search_list').quicksearch('ul#tree li');
                    var qs = $('input#id_search_list').quicksearch('ul#tree li',
                             {
                                 //                                'delay': 100,
                                 //                                'selector': 'ul',
                                 //                          
                                 //                                'loader': 'span.loading',
                                 //                                'bind': 'keyup keydown',
                                 //                                 'onBefore': function () {
                                 //                                     console.log('on before');
                                 //                                 },
                                 //                                 'onAfter': function () {
                                 //                                     console.log('on after');
                                 //                                 },
                                 'show': function () {
                                     //$('label.parent').parent().addClass('show');
                                     $(this).addClass('show');
                                 },
                                 'hide': function () {
                                     //                                   // $('label.parent').parent().addClass('hide');
                                     //                                    $('label.parent').parent().removeClass('show');
                                     $(this).addClass('hide');
                                     $(this).removeClass('show');

                                     $(this).children().removeClass('hide')
                                     $('ul.child').each(function (i, itm) {
                                         $(this).children().removeClass('hide')

                                     });
                                 },
                                 'prepareQuery': function (val) {
                                     return new RegExp(val, "i");
                                 },
                                 'testQuery': function (query, txt, _row) {
                                     // console.log(_row);
                                     return query.test(txt);
                                 }

                             });
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert('Error occurred: ' + XMLHttpRequest.statusText);
                }
            });

            $("#btnPdfExport").on('click', function () {
                var _resume = $('#resume').html();
                var _tables = $('.dataTables').html();

                var sData = JSON.stringify({ resume: _resume, tables: _tables });

                $.ajax({
                    type: "POST",
                    url: sAjaxSourceUrl + 'DownloadPDFFile',
                    data: sData,
                    contentType: 'application/json; charset=utf-8',
                    dataType: "json",
                    success: function (response) {
                        window.open("pdf/" + response.d, response.d, 'width=1024,height=768,toolbar=no,location=no,directories=no,status=no,menubar=no,copyhistory=no')
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alert('Error occurred form PDF file :' + XMLHttpRequest.statusText);
                    }
                });
            });


            $("#btnExcelExport").on('click', function (e) {

                var _resume = $('#resume').html();
                var _tables = $('.dataTables').html();

                var html = "";
                var resume = "";
                $("#resume div.widget-main p ").each(function (i, item) {
                    resume += item.innerText.sansAccent() + "\n";
                });

                html += resume + " \n \n ";
                $('.dataTables div.table-responsive table').each(function (i, item) {
                    var head = item.tHead;
                    var body = item.tBodies;
                    var foot = item.tFoot;

                    html += head.innerText.sansAccent() + " " + body[0].innerText.sansAccent() + " " + foot.innerText.sansAccent();
                    html += "\n";
                });
                // window.open(‘data:text/csv;charset=utf-8;base64,’ + $.base64Encode(html));

                window.open('data:application/vnd.ms-excel;charset=utf-8;,' + encodeURIComponent(html));
                e.preventDefault();

            });
        });


        //supprime les accents sur le texte
        String.prototype.sansAccent = function () {
            var accent = [
                    /[\300-\306]/g, /[\340-\346]/g, // A, a
                    /[\310-\313]/g, /[\350-\353]/g, // E, e
                    /[\314-\317]/g, /[\354-\357]/g, // I, i
                    /[\322-\330]/g, /[\362-\370]/g, // O, o
                    /[\331-\334]/g, /[\371-\374]/g, // U, u
                    /[\321]/g, /[\361]/g, // N, n
                    /[\307]/g, /[\347]/g, // C, c
                ];
            var noaccent = ['A', 'a', 'E', 'e', 'I', 'i', 'O', 'o', 'U', 'u', 'N', 'n', 'C', 'c'];

            var str = this;
            for (var i = 0; i < accent.length; i++) {
                str = str.replace(accent[i], noaccent[i]);
            }

            return str;
        }

        //récupère les checbox selectionnés des manifestations 0 pour tout
        function GetEventsNameSelected() {
            var chkEventsName = new Array();

                $('input[name=chkEventName]:checked').each(function () {
                    chkEventsName.push(this.value);
                });
           
            return chkEventsName;
        }

        //récupère les sessionsID selectionnés
        function GetSessionsIDSelected() {
            var selectedchkSessionID = new Array();

            $('input[name=chkSession]:checked').each(function () {
                selectedchkSessionID.push(this.value);
            });
            return selectedchkSessionID;
        }

        //Récupère les noms de tarifs 0 pour tout récupérer
        function GetTarifNameSelected(nb) {
            var arrTarifEvent = new Array();

            if (nb == 0) {

                $('input[name="chkTarifID"]:checked').each(function () {
                    arrTarifEvent.push(this.attributes.tarifname.value);
                    // console.log(this.value);
                });
            } else {
            $('input[name="chkTarifID"]:checked').each(function (i, itm) {
                if (i <= nb) {
                    arrTarifEvent.push(this.attributes.tarifname.value);
                }
                // console.log(this.value);
            });
            }
            return arrTarifEvent;
        }

        //Récupère les ID tarifs
        function GetTarifIDSelected() {

            var arrTarifEvent = new Array();

            $('input[name="chkTarifID"]:checked').each(function () {
                arrTarifEvent.push(this.value);
                // console.log(this.value);
            });

            return arrTarifEvent;
        }

        //Récupère le nom des tarifs
        function GetTarifsManifs() {
            var sessionIdSelected = GetSessionsIDSelected();
            var sData = JSON.stringify({ _sessionId: sessionIdSelected });

            $.ajax({
                type: "POST",
                url: sAjaxSourceUrl + 'GetTarifsManifs',
                data: sData,
                contentType: 'application/json; charset=utf-8',
                dataType: "json",
                success: function (response) {
                    var parsed = $.parseJSON(response.d);
                    var htmlCheck = "";
                    $.each(parsed, function (i, jsondata) {
                        htmlCheck = htmlCheck + "<div class='checkbox'><label><input name='chkTarifID' tarifName='" + jsondata.type_tarif_nom + "' checked type='checkbox' class='ace' value='" + jsondata.type_tarif_id + "' /> <span class='lbl'> " + jsondata.type_tarif_nom + "</span> </label> </div>";
                    });

                    $("#tarifManifs").html(htmlCheck);
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert('Error occurred: ' + XMLHttpRequest.statusText);
                }
            });
        }


        function GetSessionState() {
            var _startDate = $('#fromDate').val();
            var _endDate = $('#toDate').val();
          

            if ($("#optionDates input[type='text']").prop('disabled')) {
                _startDate = "";
                _endDate = "";
            }

            var sessionIdSelected = GetSessionsIDSelected();
            var tarifIdSelected = GetTarifIDSelected();
             var eventNameSelected = GetEventsNameSelected();

            // var sData = JSON.stringify({ sessionsId: sessionIdSelected, tarifId: tarifIdSelected, eventsName: eventNameSelected, startDate: _startDate, endDate: _endDate, userLanguage: _userLanguage });
             var sData = JSON.stringify({ sessionsId: sessionIdSelected, tarifId: tarifIdSelected, eventsName: eventNameSelected, startDate: _startDate, endDate: _endDate });

             $.ajax({
                 type: "POST",
                 url: sAjaxSourceUrl + 'GetSessionState',
                 data: sData,
                 contentType: 'application/json; charset=utf-8',
                 dataType: "json",
                 success: function (response) {

                     // ShowError("error", response.d.ERROR, "alert alert-success alert-dismissable");

                     if (response.d.substring(0, 3) == "OK:") {
                         $("#resume").html("");

                         var htmlResume = " <div class='widget-box'><div class='widget-header header-color-blue2'><h4 class='lighter white'>Résumé des sélections précédentes</h4></div>";
                         htmlResume += "<div class='widget-body'><div class='widget-main'>";
                         htmlResume += "<p><span class='bold'>Manifestation sélectionnés : </span>" + GetEventsNameSelected().join(', ') + "</p>";
                         htmlResume += "<p><span class='bold'>Tarifs sélectionnés : </span>" + GetTarifNameSelected(5).join(', ') + "</p>";

                         if ($("#id-check-horizontal:checked").length > 0) {
                             htmlResume += "<p><span class='bold'>Date sélectionnés : </span> de " + $("#fromDate").val() + " à " + $("#toDate").val() + "</p>";
                         } else {
                             htmlResume += "<p><span class='bold'>Date sélectionnés : </span> Aucun filtre</p>";
                         }
                         htmlResume += "</div></div><div class='space'></div>";

                         $("#resume").html(htmlResume);
                         $("#resume").show();

                         var htmlFinal = response.d.substring(3);
                         // Ajoute les tableaux créer en c#
                         $('.dataTables').html(htmlFinal);

                         // Applique la traduction des en-têtes pour tous les tableaux
                         $('.dataTables table').each(function() {
                             var tableId = $(this).attr('id');
                             if (!tableId) {
                                 // Génère un ID unique si le tableau n'en a pas
                                 tableId = 'table_' + Math.random().toString(36).substr(2, 9);
                                 $(this).attr('id', tableId);
                             }
                             TranslateTableHeaders(tableId);
                         });
                     } else {
                         ShowError("error", response.d, "alert alert-danger alert-dismissable");
                     }
                 },
                 error: function (XMLHttpRequest, textStatus, errorThrown) {
                     alert('Error occurred: ' + XMLHttpRequest.statusText);
                 }
             });
        }


        jQuery(function ($) {
            $('[data-rel=tooltip]').tooltip();
            $('#fuelux-wizard').ace_wizard().on('change', function (e, info) {
                if (info.step == 1 && info.direction == "next" && $('input[name=chkSession]:checked').length > 0) {
                    GetTarifsManifs();
                } else if (info.step == 2 && info.direction == "next" && $('input[type=checkbox]:checked').length > 0) {
                    GetTarifIDSelected();
                } else if (info.step == 3 && info.direction == "next") {
                   
                    $('.dataTables').html('');
                    GetSessionState();

                    //Active les bouton exports
                    $(".btn-export").prop("disabled", false);
                } else {
                    if (info.direction == "previous" && info.step == 4)
                        $('.dataTables').html('');

                    if (info.direction == "next") {
                        ShowError("error", "Selectionnez au moins une manifestation", "alert alert-danger alert-dismissable");
                        return false;
                    } else {
                        $("#resume").hide();
                        return true;
                    }
                }

            }).on('finished', function (e) {
                //(btnPdfExport
                //  ShowError("error", "Merci", "success");
            }).on('stepclick', function (e) {
                return false; //prevent clicking on steps
            });
        })

    

    </script>
</asp:Content>
