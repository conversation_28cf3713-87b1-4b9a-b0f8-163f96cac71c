# Script de configuration PayPal pour CustomerArea
# Ce script aide à configurer les fichiers nécessaires pour l'authentification PayPal

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Configuration PayPal pour CustomerArea" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Identifiants PayPal Sandbox (fournis)
$clientId = "AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw"
$clientSecret = "EO70VAO37ZELyJxtR7rR-lrFJVTTgRzWQsxtt1NAEcuj8CSKWP91c0n4ALcB0MXYnBjpXCXUsd4BnNDJ"

# Demander l'ID de structure
Write-Host "Entrez l'ID de votre structure (ex: 0224) :" -ForegroundColor Yellow
$structureId = Read-Host

if ([string]::IsNullOrWhiteSpace($structureId)) {
    Write-Host "Erreur : L'ID de structure est requis !" -ForegroundColor Red
    exit 1
}

# Demander le domaine
Write-Host ""
Write-Host "Entrez votre domaine (ex: dev.themisweb.fr) :" -ForegroundColor Yellow
$domain = Read-Host

if ([string]::IsNullOrWhiteSpace($domain)) {
    Write-Host "Erreur : Le domaine est requis !" -ForegroundColor Red
    exit 1
}

# Demander l'environnement
Write-Host ""
Write-Host "Sélectionnez l'environnement :" -ForegroundColor Yellow
Write-Host "1. DEV (par défaut)"
Write-Host "2. TEST"
Write-Host "3. PROD"
$envChoice = Read-Host "Votre choix (1-3)"

$environment = "DEV"
switch ($envChoice) {
    "2" { $environment = "TEST" }
    "3" { $environment = "PROD" }
    default { $environment = "DEV" }
}

Write-Host ""
Write-Host "Configuration sélectionnée :" -ForegroundColor Green
Write-Host "  - Structure ID : $structureId" -ForegroundColor White
Write-Host "  - Domaine : $domain" -ForegroundColor White
Write-Host "  - Environnement : $environment" -ForegroundColor White
Write-Host ""

# Construire les chemins
$structureIdPadded = $structureId.PadLeft(4, '0')
$basePath = "D:\customerfiles\$environment\$structureIdPadded"
$configIniPath = "$basePath\CONFIGSERVER\config.ini.xml"
$appsettingsPath = "$basePath\Customer\APPSETINGS\appsettings.json"

Write-Host "Chemins des fichiers :" -ForegroundColor Cyan
Write-Host "  - config.ini.xml : $configIniPath" -ForegroundColor White
Write-Host "  - appsettings.json : $appsettingsPath" -ForegroundColor White
Write-Host ""

# Vérifier si les fichiers existent
$configIniExists = Test-Path $configIniPath
$appsettingsExists = Test-Path $appsettingsPath

if (-not $configIniExists) {
    Write-Host "ATTENTION : Le fichier config.ini.xml n'existe pas !" -ForegroundColor Red
    Write-Host "Chemin : $configIniPath" -ForegroundColor Yellow
    Write-Host ""
}

if (-not $appsettingsExists) {
    Write-Host "ATTENTION : Le fichier appsettings.json n'existe pas !" -ForegroundColor Red
    Write-Host "Chemin : $appsettingsPath" -ForegroundColor Yellow
    Write-Host ""
}

# Demander confirmation
Write-Host "Voulez-vous continuer ? (O/N)" -ForegroundColor Yellow
$confirm = Read-Host

if ($confirm -ne "O" -and $confirm -ne "o") {
    Write-Host "Configuration annulée." -ForegroundColor Yellow
    exit 0
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Génération des configurations..." -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# ========================================
# 1. Configuration config.ini.xml
# ========================================

$configIniContent = @"
<!-- Configuration PayPal - Ajoutez ces lignes dans votre config.ini.xml -->
<PAYPAL_CONNECTUSERNAME>$clientId</PAYPAL_CONNECTUSERNAME>
<PAYPAL_CONNECTPASSWORD>$clientSecret</PAYPAL_CONNECTPASSWORD>
"@

Write-Host "1. Configuration pour config.ini.xml :" -ForegroundColor Green
Write-Host $configIniContent -ForegroundColor White
Write-Host ""

# Sauvegarder dans un fichier temporaire
$tempConfigIniPath = ".\config.ini.paypal.txt"
$configIniContent | Out-File -FilePath $tempConfigIniPath -Encoding UTF8
Write-Host "   Sauvegardé dans : $tempConfigIniPath" -ForegroundColor Cyan
Write-Host ""

# ========================================
# 2. Configuration appsettings.json
# ========================================

$returnUrl = "https://$domain/customer/_loginPayPal.aspx?idstructure=[structureid]"

$appsettingsContent = @"
{
  "global": {
    "guest": false,
    "light": false,
    "passwordRules": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$"
  },
  "login": {
    "facebookConnect": false,
    "facebook": {
      "applicationId": "",
      "urlJsLang": "//connect.facebook.net/fr_FR/sdk.js"
    },
    "payPalConnect": true,
    "payPal": {
      "appid": "$clientId",
      "returnurl": "$returnUrl"
    },
    "unidyConnect": false,
    "unidy": {
      "clientId": "",
      "redirectUrl": ""
    }
  }
}
"@

Write-Host "2. Configuration pour appsettings.json :" -ForegroundColor Green
Write-Host $appsettingsContent -ForegroundColor White
Write-Host ""

# Sauvegarder dans un fichier temporaire
$tempAppsettingsPath = ".\appsettings.paypal.json"
$appsettingsContent | Out-File -FilePath $tempAppsettingsPath -Encoding UTF8
Write-Host "   Sauvegardé dans : $tempAppsettingsPath" -ForegroundColor Cyan
Write-Host ""

# ========================================
# 3. URL de retour PayPal
# ========================================

$paypalReturnUrl = "https://$domain/customer/_loginPayPal.aspx?idstructure=$structureId"

Write-Host "3. URL de retour à configurer dans PayPal Developer Dashboard :" -ForegroundColor Green
Write-Host $paypalReturnUrl -ForegroundColor White
Write-Host ""

# Sauvegarder dans un fichier temporaire
$tempPaypalUrlPath = ".\paypal-return-url.txt"
$paypalReturnUrl | Out-File -FilePath $tempPaypalUrlPath -Encoding UTF8
Write-Host "   Sauvegardé dans : $tempPaypalUrlPath" -ForegroundColor Cyan
Write-Host ""

# ========================================
# Résumé
# ========================================

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Configuration terminée !" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Fichiers générés :" -ForegroundColor Green
Write-Host "  1. config.ini.paypal.txt - Contenu à ajouter dans config.ini.xml" -ForegroundColor White
Write-Host "  2. appsettings.paypal.json - Contenu pour appsettings.json" -ForegroundColor White
Write-Host "  3. paypal-return-url.txt - URL de retour pour PayPal" -ForegroundColor White
Write-Host ""

Write-Host "Prochaines étapes :" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Ouvrez le fichier config.ini.xml :" -ForegroundColor Cyan
Write-Host "   $configIniPath" -ForegroundColor White
Write-Host "   Ajoutez le contenu de config.ini.paypal.txt" -ForegroundColor White
Write-Host ""

Write-Host "2. Ouvrez le fichier appsettings.json :" -ForegroundColor Cyan
Write-Host "   $appsettingsPath" -ForegroundColor White
Write-Host "   Remplacez ou fusionnez avec le contenu de appsettings.paypal.json" -ForegroundColor White
Write-Host ""

Write-Host "3. Configurez PayPal Developer Dashboard :" -ForegroundColor Cyan
Write-Host "   - Allez sur https://developer.paypal.com/dashboard/" -ForegroundColor White
Write-Host "   - Sélectionnez votre application" -ForegroundColor White
Write-Host "   - Dans 'Return URL', ajoutez : $paypalReturnUrl" -ForegroundColor White
Write-Host "   - Activez 'Log In with PayPal'" -ForegroundColor White
Write-Host "   - Sélectionnez les scopes : openid, email, profile, address" -ForegroundColor White
Write-Host "   - Cliquez sur 'Save'" -ForegroundColor White
Write-Host ""

Write-Host "4. Testez l'authentification :" -ForegroundColor Cyan
Write-Host "   - Accédez à https://$domain/customer/login.aspx?idstructure=$structureId" -ForegroundColor White
Write-Host "   - Cliquez sur 'Se connecter avec PayPal'" -ForegroundColor White
Write-Host "   - Connectez-vous avec un compte de test Sandbox" -ForegroundColor White
Write-Host ""

Write-Host "Pour plus d'informations, consultez :" -ForegroundColor Yellow
Write-Host "  - README_PAYPAL_AUTHENTICATION.md" -ForegroundColor White
Write-Host "  - QUICKSTART_PAYPAL.md" -ForegroundColor White
Write-Host "  - TROUBLESHOOTING_PAYPAL.md" -ForegroundColor White
Write-Host ""

Write-Host "Identifiants PayPal Sandbox :" -ForegroundColor Yellow
Write-Host "  Client ID : $clientId" -ForegroundColor White
Write-Host "  Secret : $clientSecret" -ForegroundColor White
Write-Host ""

Write-Host "ATTENTION : Ces identifiants sont pour le Sandbox (test) uniquement !" -ForegroundColor Red
Write-Host "Pour la production, créez une nouvelle application PayPal." -ForegroundColor Red
Write-Host ""

Write-Host "Configuration terminée avec succès !" -ForegroundColor Green

