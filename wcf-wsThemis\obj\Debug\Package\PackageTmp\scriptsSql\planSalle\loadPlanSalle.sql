﻿SELECT  seance_id, e.entree_id,
 rlp.siege as seat, rlp.rang as row, rlp.orientation, rlp.type_siege, pos_x, pos_y 
 , decal_x, decal_y, orientation, 
  z.zone_id, et.etage_id,sect.section_id,
  cat.categ_id,
  CASE WHEN
   entree_etat='L' AND (flag_selection is null or flag_selection='')
   AND e.contingent_id=0 AND e.alotissement_id=0
   THEN 'O' ELSE 'N' END as Is<PERSON>ree,
 'N'  as Is<PERSON><PERSON>
	,rlp.iindex
	,reserve_id
	,rlp.denomination_id, rlp.bordure
	FROM entree_[eventID] e
	INNER JOIN reference_lieu_physique rlp ON rlp.ref_uniq_phy_id = e.reference_unique_physique_id
	INNER JOIN zone z ON z.zone_id = rlp.zone_id 
	INNER JOIN etage et ON et.etage_id = rlp.etage_id 
	INNER JOIN section sect ON sect.section_id = rlp.section_id
	INNER JOIN categorie cat ON cat.categ_id=e.categorie_id
	 WHERE entree_etat<>'X' and entree_etat<>'I' 
	  AND e.seance_id=[sessionID]
