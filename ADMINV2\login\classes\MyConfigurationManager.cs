﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Configuration;
using System.Threading;
using System.Globalization;
using System.Security.Cryptography;
using utilitaires2010;
using System.Text;

namespace login.classes
{
    public class MyConfigurationManager
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static string AppSettings(string key)
        {
            string sStructureID = (string)(System.Web.HttpContext.Current.Session["StructureID"]);

            if (ConfigurationManager.AppSettings[sStructureID + key] != null)
                return ConfigurationManager.AppSettings[sStructureID + key];
            else
            {
                if (ConfigurationManager.AppSettings[key] == null)
                {
                    log.Error(" la clé " + key + " n'existe pas dans le web.config");

                    throw new Exception("key " + key + " not exists in AppSettings");
                }
                else
                    return ConfigurationManager.AppSettings[key];
            }

        }

        public static string GetUserLanguage()
        {
            string userLanguage = "en";
            if (System.Web.HttpContext.Current.Cache["langueInAdmin"] != null)
            {
                userLanguage = System.Web.HttpContext.Current.Cache["langueInAdmin"].ToString();
            }

            if (System.Web.HttpContext.Current.Request.QueryString["lang"] != null)
            {
                
                Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(System.Web.HttpContext.Current.Request.QueryString["lang"]);
                userLanguage = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;

            }else if (HttpContext.Current.Request.UserLanguages != null)
            {
                Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(HttpContext.Current.Request.UserLanguages[0]);
                userLanguage = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;
            }

            return userLanguage;
        }


        public static void SetUserLanguage()
        {
            string userLanguage = "en";
            if (System.Web.HttpContext.Current.Cache["langueInAdmin"] == null)
            {
                
                if (System.Web.HttpContext.Current.Request.QueryString["lang"] != null)
                {
                    Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(System.Web.HttpContext.Current.Request.QueryString["lang"]);
                    userLanguage = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;

                }
                else
                {
                    if (HttpContext.Current.Request.UserLanguages != null)
                    {
                        Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(HttpContext.Current.Request.UserLanguages[0]);
                        userLanguage = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;
                    }
                }
                System.Globalization.CultureInfo cinfo = System.Globalization.CultureInfo.CreateSpecificCulture(userLanguage);
                System.Threading.Thread.CurrentThread.CurrentCulture = cinfo;
                System.Threading.Thread.CurrentThread.CurrentUICulture = cinfo;

                double delaiCache = 1800; // 30 mns
                if (delaiCache != 0)
                {
                    System.Web.HttpContext.Current.Cache.Insert("langueInAdmin", userLanguage, null, DateTime.Now.AddSeconds(delaiCache), TimeSpan.Zero);
                }
            }
           
        }


        public static string EncryptPassword(string _password)
        {

            if (!string.IsNullOrEmpty(_password))
            {

                RijndaelManaged myRijndael = new RijndaelManaged();
                myRijndael.Key = Encoding.UTF8.GetBytes("01E23Z4S1ERR4125SD52ZE102ZD51AZ4");
                myRijndael.IV = new byte[] { 0xf, 0x6f, 0x13, 0x2e, 0x35, 0xc2, 0xcd, 0xf9, 0x5, 0x46, 0x9c, 0xea, 0xa8, 0x4b, 0x73, 0xcc };
                byte[] encrypted = DBFunctions.encryptStringToBytes_AES(_password, myRijndael.Key, myRijndael.IV);

                return Convert.ToBase64String(encrypted);
            }

            return string.Empty;
        }

        // private const string _salt = "P&0myWHq";

        public static string CalculateHashedPassword(string clearpwd)
        {
            using (var sha = SHA256.Create())
            {
                var computedHash = sha.ComputeHash(Encoding.Unicode.GetBytes(clearpwd + MyConfigurationManager.AppSettings("CryptoKeyPA")));
                return Convert.ToBase64String(computedHash);
            }
        }

        public static bool TestEmail(string mail)
        {
            if (System.Text.RegularExpressions.Regex.IsMatch(mail,
                                                   "^([0-9a-zA-Z]([-.\\w]*[0-9a-zA-Z])*@([0-9a-zA-Z][-\\w]*[0-9a-zA-Z]\\.)+[a-zA-Z]{2,9})$") && mail.Contains("@"))
                return true;
            else return false;

        }
    }
}