﻿
update entree_[eventID] set flag_selection='' WHERE entree_id in ([seatsID]) and seance_id =[sessionID] and entree_etat='L' and (flag_selection='[flagCode]' or flag_selection='T[userID]')
select * FROM entree_[eventID] WHERE entree_id in ([seatsID]) and seance_id =[sessionID] and entree_etat='L' and flag_selection=''

 --sp_ws_autoplaces 'entree_[eventID]', [sessionID], [categID], '[listReservesID]', [nbrToFlag], 'T[userID]', 0,0,0,0,0