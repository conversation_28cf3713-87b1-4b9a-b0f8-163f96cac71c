﻿using App_Code;
using customerArea.App_Code;
using Ionic.Zip;
using RodWebShop.App_Code;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using utilitaires2010;
using utilitaires2010.sql.sqlserver;
using ws_bll;
using ws_bll.WT;
using ws_DTO;
using ws_DTO.objets_liaisons;

namespace customerArea
{

    public partial class Histo : basePage
    {

        /* [WebMethod]
         [System.Web.Script.Services.ScriptMethod]
         public static CommandeEntity GetListCommandesById(int commande_id)
         {


             logger.Debug("GetListCommandesById...");

             try
             {

                 if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
                 {
                     int myIdentiteId = int.Parse((string)System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                     int IdStructure = int.Parse((string)System.Web.HttpContext.Current.Session["IdStructure"]);
                     // TODO
                     int LangId = 0;

                     List<CommandeEntity> lstCommandes = GetCommandeList(IdStructure.ToString("0000"), myIdentiteId, LangId);
                     CommandeEntity command = lstCommandes.Where(c => c.Commande_id == commande_id).FirstOrDefault();

                     return command;
                 }
                 else
                 {
                     Exception ex = new Exception("non authentifié");
                     throw ex;
                 }
             }
             catch (Exception ex)
             {
                 logger.Error("dans GetListCommandesById:" + ex.Message + " " + ex.StackTrace);
                 throw ex;
             }


         }*/

        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string infoDV(int commande_id, string entree_id, int event_id, int seance_id, string hash)
        {
            int structureId = 0;
            structureId = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());

            try
            {
                log.Debug(structureId, "infoDV(" + commande_id + "," + entree_id + "," + event_id + "," + seance_id + "," + hash + ")");

                string myIdentiteId = System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString();
                //int myNbrDepotsMax = 100;

                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
                {



                    if (dvPossible(structureId))
                    {

                        // TODO check Hash
                        LigneCommandeEntity lcmde = new LigneCommandeEntity();

                        string newHash = lcmde.ComputeHash(int.Parse(entree_id), seance_id, event_id);
                        if (hash != newHash)
                        {
                            return "hash invalid";
                        }
                        else
                        {
                            wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                            //DepotVenteEntity dve = new DepotVenteEntity()
                            ws_DTO.DepotVenteEntity dvE = wsThemis.DepotVente_GetMontantReprise(structureId, int.Parse(myIdentiteId), commande_id, event_id, entree_id);
                            //bool ok = ws.PartialCancelOrder(structureId, int.Parse(myIdentiteId), commande_id, event_id, entree_id, 0 , myNbrDepotsMax, 0, 0);
                            // bool ok = ws.DepotVente_put(structureId, int.Parse(myIdentiteId), commande_id, event_id, entree_id, 0, myNbrDepotsMax);

                            return Newtonsoft.Json.JsonConvert.SerializeObject(dvE);

                            //return montantReprise.ToString();

                        }
                    }
                    else
                    {
                        GestionTraceManager.WriteLogError(structureId, "appel de infoDV alors que le parametrage n'est pas fait ?!");
                        log.Error(structureId, "appel de infoDV alors que le parametrage n'est pas fait ?!");
                        return "ko";
                    }
                }
                else
                {
                    return "session lost";
                }
            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(structureId, "dans doDV:" + ex.Message + " :" + ex.StackTrace);
                log.Error(structureId, "dans doDV:" + ex.Message + " :" + ex.StackTrace);
                throw ex;
            }

        }



        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string doDV(int commande_id, string entree_id, int event_id, int seance_id, int amount, string hash)
        {
            int structureId = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());
            try
            {
                log.Debug(structureId, "doDV(" + commande_id + "," + entree_id + "," + event_id + "," + seance_id + "," + amount + "," + hash + ")");

                int myNbrDepotsMax = 100;

                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
                {
                    string myIdentiteId = System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString();


                    GestionTraceManager.WriteLog(structureId, "doDV(" + commande_id + "," + entree_id + "," + event_id + "," + seance_id + "," + hash);

                    if (dvPossible(structureId))
                    {
                        log.Debug(structureId, "doDV possible");


                        // TODO check Hash
                        LigneCommandeEntity lcmde = new LigneCommandeEntity();

                        string newHash = lcmde.ComputeHash(int.Parse(entree_id), seance_id, event_id);
                        if (hash != newHash)
                        {
                            return "hash invalid";
                        }
                        else
                        {
                            wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                            //bool ok = ws.PartialCancelOrder(structureId, int.Parse(myIdentiteId), commande_id, event_id, entree_id, 0 , myNbrDepotsMax, 0, 0);


                            MyDictionary mySSC = new MyDictionary();
                            mySSC = mySSC.GetDictionaryFromCache(structureId);

                            int operatorId = int.Parse(mySSC["PAIEMENTWEBOPERATORID"]);

                            //logger.Debug("operatorId :" + operatorId);

                            ws_DTO.DepotVenteEntity dve = wsThemis.DepotVente_put(structureId, int.Parse(myIdentiteId), commande_id, event_id, entree_id, operatorId, amount, myNbrDepotsMax);
                            //wsOpen.par
                            if (dve.id != 0)
                            {
                                log.Debug(structureId, "depot vente id :" + dve.id);
                                GestionTraceManager.WriteLog(structureId, "depot vente id :" + dve.id);
                                // send email
                                //int identiteId = int.Parse(dsIdent.Tables[0].Rows[0]["identityid"].ToString());
                                eMail em = new eMail();

                                //int iIdstructure = int.Parse(structureId);
                                IdentiteEntity ident = LoginType.GetTypeLogin(structureId).GetLoginTable(structureId.ToString("0000"), myIdentiteId);

                                /*
                                  * 
                                  * //a voir si on le fait
                                if (ident != null)
                                {
                                    // plusieures identite avec même email, on prend la premiere identite trouvée
                                   for (int i = dsIdent.Tables[0].Rows.Count - 1; i > 0; i--)
                                    {
                                        dsIdent.Tables[0].Rows[i].Delete();
                                        dsIdent.Tables[0].AcceptChanges();
                                    }
                                }
                                */
                                if (ident != null)
                                {

                                    //em.SetTemplate("MailDemandeDepotVente");


                                    List<ws_DTO.DepotVenteEntity> listDv = new List<ws_DTO.DepotVenteEntity>
                                    {
                                        dve
                                    };
                                    em.SendEmailDVDemande(ident, event_id, "MailDemandeDepotVente", App_Code.Initialisations.GetMessageTranslate("subject_depot_vente_email"), listDv);

                                    log.Debug(structureId, "Send email identite :" + int.Parse(myIdentiteId));


                                    var logsLevel = customerArea.App_Code.Initialisations.GetKeyAppSettings("logsLevel");

                                    if (logsLevel != utilitaires2010.LogLevel.NORMAL.ToString())
                                    {
                                        GestionTraceManager.WriteLog(structureId, "Send email identite :" + int.Parse(myIdentiteId));

                                    }

                                    return "ok";
                                }
                                else
                                {
                                    log.Error(structureId, "doDV - dsIdent est null");
                                    GestionTraceManager.WriteLogError(structureId, "doDV - dsIdent est null");
                                    return "ko";
                                }
                            }
                            else
                            {
                                log.Error(structureId, "doDV - dve.id à 0");
                                GestionTraceManager.WriteLogError(structureId, "doDV - dve.id à 0");
                                return "ko";
                            }
                        }
                    }
                    else
                    {

                        log.Error(structureId, "appel de doDV alors que le parametrage n'est pas fait ?!%");
                        return "ko";
                    }
                }
                else
                {
                    return "session lost";
                }
            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(structureId, "dans doDV:" + ex.Message + " :" + ex.StackTrace);
                log.Error(structureId, "dans doDV:" + ex.Message + " :" + ex.StackTrace);
                throw ex;
            }

        }


        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string linkEntreeConsommateur(List<LinkConsumerToSeat> listlinkconsumertoseat)
        {
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
            {
                //logger.Debug("linkEntreeConsommateur(" + _commande_id + "," + _entree_id + "," + _event_id + "," + _seance_id + "," + _hash + "," + _name + "," + _surname + "," + _surname + "," + _phone);

                int structureId = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());
                int identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());

                wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();


                /*

                  ConsommateurEntity cons = new ConsommateurEntity()
                {
                    Name = _name,
                    Surname = _surname,
                    phone = _phone,
                    email = _email
                };
                List<Entree> listEntree = new List<Entree>();
                Entree en = new Entree()
                {
                    seance_id = Convert.ToInt64(_seance_id),
                    entree_id = Convert.ToInt64(_entree_id),
                    manif_id = Convert.ToInt64(_event_id)
                };


                
                listEntree.Add(en);

                */



                MyDictionary mySSC = new MyDictionary();
                mySSC = mySSC.GetDictionaryFromCache(structureId);

                int numPostalTelEmail = int.Parse(mySSC["VARIABLESEMAIL"].ToString());
                int numPostalTelTelephone = int.Parse(mySSC["VARIABLESTELEPHONE"].ToString());


                log.Debug(structureId, "listlinkconsumertoseat " + string.Join(",", listlinkconsumertoseat));
                Commons common = new Commons();
                foreach (LinkConsumerToSeat item in listlinkconsumertoseat)
                {

                    List<Entree> listEntree = new List<Entree>();
                    Entree en = new Entree()
                    {
                        seance_id = Convert.ToInt64(item.SeanceId),
                        entree_id = Convert.ToInt64(item.EntreeId),
                        manif_id = Convert.ToInt64(item.EventId),
                        commande_id = item.CommandeId
                    };


                    IdentiteEntity thisidentite = wsThemis.GetCustomerProfileOfEmailOrIdentiteId(
                       structureId.ToString("0000"),
                       string.Empty,
                       string.Empty,
                       item.IdentiteId.ToString(),
                       numPostalTelEmail,
                       "N"
                    );

                    log.Debug(structureId, "item IdentiteId " + item.IdentiteId);



                    if (thisidentite != null)
                    {
                        ConsommateurEntity conso = new ConsommateurEntity()
                        {
                            Consumer_id = thisidentite.Identite_id,
                            Name = thisidentite.FirstName,
                            Surname = thisidentite.SurName,
                            //phone = thisidentite.PhoneNumber + numPostalTelEmail,
                            email = thisidentite.Email
                        };
                        log.Debug(structureId, "conso " + conso.Consumer_id);


                        switch (numPostalTelTelephone)
                        {
                            case 1:
                                conso.phone = thisidentite.PhoneNumber1;
                                break;

                            case 2:
                                conso.phone = thisidentite.PhoneNumber2;
                                break;

                            case 3:
                                conso.phone = thisidentite.PhoneNumber3;
                                break;
                            case 4:
                                conso.phone = thisidentite.PhoneNumber4;
                                break;
                            case 5:
                                conso.phone = thisidentite.PhoneNumber5;
                                break;
                            case 6:
                                conso.phone = thisidentite.PhoneNumber6;
                                break;
                            case 7:
                                conso.phone = thisidentite.PhoneNumber7;
                                break;

                            default:
                                break;
                        }
                        en.consommateur = conso;

                        log.Debug(structureId, "conso " + conso.phone);



                        listEntree.Add(en);

                        //   IdentiteEntity thisidentite = wsThemis.GetCustomerProfileOfEmailOrIdentiteId(structureId.ToString("0000"), "", "", item.IdentiteId.ToString(), numPostalTelEmail);
                        //  common.LinkConsumer(structureId, item.EventId, thisidentite, true);
                        try
                        {
                            int newconso = wsThemis.MarquerConsommateur(structureId.ToString(), listEntree.ToArray(), identiteId);
                        }
                        catch (Exception ex)
                        {
                            log.Error(structureId, "erreur linkEntreeConsommateur : " + ex.Message);
                            throw ex;
                        }

                    }
                    else
                    {
                        log.Error(structureId, "identite n'existe pas");
                        return "danger:consumer_not_exist";
                    }
                }

                // wsThemis.MarquerConsommateur(structureId.ToString(), listEntree.ToArray(), identiteId);
            }

            return "ok";
        }



        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string linkEntreeConsommateurOld(string _commande_id, string _entree_id, string _event_id, string _seance_id, string _hash, string _name, string _surname, string _email, string _phone)
        {
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
            {

                int structureId = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());
                int identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());

                log.Debug(structureId, "linkEntreeConsommateur(" + _commande_id + "," + _entree_id + "," + _event_id + "," + _seance_id + "," + _hash + "," + _name + "," + _surname + "," + _surname + "," + _phone);

                wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                ConsommateurEntity cons = new ConsommateurEntity()
                {
                    Name = _name,
                    Surname = _surname,
                    phone = _phone,
                    email = _email
                };
                List<Entree> listEntree = new List<Entree>();
                Entree en = new Entree()
                {
                    seance_id = Convert.ToInt64(_seance_id),
                    entree_id = Convert.ToInt64(_entree_id),
                    manif_id = Convert.ToInt64(_event_id)
                };
                listEntree.Add(en);

                wsThemis.MarquerConsommateur(structureId.ToString(), listEntree.ToArray(), identiteId);
            }
            return "ok";
        }


        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string editionsEntrees(object[] structEntrees)
        {
            int structureId = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());

            try
            {
                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
                {


                    int identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());

                    log.Debug(structureId, "editionsEntrees " + structureId + "," + identiteId + " (" + structEntrees.Count().ToString() + ")");

                    List<Entree> listEntree = new List<Entree>();

                    foreach (object obj in structEntrees)
                    {
                        Dictionary<string, object> dentr = new Dictionary<string, object>();
                        dentr = (Dictionary<string, object>)obj;

                        int _commande_id = int.Parse(dentr["cmdid"].ToString());
                        int _seance_id = int.Parse(dentr["seanceid"].ToString());
                        int _entree_id = int.Parse(dentr["entreeid"].ToString());
                        int _event_id = int.Parse(dentr["eventid"].ToString());
                        int _dossier_id = int.Parse(dentr["dossid"].ToString());
                        int _maquette_id = int.Parse(dentr["maqid"].ToString());
                        string hash = dentr["hash"].ToString();

                        LigneCommandeEntity lcmde = new LigneCommandeEntity();
                        string newHash = lcmde.ComputeHash(_entree_id, _seance_id, _event_id);
                        if (newHash == hash)
                        {

                            Entree en = new Entree()
                            {
                                seance_id = Convert.ToInt64(_seance_id),
                                entree_id = Convert.ToInt64(_entree_id),
                                manif_id = Convert.ToInt64(_event_id),
                                commande_id = _commande_id,
                                dossier_id = _dossier_id,
                                maquette_id = _maquette_id
                            };

                            if (!listEntree.Any(e => e.seance_id == en.seance_id && e.entree_id == en.entree_id && e.commande_id == en.commande_id))
                            {
                                listEntree.Add(en);
                            }

                        }

                    }
                    string typeConn = utilitaires2010.Initialisations.GetKeyAppSettings("TypeRun");
                    SqlServerConnexion cnx = DBFunctions.ConnectOpen(structureId.ToString(), typeConn);


                    bool isAlreadyEditing = RecetteManager.IsAlreadyEditing(structureId, typeConn, cnx, listEntree);


                    string pathPdf = System.Configuration.ConfigurationManager.AppSettings["redoPdf"].Replace("[idstructure]", structureId.ToString("0000")).Replace("[idstructureSur4zeros]", structureId.ToString("0000"));

                    wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();


                    MyDictionary mySSC = new MyDictionary();
                    mySSC = mySSC.GetDictionaryFromCache(structureId);

                    int operateurId = int.Parse(mySSC["PAIEMENTWEBOPERATORID"]);


                    if (!isAlreadyEditing && wsThemis.EditerCommande(structureId.ToString(), listEntree.ToArray(), identiteId, operateurId))
                    {

                        var entries = EntreeManager.GetEntriesWithInsurance(typeConn, cnx, structureId, listEntree);
                        var entriesWhithnsurance = entries.Where(e => e.controleacces == "9");
                        string insranceCompany = StructrurePrefsManager.GetValueByPreferenceKey(structureId, typeConn, cnx, "INSURANCE_COMPAGNY");

                        if (!string.IsNullOrEmpty(insranceCompany))
                        {
                            RecetteManager.UpdateBilletForInsurance(structureId, typeConn, cnx, entriesWhithnsurance, insranceCompany);
                        }

                        isAlreadyEditing = true;

                    }
                    else
                    {
                        log.Error(structureId, "wcfO.EditerCommande retourne false !");
                    }

                    if (isAlreadyEditing)
                    {

                        string fileName = doPdfCpl(listEntree[0].commande_id.ToString(), structEntrees);

                        log.Debug(structureId, "editionsEntrees " + structureId + "," + identiteId + " (" + structEntrees.Count().ToString() + ") fileName=" + fileName);

                        return fileName;
                    }


                }

            }
            catch (Exception ex)
            {
                log.Error(structureId, "dans editionsEntrees:" + ex.StackTrace);
            }
            return "ok";
        }


        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string linkEntreesConsommateur(object[] structEntrees)
        {
            int structureId = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());

            try
            {
                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
                {
                    log.Debug(structureId, "linkEntreeConsommateur(" + structEntrees.Count() + ")");

                    int identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());

                    wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                    // customerArea.wcf_WebOpen.Iwcf_wsOpenClient wcfO = new customerArea.wcf_WebOpen.Iwcf_wsOpenClient();

                    LigneCommandeEntity lcmde = new LigneCommandeEntity();

                    List<Entree> listEntree = new List<Entree>();

                    foreach (object obj in structEntrees)
                    {
                        Dictionary<string, object> dentr = new Dictionary<string, object>();
                        dentr = (Dictionary<string, object>)obj;

                        int _commande_id = int.Parse(dentr["cmdid"].ToString());
                        int _seance_id = int.Parse(dentr["seanceid"].ToString());
                        int _entree_id = int.Parse(dentr["entreeid"].ToString());
                        int _event_id = int.Parse(dentr["eventid"].ToString());
                        string hash = dentr["hash"].ToString();

                        string newHash = lcmde.ComputeHash(_entree_id, _seance_id, _event_id);

                        string nameC = "";
                        string surnameC = "";
                        string emailC = "";
                        string phoneC = "";

                        if (newHash == hash)
                        {
                            if (dentr["namec"] != null)
                            {
                                nameC = dentr["namec"].ToString();
                            }

                            if (dentr["surnamec"] != null)
                            {
                                surnameC = dentr["surnamec"].ToString();
                            }

                            if (dentr["emailc"] != null)
                            {
                                emailC = dentr["emailc"].ToString();
                            }

                            if (dentr.Keys.Contains("phonec"))
                            {
                                phoneC = dentr["phonec"].ToString();
                            }

                            //   stodo += _entree_id.ToString() + ":" + _seance_id.ToString() + ",";

                            Entree en = new Entree()
                            {
                                seance_id = Convert.ToInt64(_seance_id),
                                entree_id = Convert.ToInt64(_entree_id),
                                manif_id = Convert.ToInt64(_event_id),
                                commande_id = _commande_id
                            };

                            //ConsommateurEntity consommateur = new ConsommateurEntity();
                            en.consommateur = new ConsommateurEntity
                            {
                                Name = nameC,
                                Surname = surnameC,
                                email = emailC,
                                phone = phoneC
                            };

                            listEntree.Add(en);
                        }
                        else
                        {
                            log.Error(structureId, "hash invalide !?!: recu=" + hash + ", calcul=" + newHash + " pour " + _entree_id + "," + _seance_id + "," + _event_id);

                        }
                    }

                    wsThemis.MarquerConsommateur(structureId.ToString(), listEntree.ToArray(), identiteId);
                }
            }
            catch (Exception ex)
            {
                log.Error(structureId, "dans linkEntreesConsommateur:" + ex.StackTrace);
            }
            return "ok";

        }




        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string doPdfMike(string commande_id, string entree_id, string event_id, string seance_id, int dossier_id, string hash)
        {
            int structureId = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());

            try
            {

                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
                {
                    int identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());

                    log.Debug(structureId, "doPdf(" + commande_id + "," + entree_id + "," + event_id + "," + seance_id + "," + hash);

                    // TODO check Hash
                    LigneCommandeEntity lcmde = new LigneCommandeEntity();

                    string newHash = lcmde.ComputeHash(int.Parse(entree_id), int.Parse(seance_id), int.Parse(event_id));
                    if (hash != newHash)
                    {
                        log.Error(structureId, "hash invalide !?!: recu=" + hash + ", calcul=" + newHash + " pour " + entree_id + "," + seance_id + "," + event_id);
                        return "hash invalid";
                    }
                    else
                    {

                        string completepathPdf = GetPdfName(structureId, int.Parse(commande_id), identiteId, int.Parse(seance_id), dossier_id, "consomm");

                        ZipFile zipFile = new ZipFile();

                        foreach (string oknamepdf in completepathPdf.Split('|'))
                        {
                            string namepdf = oknamepdf.Replace("ok:", "");
                            string pathPdf = System.Configuration.ConfigurationManager.AppSettings["redoPdf"] + "\\" + namepdf;
                            pathPdf = pathPdf.Replace("[idstructure]", structureId.ToString("0000")).Replace("[idstructureSur4zeros]", structureId.ToString("0000"));


                            zipFile.AddFile(pathPdf);

                        }
                        string zipNamepath = "pdf_" + structureId + "_" + identiteId + ".zip";



                        string strDirectory = HttpContext.Current.Server.MapPath("~/tempfiles/");


                        zipFile.Save(strDirectory + zipNamepath);

                        HttpContext.Current.Response.ClearContent();
                        HttpContext.Current.Response.ClearHeaders();


                        HttpContext.Current.Response.ContentType = "application/octet-stream";
                        HttpContext.Current.Response.AppendHeader("Content-Disposition", "attachment; filename=file.zip");
                        HttpContext.Current.Response.TransmitFile(strDirectory + zipNamepath);
                        HttpContext.Current.Response.Flush();
                        HttpContext.Current.Response.End();


                        //Set zip file name  
                        // HttpContext.Current.Response.AppendHeader("content-disposition", "attachment; filename=file.zip");
                        // zipFile.CompressionMethod = CompressionMethod.BZip2;
                        // zipFile.CompressionLevel = Ionic.Zlib.CompressionLevel.BestCompression;

                        //HttpContext.Current.Response.TransmitFile("TemplatedDocuments.zip");
                        //Save the zip content in output stream  
                        //zipFile.Save(HttpContext.Current.Response.OutputStream);

                        //HttpContext.Current.Response.Close();

                        return "";
                    }


                }
                else
                {
                    return "session lost";

                }
            }
            catch (Exception ex)
            {
                log.Error(structureId, "dans doPdf:" + ex.Message + " :" + ex.StackTrace);
                throw ex;
            }

        }

        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string doPdf(string commande_id, string entree_id, string event_id, string seance_id, int dossier_id, string hash)
        {
            int structureId = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());

            try
            {

                log.Debug(structureId, "doPdf(" + commande_id + "," + entree_id + "," + event_id + "," + seance_id + "," + hash);
                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
                {


                    int identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());


                    // TODO check Hash
                    LigneCommandeEntity lcmde = new LigneCommandeEntity();

                    string newHash = lcmde.ComputeHash(int.Parse(entree_id), int.Parse(seance_id), int.Parse(event_id));
                    if (hash != newHash)
                    {
                        log.Error(structureId, "hash invalide !?!: recu=" + hash + ", calcul=" + newHash + " pour " + entree_id + "," + seance_id + "," + event_id);
                        return "hash invalid";
                    }
                    else
                    {

                        /* wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                         //customerArea.wsOpen.wsThemisOpen wsO = new customerArea.wsOpen.wsThemisOpen();
                         //   wsO.test_DepotVente_inform(0, true, 0, true, 0,true, 0, true, "11");

                         string langIso = "en";
                         if (System.Web.HttpContext.Current.Session["SVarLangue"] != null)
                         {
                             // langIso = System.Web.HttpContext.Current.Session["SVarLangue"].ToString();
                             langIso = System.Web.HttpContext.Current.Session["language_id"].ToString();
                         }


                         MultiLangue.GetLanguage();
                         MyDictionary mySSC = new MyDictionary();
                         mySSC = mySSC.GetDictionaryFromCache(structureId);


                         string filiere_id = "";
                         if (mySSC.Contains("CREATEPROFILWEBFILIEREID"))
                             filiere_id = mySSC["CREATEPROFILWEBFILIEREID"];
                         {
                             int iFiliereId = 0;
                             if (int.TryParse(filiere_id, out iFiliereId))
                             {
                                 return filiere_id;
                             }
                             else
                             {
                                 exceptionConfigIni e = new exceptionConfigIni("CREATEPROFILWEBFILIEREID incorrect dans le config.ini (" + filiere_id + ")");
                                 throw e;
                             }
                         }


                         int operateurId = int.Parse(mySSC["PAIEMENTWEBOPERATORID"]);

                         string pathPdf = System.Configuration.ConfigurationManager.AppSettings["redoPdf"].Replace("[idstructure]", structureId.ToString("0000")).Replace("[idstructureSur4zeros]", structureId.ToString("0000"));
                         //string completepathPdf = wsO.TAH_MakePDF_FromCmd(structureId, int.Parse(commande_id), entree_id + ":" + seance_id.ToString(), pathPdf, "test consom", langIso);

                         List<string> completepathPdf = wcfThemis.TAH_MakePDF_FromCmd(structureId, int.Parse(commande_id), int.Parse(filiere_id), operateurId, pathPdf, "test consom", langIso).ToList();

                         */

                        string completepathPdf = GetPdfName(structureId, int.Parse(commande_id), identiteId, int.Parse(seance_id), dossier_id, "consomm");


                        return completepathPdf;


                        foreach (string oknamepdf in completepathPdf.Split('|'))
                        {
                            string namepdf = oknamepdf.Replace("ok:", "");
                            string pathPdf = System.Configuration.ConfigurationManager.AppSettings["redoPdf"] + "\\" + namepdf;
                            pathPdf = pathPdf.Replace("[idstructure]", structureId.ToString("0000")).Replace("[idstructureSur4zeros]", structureId.ToString("0000"));





                            string zipNamepath = "pdf_" + structureId + ".zip";
                            // string zipNamepath = "pdf_" + structureId + "_" + idIdentite + extensionArchiveAttachments;
                            // ZIP ALL FILES IN THE FOLDER.
                            /* using (ZipFile zip = new ZipFile())
                             {

                                 zip.AddFiles(pathPdf);
                                 // zip.Save(zipNamepath);  // SAVE THE ZIP FILE.
                             }*/







                            /*

                              using (FileStream fs = File.OpenRead(pathPdf))
                              {



                                  int length = (int)fs.Length;
                                    byte[] buffer;
                                    using (BinaryReader br = new BinaryReader(fs))
                                    {
                                        buffer = br.ReadBytes(length);
                                    }


                                    HttpContext.Current.Response.Clear();
                                    HttpContext.Current.Response.Buffer = true;
                                    HttpContext.Current.Response.AddHeader("content-disposition", String.Format("attachment;filename={0}", Path.GetFileName(pathPdf)));
                                    HttpContext.Current.Response.ContentType = "application/" + Path.GetExtension(pathPdf).Substring(1);
                                    HttpContext.Current.Response.BinaryWrite(buffer);
                                    HttpContext.Current.Response.End();
                               
                            } */

                        }



                        ZipFile zipFile = new ZipFile();

                        HttpContext.Current.Response.ClearContent();
                        HttpContext.Current.Response.ClearHeaders();
                        //Set zip file name  
                        HttpContext.Current.Response.AppendHeader("content-disposition", "attachment; filename=TemplatedDocuments.zip");
                        zipFile.CompressionMethod = CompressionMethod.BZip2;
                        zipFile.CompressionLevel = Ionic.Zlib.CompressionLevel.BestCompression;

                        HttpContext.Current.Response.TransmitFile("TemplatedDocuments.zip");
                        //Save the zip content in output stream  
                        zipFile.Save(HttpContext.Current.Response.OutputStream);

                        HttpContext.Current.Response.Close();


                        return "";


                        // return completepathPdf;

                    }
                }
                else
                {
                    return "session lost";
                }
            }
            catch (Exception ex)
            {
                log.Error(structureId, "dans doPdf:" + ex.Message + " :" + ex.StackTrace);
                throw ex;
            }
        }


        //Pas utilisée en Js
        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string doPdfCpl(string commande_id, object[] structEntrees)
        {
            int structureId = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());

            try
            {


                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
                {
                    string stodo = "";
                    int identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                    int dossierId = 0;

                    log.Debug(structureId, "doPdfCpl(" + commande_id + "," + structEntrees.Count() + " entrees)");

                    // TODO check Hash
                    LigneCommandeEntity lcmde = new LigneCommandeEntity();

                    foreach (object obj in structEntrees)
                    {
                        Dictionary<string, object> dentr = new Dictionary<string, object>();
                        dentr = (Dictionary<string, object>)obj;

                        int seance_id = int.Parse(dentr["seanceid"].ToString());
                        int entree_id = int.Parse(dentr["entreeid"].ToString());
                        int event_id = int.Parse(dentr["eventid"].ToString());
                        dossierId = int.Parse(dentr["dossid"].ToString());
                        string hash = dentr["hash"].ToString();

                        string newHash = lcmde.ComputeHash(entree_id, seance_id, event_id);
                        if (newHash == hash)
                        {
                            stodo += entree_id.ToString() + ":" + seance_id.ToString() + ",";
                        }
                        else
                        {
                            log.Error(structureId, "hash invalide !?!: recu=" + hash + ", calcul=" + newHash + " pour " + entree_id + "," + seance_id + "," + event_id);
                        }
                    }
                    if (stodo.EndsWith(","))
                    {
                        stodo = stodo.Remove(stodo.LastIndexOf(","));
                    }

                    /*  string langIso = "en";
                      if (System.Web.HttpContext.Current.Session["SVarLangue"] != null)
                          langIso = System.Web.HttpContext.Current.Session["SVarLangue"].ToString();

                      string pathPdf = System.Configuration.ConfigurationManager.AppSettings["redoPdf"].Replace("[idstructure]", structureId.ToString("0000"));

                      customerArea.wsOpen.wsThemisOpen wsO = new customerArea.wsOpen.wsThemisOpen();
                      string completepathPdf = wsO.TAH_MakePDF_FromCmd(structureId, int.Parse(commande_id), stodo, pathPdf, "", langIso);
                      */

                    string completepathPdf = GetPdfName(structureId, int.Parse(commande_id), identiteId, 0, dossierId, "");
                    if (completepathPdf.StartsWith("ok:"))
                    {

                        completepathPdf = completepathPdf.Replace("ok:", "");
                    }

                    //FileInfo fileInf = new FileInfo(completepathPdf);
                    return completepathPdf;

                    // return completepathPdf;
                }
                else
                {
                    return "session lost";
                }
            }
            catch (Exception ex)
            {
                log.Error(structureId, "dans doPdfCpl:" + ex.Message + " :" + ex.StackTrace);
                throw ex;
            }
        }


        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string GetListCommandes()
        {

            int IdStructure = (int)System.Web.HttpContext.Current.Session["IdStructure"];

            try
            {

                if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
                {
                    int myIdentiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                    log.Debug(IdStructure, "GetListCommandes...");

                    // TODO
                    int LangId = 0;

                    List<CommandeEntity> listCmds = new List<CommandeEntity>();

                    string strLangue = App_Code.Initialisations.GetUserLanguage();
                    /*  string strLangue = (string)System.Web.HttpContext.Current.Session["SVarLangue"];
                    Thread.CurrentThread.CurrentUICulture = new CultureInfo(strLangue);
                     Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(strLangue);*/

                    listCmds = GetCommandeList(IdStructure, myIdentiteId, LangId);

                    //return listCmds;



                    return Newtonsoft.Json.JsonConvert.SerializeObject(listCmds);
                    //return json



                    //return "testing";
                }
                else
                {
                    Exception ex = new Exception("non authentifié");
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                log.Error(IdStructure, "dans GetListCommandes:" + ex.Message + " " + ex.StackTrace);
                throw ex;
            }

        }

        /// <summary>
        /// liste des commandes effectuées 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="_identiteId"></param>
        /// <param name="_langId"></param>
        /// <returns></returns>
        private static List<CommandeEntity> GetCommandeList(int structureId, int _identiteId, int _langId)
        {

            List<CommandeEntity> commandes = HistoViewModel.GetCommandeList(structureId, _identiteId, 0, true, new List<int>(), new List<int>(), new List<string>());

            return commandes;
            /*
             * 
             *   List<CommandeEntity> listCmds = new List<CommandeEntity>();

            wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();


            MyDictionary mySSC = new MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(int.Parse(structureId));
            int numPostalTelEmail = int.Parse(mySSC["VARIABLESEMAIL"].ToString());

            DataSet myHisto = wctrlHistoUser.GetHistoriqueCommandes(structureId, _identiteId, _langId);
            if (myHisto != null && myHisto.Tables.Count > 1)
            {

                // GetListe Commandes Internet                                                          
                DataSet myHistoWT = wctrlHistoUser.GetHistoriqueCommandesInternet(structureId, _identiteId, _langId);
                myHisto.Tables[0].Columns.Add("maquette_id");

                foreach (DataRow drHistoWT in myHistoWT.Tables[0].Rows)
                {
                    DataRow[] thisRows = myHisto.Tables[0].Select("entreeid=" + drHistoWT["entree_id"] + " AND seanceid=" + drHistoWT["seance_id"]);
                    if (thisRows.Length > 0)
                    {
                        thisRows[0]["maquette_id"] = drHistoWT["maquette_id"];
                    }
                }

                foreach (DataRow drRow in myHisto.Tables[0].Rows)
                {
                    int cmdId = int.Parse(drRow["orderid"].ToString());
                    CommandeEntity cmd = listCmds.Find(delegate (CommandeEntity c) { return c.Commande_id == cmdId; });
                    bool newCmd = false;
                    if (cmd == null)
                    {
                        newCmd = true;
                        cmd = new CommandeEntity
                        {
                            Commande_id = cmdId,
                            ListSeatLines = new List<LigneCommandeEntreeEntity>(),
                            ListProductLines = new List<LigneCommandeProduitEntity>(),
                            Status = ""
                        };

                    }
                    LigneCommandeEntreeEntity ligneCmd = new LigneCommandeEntreeEntity
                    {
                        Event_name = drRow["eventname"].ToString(),
                        PriceId = int.Parse(drRow["priceid"].ToString()),
                        Price_name = drRow["pricename"].ToString()
                    };
                    if (drRow.Table.Columns.Contains("categid"))
                    {
                        ligneCmd.Category_id = int.Parse(drRow["categid"].ToString());
                    }

                    ligneCmd.Category_name = drRow["categoryname"].ToString();
                    //ligneCmd.Rank = drRow["denom"].ToString();




                    ligneCmd.Rank = drRow["rank"].ToString();
                    ligneCmd.Seat = drRow["seat"].ToString();
                    ligneCmd.Amount = int.Parse(drRow["amount"].ToString());
                    ligneCmd.Etat = drRow["etat"].ToString();
                    ligneCmd.Entree_id = int.Parse(drRow["entreeid"].ToString());
                    ligneCmd.Seance_id = int.Parse(drRow["seanceid"].ToString());

                    ligneCmd.dossier_id = int.Parse(drRow["dossierid"].ToString());

                    ligneCmd.identite_id = int.Parse(drRow["identite_id"].ToString());
                    ligneCmd.IdentiteConsumerId = int.Parse(drRow["identite_consommateur_id"].ToString());
                    ligneCmd.AboId = int.Parse(drRow["abo_id"].ToString());

                    //voir si on en a besoin vraiment
                    // ligneCmd.Consumer = wcfThemis.GetCustomerProfileOfEmailOrIdentiteId(structureId, "", "", ligneCmd.identite_id.ToString(), numPostalTelEmail); //wctrlHistoUser.GetDetailCommande
                    string languser = App_Code.Initialisations.GetUserLanguage();
                    CultureInfo culture = new CultureInfo(languser);
                    Thread.CurrentThread.CurrentCulture = culture;

                    ligneCmd.Event_id = int.Parse(drRow["eventid"].ToString());
                    //ligneCmd.Session_date = (DateTime)drRow["sessiondate"]; //seance_date_deb
                

                    if (drRow.Table.Columns.Contains("session_date_deb"))
                    {
                        ligneCmd.SessionDateDeb = (DateTime)drRow["session_date_deb"]; //seance_date_deb
                    }

                    if (drRow.Table.Columns.Contains("session_date_fin"))
                    {
                        ligneCmd.SessionDateFin = (DateTime)drRow["session_date_fin"]; //seance_date_deb
                    }


                    if (drRow.Table.Columns.Contains("isControlAccessPassed"))
                    {
                        ligneCmd.IsControlAccessPassed = Convert.ToBoolean(drRow["isControlAccessPassed"].ToString());
                    }

                    //ligneCmd.SessionDateFin = (DateTime)drRow["session_date_fin"]; //seance_date_deb
                    ligneCmd.itsaFutureSession = (ligneCmd.SessionDateDeb > DateTime.Now); 

                    ligneCmd.StrSessionDateDeb = ligneCmd.SessionDateDeb.ToString("f", CultureInfo.CurrentCulture);
                    ligneCmd.StrSessionDateFin = ligneCmd.SessionDateFin.ToString("f", CultureInfo.CurrentCulture);
                    //ligneCmd.IsControlAccessPassed = Convert.ToBoolean(drRow["isControlAccessPassed"].ToString());

                    if (drRow["depotventeid"] != DBNull.Value)
                    {
                        ligneCmd.depotVenteId = int.Parse(drRow["depotventeid"].ToString());
                    }

                    if (drRow["status_reprise"] != DBNull.Value)
                    {
                        ligneCmd.depotVenteStatus = drRow["status_reprise"].ToString();
                    }

                    if (drRow["maquette_id_rod"] != DBNull.Value && drRow["maquette_id_rod"] != "")
                    {
                        ligneCmd.Maquetteid = int.Parse(drRow["maquette_id_rod"].ToString());
                    }
                    else if (drRow["maquette_id"] != DBNull.Value && drRow["maquette_id"] != "")
                    {
                        ligneCmd.Maquetteid = int.Parse(drRow["maquette_id"].ToString());
                    }

                    cmd.ListSeatLines.Add(ligneCmd);

                    cmd.Status += ligneCmd.Etat + ",";

                    if (newCmd)
                    {
                        listCmds.Add(cmd);
                    }
                }

                foreach (DataRow drRow in myHisto.Tables[1].Rows)
                {
                    int cmdId = int.Parse(drRow["orderid"].ToString());
                    CommandeEntity cmd = listCmds.Find(delegate (CommandeEntity c) { return c.Commande_id == cmdId; });
                    bool newCmd = false;
                    if (cmd == null)
                    {
                        newCmd = true;
                        cmd = new CommandeEntity
                        {
                            Commande_id = cmdId,
                            ListSeatLines = new List<LigneCommandeEntreeEntity>(),
                            ListProductLines = new List<LigneCommandeProduitEntity>(),
                            Status = ""
                        };
                    }

                    LigneCommandeProduitEntity ligneCmd = new LigneCommandeProduitEntity
                    {
                        Product_name = drRow["productname"].ToString(),
                        ProductAmountCent = decimal.Parse(drRow["amountincent"].ToString()),
                        OrderId = int.Parse(drRow["orderid"].ToString())
                    };
                    cmd.ListProductLines.Add(ligneCmd);

                    if (newCmd)
                    {
                        cmd.Status = "Ok";
                        listCmds.Add(cmd);
                    }

                }


            }
 return listCmds;

            */


        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!UserIsAuthentified)
            {
                string pageIdentif = "Login.aspx";

                string qs = Page.Request.Url.Query.Replace("resetI=1", "wrst=1");
                string fileP = pageIdentif + qs;

                Response.Redirect(fileP, true);
            }
            else
            {

                GestionTraceManager.WriteLog(IdStructure, "page_load popHistoCustomer...", TypeLog.LoadPage);
                /*
                if (!UserIsAuthentified)
                {
                    string pageIdentif = "Login.aspx";

                    string qs = Page.Request.Url.Query.Replace("resetI=1", "wrst=1");
                    string fileLogin = pageIdentif + qs;

                    Response.Redirect(fileLogin, true);
                }
                */

                //WebPartManager1.StaticConnections.Clear();

                string fileP = Page.Request.FilePath;
                fileP = fileP.Substring(fileP.LastIndexOf('/') + 1, fileP.LastIndexOf(".") - fileP.LastIndexOf('/') - 1);

                Ihm myIhm = new Ihm();

                int resulEvent = 0;
                if (Request.QueryString["eventId"] != "" && Request.QueryString["eventId"] != null && int.TryParse(Request.QueryString["eventId"], out resulEvent))
                {
                    System.Web.HttpContext.Current.Session["eventId"] = resulEvent;
                }


                string idpa = "0";
                if (Session["ProfilAcheteurId"] != null)
                {
                    idpa = (string)Session["ProfilAcheteurId"];
                }


                int structureId = 0;
                if (Request.QueryString["idstructure"] != "" && Request.QueryString["idstructure"] != null && int.TryParse(Request.QueryString["idstructure"], out structureId))
                {
                    System.Web.HttpContext.Current.Session["idstructure"] = structureId;
                }
                else if (GetStructureId() > 0)
                {
                    structureId = GetStructureId();
                }

                string lang = customerArea.App_Code.Initialisations.GetUserLanguage();


                string plateformCode = "";
                if (Request.QueryString["plateformCode"] != "" && Request.QueryString["plateformCode"] != null)
                {
                    plateformCode = Request.QueryString["plateformCode"].ToString();
                    System.Web.HttpContext.Current.Session["plateformCode"] = Request.QueryString["plateformCode"].ToString();
                }


                List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
                        {
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
                        };


                dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, structureId, resulEvent, int.Parse(idpa), plateformCode, lang);

                if (!customerArea.App_Code.Initialisations.IsAuthorizedPage(globalPlateform, "myorders"))
                {

                    GestionTraceManager.WriteLogError(IdStructure, "L'affichage de la page Histo est false dans le fichier appsettings.json");
                    Response.Redirect("error.aspx");
                }




                #region literaux pour insertion commentaires, tags, etc

                litForCommentaireBas.Text = GetLiteralCommentaireEnclosed(fileP, "Bas", resulEvent);
                litForCommentaireHaut.Text = GetLiteralCommentaireEnclosed(fileP, "Haut", resulEvent);
                #endregion

                #region fichiers javascripts par page/structures
                // envoie le fichier javascript propres à la page 
                System.Text.StringBuilder sb_insere_javascriptsfiles = new System.Text.StringBuilder();
                //basePage bp = new basePage();
                //Urls url = new Urls();

                Urls url = new Urls();
                //ScriptManager.RegisterStartupScript(this.Page, typeof(Page), string.Format("StartupInclude"), sb_insere_javascriptsfiles.ToString(), false);
                sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile(fileP, resulEvent, true));
                //   sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile("commons", resulEvent, true));

                ScriptManager.RegisterStartupScript(Page, typeof(Page), "Master", sb_insere_javascriptsfiles.ToString(), false);
                #endregion

                /*  if (Request.QueryString["isPop"] == "1")
                  {
                      hlHomePage.NavigateUrl = "closeme";
                      hlHomePage.Text = "Closeme";
                  }
                  else
                  {
                      string pageHome = "Home.aspx";
                      string qs = Page.Request.Url.Query;
                      fileP = pageHome + qs;
                      hlHomePage.NavigateUrl = fileP;
                  }
                  */
                dvpflag.Attributes.Add("poss", dvPossible(IdStructure).ToString().ToLower());

            }


        }

        private static bool dvPossible(int idStructure)
        {
            string nomCachDVPoss = "dvposs" + idStructure;
            bool dvpossible = false;
            if (HttpContext.Current.Cache[nomCachDVPoss] != null)
            {
                dvpossible = (bool)HttpContext.Current.Cache[nomCachDVPoss];
            }
            else
            {
                //customerArea.wcf_WebOpen.Iwcf_wsOpenClient wcfO = new customerArea.wcf_WebOpen.Iwcf_wsOpenClient();
                wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
                dvpossible = wcfThemis.DepotVente_ispossible(idStructure);
                HttpContext.Current.Cache.Insert(nomCachDVPoss, dvpossible, null, DateTime.Now.AddSeconds(60), TimeSpan.Zero);
            }
            return dvpossible;
        }



        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string GetHtmlDetailCmd(int IdStructure, int OrderId, string typeL, int eventId)
        {
            if (System.Web.HttpContext.Current.Session["eventId"] != null && int.TryParse(System.Web.HttpContext.Current.Session["eventId"].ToString(), out int resultEvent))
            {

            }

            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null)
            {
                string myIdentiteId = System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString();

                DataSet dsHistoCmds = wctrlHistoUser.GetDetailCommande(IdStructure.ToString("0000"), int.Parse(myIdentiteId), OrderId, typeL, eventId);

                if (dsHistoCmds != null && dsHistoCmds.Tables.Count > 0)
                {
                    string html = "<table>";
                    foreach (DataRow dr in dsHistoCmds.Tables[0].Rows)
                    {
                        html += "<tr><td>" + dr["type_tarif_nom"].ToString() + "</td></tr>";
                    }
                    html += "</table>";
                    return OrderId + "_" + typeL + "_" + eventId + ":" + html;

                }


            }

            return OrderId + ":detail Cmd " + OrderId.ToString();
        }

        /// <summary>
        /// creation du pdf / appel à wcf_Themis
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="commandeId"></param>
        /// <param name="identiteId"></param>
        /// <param name="dossierId"></param>
        /// <param name="consommateurName"></param>
        /// <returns></returns>
        private static string GetPdfName(int structureId, int commandeId, int identiteId, int sessionId, int dossierId, string consommateurName)
        {

            wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();
            //customerArea.wsOpen.wsThemisOpen wsO = new customerArea.wsOpen.wsThemisOpen();
            //   wsO.test_DepotVente_inform(0, true, 0, true, 0,true, 0, true, "11");
            string langIso = App_Code.Initialisations.GetUserLanguage();
            /* string langIso = "en";
             if (System.Web.HttpContext.Current.Session["SVarLangue"] != null)
             {
                 // langIso = System.Web.HttpContext.Current.Session["SVarLangue"].ToString();
                 langIso = System.Web.HttpContext.Current.Session["language_id"].ToString();
             }*/



            MyDictionary mySSC = new MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(structureId);


            string filiere_id = "";
            if (mySSC.Contains("CREATEPROFILWEBFILIEREID"))
            {
                filiere_id = mySSC["CREATEPROFILWEBFILIEREID"];
            }

            {
                if (!int.TryParse(filiere_id, out int iFiliereId))
                {
                    GestionTraceManager.WriteLogError(structureId, "CREATEPROFILWEBFILIEREID incorrect dans le config.ini (" + filiere_id + ")");
                    exceptionConfigIni e = new exceptionConfigIni("CREATEPROFILWEBFILIEREID incorrect dans le config.ini (" + filiere_id + ")");
                    throw e;
                }

            }


            int operateurId = int.Parse(mySSC["PAIEMENTWEBOPERATORID"]);

            string pathPdf = System.Configuration.ConfigurationManager.AppSettings["redoPdf"].Replace("[idstructure]", structureId.ToString("0000")).Replace("[idstructureSur4zeros]", structureId.ToString("0000"));
            //string completepathPdf = wsO.TAH_MakePDF_FromCmd(structureId, int.Parse(commande_id), entree_id + ":" + seance_id.ToString(), pathPdf, "test consom", langIso);

            List<string> completepathPdf = wcfThemis.TAH_MakePDF_FromCmd(structureId, commandeId, int.Parse(filiere_id), operateurId, identiteId, pathPdf, consommateurName, langIso, "CAHist").ToList();


            return string.Join("|", completepathPdf);
            //return completepathPdf.Join( [0]+;
            /*FileInfo fileInf = new FileInfo(completepathPdf[0]);
            return fileInf.Name;*/
        }






    }

}