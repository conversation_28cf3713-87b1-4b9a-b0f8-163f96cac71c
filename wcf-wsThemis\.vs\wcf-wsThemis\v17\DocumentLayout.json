{"Version": 1, "WorkspaceRootPath": "D:\\WORK\\VIEUXPROJETS\\wcf-wsThemis\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{648C5A8D-0A62-4439-B2BC-1F377D7ACAF3}|wcf-wsThemis.csproj|d:\\work\\vieuxprojets\\wcf-wsthemis\\wcf-wsthemis.svc.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{648C5A8D-0A62-4439-B2BC-1F377D7ACAF3}|wcf-wsThemis.csproj|solutionrelative:wcf-wsthemis.svc.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{648C5A8D-0A62-4439-B2BC-1F377D7ACAF3}|wcf-wsThemis.csproj|D:\\WORK\\VIEUXPROJETS\\wcf-wsThemis\\web.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{648C5A8D-0A62-4439-B2BC-1F377D7ACAF3}|wcf-wsThemis.csproj|solutionrelative:web.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{817A794C-460D-4585-83C3-2C0B5041A8A9}|..\\ws_DTO\\ws_DTO.csproj|d:\\work\\vieuxprojets\\ws_dto\\gestionplaceentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\WORK\\VIEUXPROJETS\\customerAreaV2\\customerArea\\wctrlGuestLogin.ascx.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{648C5A8D-0A62-4439-B2BC-1F377D7ACAF3}|wcf-wsThemis.csproj|D:\\WORK\\VIEUXPROJETS\\wcf-wsThemis\\global.asax.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{648C5A8D-0A62-4439-B2BC-1F377D7ACAF3}|wcf-wsThemis.csproj|solutionrelative:global.asax.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\WORK\\VIEUXPROJETS\\customerAreaV2\\customerArea\\Login.aspx||{57312C73-6202-49E9-B1E1-40EA1A6DC1F6}"}, {"AbsoluteMoniker": "D:0:0:{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}|..\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj|D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\EventsSessions\\GestionPlaceEntity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F11805B-FC68-4EBC-BB35-D458FF8B9312}|..\\OpenEntity2010\\OpenEntity2010.csproj|D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\entities\\GestionPlaceEntity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{817A794C-460D-4585-83C3-2C0B5041A8A9}|..\\ws_DTO\\ws_DTO.csproj|D:\\WORK\\VIEUXPROJETS\\ws_DTO\\app.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{A2FACD66-71D3-489A-9BA0-BEB74AC55956}|..\\WebTracing2010\\WebTracing2010.csproj|D:\\WORK\\VIEUXPROJETS\\WebTracing2010\\app.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{8F11805B-FC68-4EBC-BB35-D458FF8B9312}|..\\OpenEntity2010\\OpenEntity2010.csproj|D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\app.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}|..\\ws_BLL\\ws_BLL.csproj|D:\\WORK\\VIEUXPROJETS\\ws_BLL\\mailing\\cibleManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{99b8fa2f-ab90-4f57-9c32-949f146f1914}"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Login.aspx", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\customerAreaV2\\customerArea\\Login.aspx", "RelativeDocumentMoniker": "..\\customerAreaV2\\customerArea\\Login.aspx", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\customerAreaV2\\customerArea\\Login.aspx", "RelativeToolTip": "..\\customerAreaV2\\customerArea\\Login.aspx", "LogicalView": "7651a703-06e5-11d1-8ebd-00a0c90f26ea", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000195|", "WhenOpened": "2025-08-26T16:26:52.703Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "wcf-wsThemis.svc.cs", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\wcf-wsThemis\\wcf-wsThemis.svc.cs", "RelativeDocumentMoniker": "wcf-wsThemis.svc.cs", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\wcf-wsThemis\\wcf-wsThemis.svc.cs", "RelativeToolTip": "wcf-wsThemis.svc.cs", "ViewState": "AgIAAG4CAAAAAAAAAAAUwCgDAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T15:38:14.283Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "GestionPlaceEntity.cs", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\GestionPlaceEntity.cs", "RelativeDocumentMoniker": "..\\ws_DTO\\GestionPlaceEntity.cs", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\GestionPlaceEntity.cs", "RelativeToolTip": "..\\ws_DTO\\GestionPlaceEntity.cs", "ViewState": "AgIAACoAAAAAAAAAAAA6wDAAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-25T07:02:55.368Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "GestionPlaceEntity.cs", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\EventsSessions\\GestionPlaceEntity.cs", "RelativeDocumentMoniker": "..\\Themis.Libraries.DTO\\EventsSessions\\GestionPlaceEntity.cs", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.DTO\\EventsSessions\\GestionPlaceEntity.cs", "RelativeToolTip": "..\\Themis.Libraries.DTO\\EventsSessions\\GestionPlaceEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-25T07:02:53.336Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "GestionPlaceEntity.cs", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\entities\\GestionPlaceEntity.cs", "RelativeDocumentMoniker": "..\\OpenEntity2010\\entities\\GestionPlaceEntity.cs", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\entities\\GestionPlaceEntity.cs", "RelativeToolTip": "..\\OpenEntity2010\\entities\\GestionPlaceEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-25T07:02:51.263Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "app.config", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\app.config", "RelativeDocumentMoniker": "..\\ws_DTO\\app.config", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\ws_DTO\\app.config", "RelativeToolTip": "..\\ws_DTO\\app.config", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000601|", "WhenOpened": "2025-09-09T08:25:56.749Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Web.config", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\wcf-wsThemis\\Web.config", "RelativeDocumentMoniker": "Web.config", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\wcf-wsThemis\\Web.config", "RelativeToolTip": "Web.config", "ViewState": "AgIAAAAAAAAAAAAAAAAAACUAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000601|", "WhenOpened": "2025-08-06T10:24:44.172Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "wctrlGuestLogin.ascx.cs", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\customerAreaV2\\customerArea\\wctrlGuestLogin.ascx.cs", "RelativeDocumentMoniker": "..\\customerAreaV2\\customerArea\\wctrlGuestLogin.ascx.cs", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\customerAreaV2\\customerArea\\wctrlGuestLogin.ascx.cs", "RelativeToolTip": "..\\customerAreaV2\\customerArea\\wctrlGuestLogin.ascx.cs", "ViewState": "AgIAAEoAAAAAAAAAAAAmwF8AAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-09T08:21:31.193Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "app.config", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\WebTracing2010\\app.config", "RelativeDocumentMoniker": "..\\WebTracing2010\\app.config", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\WebTracing2010\\app.config", "RelativeToolTip": "..\\WebTracing2010\\app.config", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000601|", "WhenOpened": "2025-09-09T08:25:34.878Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "app.config", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\app.config", "RelativeDocumentMoniker": "..\\OpenEntity2010\\app.config", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\OpenEntity2010\\app.config", "RelativeToolTip": "..\\OpenEntity2010\\app.config", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000601|", "WhenOpened": "2025-09-09T08:24:53.714Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "cibleManager.cs", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\ws_BLL\\mailing\\cibleManager.cs", "RelativeDocumentMoniker": "..\\ws_BLL\\mailing\\cibleManager.cs", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\ws_BLL\\mailing\\cibleManager.cs", "RelativeToolTip": "..\\ws_BLL\\mailing\\cibleManager.cs", "ViewState": "AgIAADUAAAAAAAAAAAAQwJ8AAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T15:37:11.255Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Global.asax.cs", "DocumentMoniker": "D:\\WORK\\VIEUXPROJETS\\wcf-wsThemis\\Global.asax.cs", "RelativeDocumentMoniker": "Global.asax.cs", "ToolTip": "D:\\WORK\\VIEUXPROJETS\\wcf-wsThemis\\Global.asax.cs", "RelativeToolTip": "Global.asax.cs", "ViewState": "AgIAAAYAAAAAAAAAAIBJwBIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-18T13:24:52.319Z", "EditorCaption": ""}]}]}]}