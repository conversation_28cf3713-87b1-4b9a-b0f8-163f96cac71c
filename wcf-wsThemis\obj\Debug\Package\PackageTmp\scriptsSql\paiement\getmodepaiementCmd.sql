﻿select  isnull(mp.mode_paie_nom,'') as mode_Paiement ,sum(cc.montant1+cc.montant2) as Mont<PERSON>_Paiement ,CompteMP.commande_id as Commande_ID  from 

compte_client cc 
left outer join mode_paiement mp on mp.mode_paie_id = cc.mode_paiement_id
inner join (
select distinct  ct.operation2_id as Operation_id,cc.commande_id  as commande_id from compte_client cc inner join compte_transaction ct on ct.operation1_id = cc.cc_operation_id     
inner join compte_client cc1 on cc1.cc_operation_id = ct.operation2_id

where cc.commande_id = [CommandeID] and cc.cc_typetransaction in ('CREDITDOS','CREDITPRO') and ct.transaction_type = 'MACPTE') as CompteMP 
on cc.cc_operation_id = CompteMP.Operation_id

group by isnull(mp.mode_paie_nom,''),cc.num_acompte,CompteMP.commande_id