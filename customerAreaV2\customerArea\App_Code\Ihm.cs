using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Xml;
using System.Collections;
using System.IO;
using log4net;
using customerArea.App_Code;
using ws_DTO;
using ws_bll.WT;
using System.Collections.Generic;
using System.Threading;
using utilitaires2010;

namespace customerArea
{
    public class Ihm
    {
        private static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


        public Hashtable htLibelle;
        public Hashtable htContextualHelp;
        public Hashtable htVisible;
        public Hashtable htObligatoire;
        public Hashtable htLock;
        public Hashtable htEnable;
        public Hashtable htAttribut;
        public Hashtable htType;
        public Hashtable htFormat;
        public Hashtable htImage;

        private static XmlDocument xmlFile;


        /// <summary>
        /// lecture du fichier de param�trage de IHM
        /// </summary>
        /// <param name="cle_ihm">cl� du Web.config pointant le fichier xml IHM decrivant la page</param>
        /// <param name="cle_1">balise niveau 1 dans le fichier xml</param>
        /// <param name="cle_2">balise niveau 2 dans le fichier xml</param>
        [Obsolete]
        public void ReadXMLFile(string cle_ihm, string cle_1, string cle_2)//"IHMCHOIXMANIFESTATION"
        {

            int idStructure = 0;
            if (System.Web.HttpContext.Current.Session["IdStructure"] != null)
            {
                idStructure = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());
            }

            try
            {


                App_Code.Initialisations myInit = new App_Code.Initialisations();
                // string Nom_Fichier = myInit.GetFile(cle_ihm, ".xml");
                string Nom_Fichier = "";

                //gt.WriteLog("Nom_Fichier=" + Nom_Fichier, "LOGGENERIQUE");

                htLibelle = new Hashtable();
                htContextualHelp = new Hashtable();
                htVisible = new Hashtable();
                htEnable = new Hashtable();
                htObligatoire = new Hashtable();
                htLock = new Hashtable();
                htFormat = new Hashtable();
                htAttribut = new Hashtable();
                htType = new Hashtable();
                htImage = new Hashtable();

                XmlDocument xmlDoc = new XmlDocument();
                xmlDoc.Load(Nom_Fichier);

                if (xmlDoc == null)
                {
                    GestionTraceManager.WriteLogError(idStructure, "xmlDoc == null (" + Nom_Fichier + ")");
                }

                xmlFile = xmlDoc;

                XmlNode xmlRoot = xmlDoc.SelectSingleNode(cle_1 + "/" + cle_2);

                //gt.WriteLog(xmlRoot.ChildNodes.Count + " nodes in " + Nom_Fichier, "LOGGENERIQUE");
                foreach (XmlNode xmlNode in xmlRoot.ChildNodes)
                {
                    //VISIBLE O/N
                    XmlNode xmlNodeChild = xmlNode.SelectSingleNode("VISIBLE");
                    if (xmlNodeChild != null)
                        htVisible.Add(xmlNode.Name, bool.Parse(xmlNodeChild.InnerText));
                    //ENABLE
                    xmlNodeChild = xmlNode.SelectSingleNode("ENABLE");
                    if (xmlNodeChild != null)
                        htEnable.Add(xmlNode.Name, bool.Parse(xmlNodeChild.InnerText));
                    //OBLIGATOIRE
                    xmlNodeChild = xmlNode.SelectSingleNode("OBLIGATOIRE");
                    if (xmlNodeChild != null)
                        htObligatoire.Add(xmlNode.Name, bool.Parse(xmlNodeChild.InnerText));
                    //LOCK
                    xmlNodeChild = xmlNode.SelectSingleNode("LOCK");
                    if (xmlNodeChild != null)
                        htLock.Add(xmlNode.Name, bool.Parse(xmlNodeChild.InnerText));
                    //LABEL
                    xmlNodeChild = xmlNode.SelectSingleNode("LABEL");
                    if (xmlNodeChild != null)
                        htLibelle.Add(xmlNode.Name, xmlNodeChild.InnerText);
                    xmlNodeChild = xmlNode.SelectSingleNode("CONTEXTUALHELP");
                    //AIDE CONTEXTUELLE
                    if (xmlNodeChild != null)
                        htContextualHelp.Add(xmlNode.Name, xmlNodeChild.InnerText);
                    //ATTRIBUT
                    xmlNodeChild = xmlNode.SelectSingleNode("ATTRIBUT");
                    if (xmlNodeChild != null)
                        htAttribut.Add(xmlNode.Name, xmlNodeChild.InnerText);
                    //TYPE
                    xmlNodeChild = xmlNode.SelectSingleNode("TYPE");
                    if (xmlNodeChild != null)
                        htType.Add(xmlNode.Name, xmlNodeChild.InnerText);
                    //FORMAT
                    xmlNodeChild = xmlNode.SelectSingleNode("FORMAT");
                    if (xmlNodeChild != null)
                        htFormat.Add(xmlNode.Name, xmlNodeChild.InnerText);
                    //IMAGE
                    xmlNodeChild = xmlNode.SelectSingleNode("IMAGE");
                    if (xmlNodeChild != null)
                        htImage.Add(xmlNode.Name, xmlNodeChild.InnerText);
                }
            }
            catch (Exception ex)
            {
                GestionTraceManager.WriteLogError(idStructure, "in ReadXMLFile(" + cle_ihm + "):" + ex.Message);
                //WriteLogError(ex.Message);
            }
            finally
            {
                //if (read_xml != null)
                //   read_xml.Close();
            }
        }


        public bool ReadXMLFile(string cle_ihm, string idEvent, string idpa, string cle_1, string cle_2, bool useDefaultIfNotExists)//"IHMCHOIXMANIFESTATION"
        {
            bool ok = true;

            int idStructure = 0;
            if (!string.IsNullOrEmpty(System.Web.HttpContext.Current.Session["IdStructure"].ToString()))
            {
                idStructure = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());
            }
            else
            {
                logger.Debug("Session[IdStructure] is null or empty !!");
                if (!string.IsNullOrEmpty(System.Web.HttpContext.Current.Session["idstructure"].ToString()))
                {
                    idStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());
                }
                else
                {
                    logger.Debug("Session[idstructure] is null or empty !!!!!!");
                }

            }

            try
            {

                //logger.Debug("ReadXMLFile : idstructure " + idStructure);

                App_Code.Initialisations myInit = new App_Code.Initialisations();
                string Nom_Fichier = myInit.GetFile(cle_ihm, idEvent, idpa, ".xml", useDefaultIfNotExists);

                if (Nom_Fichier == "")
                {
                    GestionTraceManager.WriteLogError(idStructure, "Pb de IHM !!! " + cle_ihm + " file : " + Nom_Fichier);
                    logger.Error("Nom_Fichier est vide dans ReadXMLFile(" + cle_ihm + "," + idEvent.ToString() + "," + cle_1 + "," + cle_2 + ")");
                    Exception ex = new Exception("Nom_Fichier est vide dans ReadXMLFile(" + cle_ihm + "," + idEvent.ToString() + "," + cle_1 + "," + cle_2 + ")");
                    throw ex;
                }
                if (Nom_Fichier == "notfound")
                {
                    GestionTraceManager.WriteLogError(idStructure, "Pb de IHM !!! " + cle_ihm + " file : " + Nom_Fichier);
                    logger.Error("Nom_Fichier est vide dans ReadXMLFile(" + cle_ihm + "," + idEvent.ToString() + "," + cle_1 + "," + cle_2 + ")");
                    return false;
                }

                //gt.WriteLog("Nom_Fichier=" + Nom_Fichier, "LOGGENERIQUE");

                htLibelle = new Hashtable();
                htContextualHelp = new Hashtable();
                htVisible = new Hashtable();
                htEnable = new Hashtable();
                htObligatoire = new Hashtable();
                htLock = new Hashtable();
                htFormat = new Hashtable();
                htAttribut = new Hashtable();
                htType = new Hashtable();
                htImage = new Hashtable();

                XmlDocument xmlDoc = new XmlDocument();
                xmlDoc.Load(Nom_Fichier);

                bool isFileExist = File.Exists(Nom_Fichier);
                if (!isFileExist)
                {
                    GestionTraceManager.WriteLogError(idStructure, Nom_Fichier + " not exist");
                    Exception ex = new Exception(Nom_Fichier + " not exist");
                    throw ex;
                }

                if (xmlDoc == null)
                {
                    GestionTraceManager.WriteLogError(idStructure, "xmlDoc == null (" + Nom_Fichier + ")");
                    Exception ex = new Exception("xmlDoc == null (" + Nom_Fichier + ")");
                    throw ex;

                }

                xmlFile = xmlDoc;

                XmlNode xmlRoot = xmlDoc.SelectSingleNode(cle_1 + "/" + cle_2);

                //gt.WriteLog(xmlRoot.ChildNodes.Count + " nodes in " + Nom_Fichier, "LOGGENERIQUE");
                foreach (XmlNode xmlNode in xmlRoot.ChildNodes)
                {
                    //VISIBLE O/N
                    XmlNode xmlNodeChild = xmlNode.SelectSingleNode("VISIBLE");
                    if (xmlNodeChild != null)
                        htVisible.Add(xmlNode.Name, bool.Parse(xmlNodeChild.InnerText));
                    //ENABLE
                    xmlNodeChild = xmlNode.SelectSingleNode("ENABLE");
                    if (xmlNodeChild != null)
                        htEnable.Add(xmlNode.Name, bool.Parse(xmlNodeChild.InnerText));
                    //OBLIGATOIRE
                    xmlNodeChild = xmlNode.SelectSingleNode("OBLIGATOIRE");
                    if (xmlNodeChild != null)
                        htObligatoire.Add(xmlNode.Name, bool.Parse(xmlNodeChild.InnerText));
                    //LOCK
                    xmlNodeChild = xmlNode.SelectSingleNode("LOCK");
                    if (xmlNodeChild != null)
                        htLock.Add(xmlNode.Name, bool.Parse(xmlNodeChild.InnerText));
                    //LABEL
                    xmlNodeChild = xmlNode.SelectSingleNode("LABEL");
                    if (xmlNodeChild != null)
                        htLibelle.Add(xmlNode.Name, xmlNodeChild.InnerText);
                    xmlNodeChild = xmlNode.SelectSingleNode("CONTEXTUALHELP");
                    //AIDE CONTEXTUELLE
                    if (xmlNodeChild != null)
                        htContextualHelp.Add(xmlNode.Name, xmlNodeChild.InnerText);
                    //ATTRIBUT
                    xmlNodeChild = xmlNode.SelectSingleNode("ATTRIBUT");
                    if (xmlNodeChild != null)
                        htAttribut.Add(xmlNode.Name, xmlNodeChild.InnerText);
                    //TYPE
                    xmlNodeChild = xmlNode.SelectSingleNode("TYPE");
                    if (xmlNodeChild != null)
                        htType.Add(xmlNode.Name, xmlNodeChild.InnerText);
                    //FORMAT
                    xmlNodeChild = xmlNode.SelectSingleNode("FORMAT");
                    if (xmlNodeChild != null)
                        htFormat.Add(xmlNode.Name, xmlNodeChild.InnerText);
                    //IMAGE
                    xmlNodeChild = xmlNode.SelectSingleNode("IMAGE");
                    if (xmlNodeChild != null)
                        htImage.Add(xmlNode.Name, xmlNodeChild.InnerText);
                }
            }


            catch (Exception ex)
            {

                GestionTraceManager.WriteLogError(idStructure, "in ReadXMLFile(" + cle_ihm + "):" + ex.Message + " " + ex.StackTrace);
                throw ex;
                //WriteLogError(ex.Message);
            }
            finally
            {
                //if (read_xml != null)
                //   read_xml.Close();

            }
            return ok;
        }


        public void GetVisibility(string mykey, ref HtmlGenericControl myControl)
        {
            if (htVisible == null)
            {
                Exception ex = new Exception("htvisible==null dans GetVisibility(" + mykey + ",,)");
                throw ex;
            }
            if (htVisible[mykey] == null)
            {
                //si la cl� n'existe pas on cache le control
                myControl.Visible = false;
                myControl.Attributes.Add("data-pageihm-visible", "false");
            }
            else
            {

                if (htObligatoire[mykey] != null && (bool)htObligatoire[mykey])
                {
                    myControl.Attributes.Add("data-pageihm-mandatory", "true");
                    myControl.Attributes.Add("required", "");
                }
                else
                {
                    myControl.Attributes.Add("data-pageihm-mandatory", "false");
                }

                if ((bool)htVisible[mykey])
                {
                    myControl.Visible = true;
                }
                else
                {
                    myControl.Visible = false;
                }
            }
        }


        public void GetVisibility(string mykey, ref TextBox myControl, ref Label myLabel, string termTranslateFile)
        {
            if (htVisible == null)
            {
                Exception ex = new Exception("htvisible==null dans GetVisibility(" + mykey + ",,)");
                throw ex;
            }
            if (htVisible[mykey] == null)
            {
                //si la cl� n'existe pas on cache le control
                myControl.Visible = true;
                myControl.Attributes.Add("data-pageihm-visible", "false");
                myLabel.Visible = true;
                myLabel.Attributes.Add("data-pageihm-visible", "false");

                // Exception ex = new Exception("htVisible["  + mykey + "]==null dans GetVisibility()");
                //throw ex;
            }
            else
            {

                if ((bool)htVisible[mykey])
                {
                    myControl.Visible = true;
                    myControl.Text = "";
                    myLabel.Visible = true;
                    if (htLibelle[mykey] != null)
                        myLabel.Text = App_Code.Initialisations.GetMessageTranslate(termTranslateFile);
                    //myLabel.Text = (string)htLibelle[mykey];


                    if (htLock != null && htLock[mykey] != null && (bool)htLock[mykey])
                    {
                        myControl.Attributes.Add("data-pageihm-lock", "true");
                    }
                    else
                    {
                        myControl.Attributes.Add("data-pageihm-lock", "false");
                    }


                    if (htObligatoire[mykey] != null && (bool)htObligatoire[mykey])
                    {
                        myLabel.CssClass = "csslabelobligatoire";
                        myControl.Attributes.Add("data-pageihm-mandatory", "true");
                        myControl.Attributes.Add("required", "");
                    }
                    else
                    {
                        myLabel.CssClass = "csslabel";
                        myControl.Attributes.Add("data-pageihm-mandatory", "false");
                    }

                    if (htContextualHelp[mykey] != null)
                    {
                        myControl.Attributes.Add("title", App_Code.Initialisations.GetMessageTranslate("contextual_help_" + mykey.ToLower()));
                        myControl.Attributes.Remove("placeholder");
                        myControl.Attributes.Add("placeholder", App_Code.Initialisations.GetMessageTranslate("contextual_help_" + mykey.ToLower()));
                        //myControl.Attributes.Add("placeholder",htContextualHelp[mykey].ToString());
                    }


                    if (htFormat[mykey] != null && htFormat[mykey].ToString().Contains("UPPER"))
                    {
                        //myControl.Attributes.Add("onkeyup", "this.value=this.value.toUpperCase()");
                        myControl.Attributes.Add("data-upper", "true");
                    }

                }
                else
                {
                    myControl.Visible = true;
                    myControl.Attributes.Add("data-pageihm-visible", "false");
                    myLabel.Visible = true;
                    myLabel.Attributes.Add("data-pageihm-visible", "false");

                }
            }
        }

        /// <summary>
        /// rend visible ou pas les controls
        /// </summary>
        /// <param name="json">fichier json </param>
        /// <param name="jsonKey">cl� du json � v�rifier </param>
        /// <param name="groupControl">control form-group de l'aspx</param>
        /// <param name="inputControl">input control</param>
        public void GetVisibilityTextBoxJson(dynamic json, string jsonKey, ref HtmlGenericControl groupControl, ref HtmlInputText inputControl)
        {
            if (groupControl != null)
            {
                groupControl.Visible = Convert.ToBoolean(json[jsonKey].visible.Value.ToString().ToLower());
                if (inputControl.Visible)
                {
                    inputControl.ClientIDMode = ClientIDMode.Static;

                    if (json[jsonKey].isupper != null)
                    {
                        inputControl.Attributes.Add("data-upper", json[jsonKey].isupper.Value.ToString().ToLower());
                    }
                    inputControl.Attributes.Add("data-pageihm-mandatory", json[jsonKey].mandatory.Value.ToString().ToLower());
                }
            }

        }


        /// <summary>
        /// rend visible ou pas les controls
        /// </summary>
        /// <param name="json">fichier json </param>
        /// <param name="jsonKey">cl� du json � v�rifier </param>
        /// <param name="groupControl">control form-group de l'aspx</param>
        /// <param name="inputControl">input control</param>
        public void GetVisibilityCheckBoxJson(dynamic json, string jsonKey, ref HtmlGenericControl groupControl, ref HtmlInputCheckBox inputControl)
        {
            if (groupControl != null)
            {
                groupControl.Visible = Convert.ToBoolean(json[jsonKey].visible.Value.ToString().ToLower());
                if (inputControl.Visible)
                {
                    inputControl.ClientIDMode = ClientIDMode.Static;

                    if (json[jsonKey].isupper != null)
                    {
                        inputControl.Attributes.Add("data-upper", json[jsonKey].isupper.Value.ToString().ToLower());
                    }
                    inputControl.Attributes.Add("data-pageihm-mandatory", json[jsonKey].mandatory.Value.ToString().ToLower());
                }
            }

        }

        /// <summary>
        /// rend visible ou pas les controls
        /// </summary>
        /// <param name="json">fichier json </param>
        /// <param name="jsonKey">cl� du json � v�rifier </param>
        /// <param name="groupControl">control form-group de l'aspx</param>
        /// <param name="inputEmailControl">input type="email" control</param>
        public void GetVisibilityTextBoxEmailJson(dynamic json, string jsonKey, ref HtmlGenericControl groupControl, ref HtmlInputGenericControl inputEmailControl)
        {
            if (groupControl != null)
            {
                groupControl.Visible = Convert.ToBoolean(json[jsonKey].visible.Value.ToString().ToLower());
                if (inputEmailControl.Visible)
                {
                    inputEmailControl.ClientIDMode = ClientIDMode.Static;

                    if (json[jsonKey].isupper != null)
                    {
                        inputEmailControl.Attributes.Add("data-upper", json[jsonKey].isupper.Value.ToString().ToLower());
                    }
                    inputEmailControl.Attributes.Add("data-pageihm-mandatory", json[jsonKey].mandatory.Value.ToString().ToLower());
                }
            }

        }

        public void GetVisibilityTextAreaJson(dynamic json, string jsonKey, ref HtmlGenericControl groupControl, ref HtmlTextArea inputControl)
        {
            if (groupControl != null)
            {
                groupControl.Visible = Convert.ToBoolean(json[jsonKey].visible.Value.ToString().ToLower());
                if (inputControl.Visible)
                {
                    inputControl.ClientIDMode = ClientIDMode.Static;

                    if (json[jsonKey].isupper != null)
                    {
                        inputControl.Attributes.Add("data-upper", json[jsonKey].isupper.Value.ToString().ToLower());
                    }

                    inputControl.Attributes.Add("data-pageihm-mandatory", json[jsonKey].mandatory.Value.ToString().ToLower());
                }
            }

        }

        public void GetVisibilitySelectJson(dynamic json, string jsonKey, ref HtmlGenericControl groupControl, ref HtmlSelect selectControl)
        {
            if (groupControl != null)
            {
                groupControl.Visible = Convert.ToBoolean(json[jsonKey].visible.Value.ToString().ToLower());
                if (selectControl.Visible)
                {
                    selectControl.ClientIDMode = ClientIDMode.Static;
                    selectControl.Attributes.Add("data-pageihm-mandatory", json[jsonKey].mandatory.Value.ToString().ToLower());
                }
            }
        }



        public void GetVisibility(string mykey, ref TextBox myControl, ref Label myLabel, ref Label contextualHelpLabel)
        {
            if ((bool)htVisible[mykey])
            {
                myControl.Visible = true;
                myControl.Text = "";
                myLabel.Visible = true;
                if (htLibelle[mykey] != null)
                    myLabel.Text = (string)htLibelle[mykey];
                if (htObligatoire[mykey] != null && (bool)htObligatoire[mykey])
                    myLabel.CssClass = "csslabelobligatoire";
                else
                    myLabel.CssClass = "csslabel";

                if (htContextualHelp[mykey] != null)
                    contextualHelpLabel.Text = htContextualHelp[mykey].ToString();

                if (htFormat[mykey] != null && htFormat[mykey].ToString().Contains("UPPER"))
                {
                    // myControl.Attributes.Add("onkeyup", "this.value=this.value.toUpperCase()");
                    myControl.Attributes.Add("data-upper", "true");
                }

            }
            else
            {
                myControl.Visible = false;
                contextualHelpLabel.Visible = false;
                myLabel.Visible = false;
            }
        }

        public void GetVisibility(string mykey, ref TextBox myControl, ref Label labelTitre, ref Label contextualHelpLabel, ref RequiredFieldValidator myRFV)
        {
            if ((bool)htVisible[mykey])
            {
                myControl.Visible = true;
                myControl.Text = "";
                labelTitre.Visible = true;
                if (htLibelle[mykey] != null)
                    labelTitre.Text = (string)htLibelle[mykey];
                if (htContextualHelp[mykey] != null)
                {
                    //contextualHelpLabel.Text = htContextualHelp[mykey].ToString();
                    myControl.Attributes.Add("title", htContextualHelp[mykey].ToString());
                }
                if (htObligatoire[mykey] != null && (bool)htObligatoire[mykey])
                {
                    myRFV.Enabled = true;
                    myRFV.ErrorMessage = labelTitre.Text;
                    myRFV.Text = "*";
                    labelTitre.CssClass = "csslabelobligatoire";
                }
                else
                {
                    myRFV.Enabled = false;
                    labelTitre.CssClass = "csslabel";
                }
                if (htFormat[mykey] != null && htFormat[mykey].ToString().Contains("UPPER"))
                {
                    // myControl.Attributes.Add("onkeyup", "this.value=this.value.toUpperCase()");
                    myControl.Attributes.Add("data-upper", "true");
                }

            }
            else
            {
                myControl.Visible = false;
                labelTitre.Visible = false;
                contextualHelpLabel.Visible = false;
                myRFV.Visible = false;
            }
        }

        public void GetVisibility(string mykey, ref TextBox myControl, ref Label labelTitre, ref RequiredFieldValidator myRFV)
        {
            if ((bool)htVisible[mykey])
            {
                myControl.Visible = true;
                myControl.Text = "";
                labelTitre.Visible = true;
                if (htLibelle[mykey] != null)
                    labelTitre.Text = (string)htLibelle[mykey];
                if (htContextualHelp[mykey] != null)
                {
                    //contextualHelpLabel.Text = htContextualHelp[mykey].ToString();
                    myControl.Attributes.Add("title", htContextualHelp[mykey].ToString());
                }
                if (htObligatoire[mykey] != null && (bool)htObligatoire[mykey])
                {


                    myRFV.Enabled = true;
                    myRFV.ErrorMessage = labelTitre.Text;
                    myRFV.Text = "*";
                    labelTitre.CssClass = "csslabelobligatoire";
                }
                else
                {
                    myRFV.Enabled = false;
                    labelTitre.CssClass = "csslabel";
                }
                if (htFormat[mykey] != null && htFormat[mykey].ToString().Contains("UPPER"))
                {
                    // myControl.Attributes.Add("onkeyup", "this.value=this.value.toUpperCase()");
                    myControl.Attributes.Add("data-upper", "true");
                }

            }
            else
            {
                myControl.Visible = false;
                labelTitre.Visible = false;
                //contextualHelpLabel.Visible = false;
                myRFV.Visible = false;
            }
        }

        public void GetVisibility(string mykey, ref ListBox myControl, ref Label myLabel)
        {
            try
            {
                if (htVisible[mykey] != null && (bool)htVisible[mykey])
                {
                    myControl.Visible = true;
                    myControl.Text = "";
                    myLabel.Visible = true;
                    if (htLibelle[mykey] != null)
                        myLabel.Text = (string)htLibelle[mykey];
                    if (htObligatoire[mykey] != null && (bool)htObligatoire[mykey])
                        myLabel.CssClass = "csslabelobligatoire";
                    else
                        myLabel.CssClass = "csslabel";
                }
                else
                {
                    myControl.Visible = false;
                    myLabel.Visible = false;
                }
            }

            catch (Exception)
            {
                myControl.Visible = false;
                myLabel.Visible = false;
            }
        }

        public void GetVisibility(string mykey, ref DropDownList myControl, ref Label myLabel)
        {
            try
            {
                if (htVisible[mykey] != null && (bool)htVisible[mykey])
                {
                    myControl.Visible = true;
                    myLabel.Visible = true;
                    if (htLibelle[mykey] != null)
                        myLabel.Text = (string)htLibelle[mykey];
                    if (htObligatoire[mykey] != null && (bool)htObligatoire[mykey])
                    {
                        myLabel.CssClass = "csslabelobligatoire";
                        myControl.Attributes.Add("data-pageihm-mandatory", "true");
                        myControl.Attributes.Add("required", "");
                    }
                    else
                    {
                        myLabel.CssClass = "csslabel";
                        myControl.Attributes.Add("data-pageihm-mandatory", "false");
                    }
                }
                else
                {
                    myControl.Visible = false;
                    myLabel.Visible = false;
                }
            }

            catch (Exception)
            {
                myControl.Visible = false;
                myLabel.Visible = false;
            }
        }
        public string GetLibelle(string cle, string termTranslateFile)
        {

            if (htVisible != null)
            {
                bool isvisible = false;
                if (htVisible.Contains(cle))
                    isvisible = (bool)htVisible[cle];
                if (isvisible)
                {

                    return App_Code.Initialisations.GetMessageTranslate(termTranslateFile);
                    /*if (htLibelle[cle] != null && (string)htLibelle[cle] != "")
                        return (string)htLibelle[cle];
                    else
                        return "";*/
                }
                else
                    return "";

            }
            return "";
        }

        public string GetAttribut(string cle)
        {
            if (htAttribut[cle] != null && (string)htAttribut[cle] != "")
                return (string)htAttribut[cle];
            else
                return "";
        }


        public void GetImageButton(string cle, ref System.Web.UI.HtmlControls.HtmlButton MyButton)
        {
            if (htVisible[cle] != null && (bool)htVisible[cle] == true)
            {
                MyButton.Visible = true;
                if (htLibelle[cle] != null)
                    MyButton.InnerText = (string)htLibelle[cle];
            }
            else MyButton.Visible = false;
        }

        public void GetImageButton(string cle, ref ImageButton MyButton)
        {
            if (htVisible[cle] != null && (bool)htVisible[cle] == true)
            {
                MyButton.Visible = true;
                if (htLibelle[cle] != null)
                    MyButton.ToolTip = (string)htLibelle[cle];
                if (htImage[cle] != null && (string)htImage[cle] != "")
                    MyButton.ImageUrl = (string)htImage[cle];
            }
            else MyButton.Visible = false;
        }

        public void GetImageButton(string cle, ref HyperLink MyButton)
        {
            if (htVisible[cle] != null && (bool)htVisible[cle] == true)
            {
                MyButton.Visible = true;
                if (htLibelle[cle] != null)
                {
                    MyButton.ToolTip = (string)htLibelle[cle];
                    MyButton.Text = (string)htLibelle[cle];
                }
                if (htImage[cle] != null && (string)htImage[cle] != "")
                    MyButton.ImageUrl = (string)htImage[cle];
            }
            else MyButton.Visible = false;
        }

        public void GetImageButton(string cle, ref LinkButton MyButton, string termTranslate)
        {
            if (htVisible[cle] != null && (bool)htVisible[cle] == true)
            {
                MyButton.Visible = true;
                if (htLibelle[cle] != null)
                {
                    MyButton.ToolTip = (string)htLibelle[cle];
                    // MyButton.Text = (string)htLibelle[cle];
                    MyButton.Text = App_Code.Initialisations.GetMessageTranslate(termTranslate);
                }
                //if (htImage[cle] != null && (string)htImage[cle] != "")
                //    MyButton.ImageUrl = (string)htImage[cle];
            }
            else MyButton.Visible = false;
        }

        /*
        public void GetImage(string cle, ref Image MyButton, string termTranslate)
        {
            if (htVisible[cle] != null && (bool)htVisible[cle] == true)
            {
                MyButton.Visible = true;
                if (htLibelle[cle] != null)
                {
                    // MyButton.ToolTip = (string)htLibelle[cle];
                    MyButton.ToolTip = Initialisations.GetMessageTranslate(termTranslate);
                }
                if (htImage[cle] != null && (string)htImage[cle] != "")
                    MyButton.ImageUrl = (string)htImage[cle];
            }
            else MyButton.Visible = false;
        }
        */
        public static XmlNode GetXmlNodeinIhmFile(string cle_1)
        {
            if (xmlFile == null)
            {
                Exception ex = new Exception("in GetXmlNodeinIhmFile : xmlFile is null");
                throw ex;
            }
            return xmlFile.SelectSingleNode("//" + cle_1);
        }

        /*  public XmlNode GetXmlNodeinIhmFile(string cle_ihm_inWebConfig, string cle_1)
         {
             XmlDocument xmlDoc = new XmlDocument();
             if (xmlFile == null)
             {
                 Initialisations myInit = new Initialisations();
                 string Nom_Fichier = myInit.GetFile(cle_ihm_inWebConfig, ".xml");
                 xmlDoc.Load(Nom_Fichier);

             }
             else
                 xmlDoc = xmlFile;

             return xmlDoc.SelectSingleNode("//" + cle_1);
         }

         public string GetXmlNodeinIhmFile(string cle_ihm_inWebConfig, string cle_1, string cle_2)
         {
             string strMessage = "";
             XmlDocument xmlDoc = new XmlDocument();
             if (xmlFile == null)
             {
                 Initialisations myInit = new Initialisations();
                 string Nom_Fichier = myInit.GetFile(cle_ihm_inWebConfig, ".xml");
                 if (File.Exists(Nom_Fichier))
                 {

                     xmlDoc.Load(Nom_Fichier);
                 }
             }
             else
                 xmlDoc = xmlFile;

             XmlNode xmlNodeCherche = xmlDoc.SelectSingleNode("//page//" + cle_1 + "//" + cle_2);
             if (xmlNodeCherche != null && xmlNodeCherche.InnerText != null)
             {
                 strMessage = xmlNodeCherche.InnerText;
             }

             return strMessage;
         }
         */
        /* /// <summary>
         /// recherche la cl� dans le fichier (en rechargeant ce fichier : ne tient pas compte de xmlfile d�j� charg�)
         /// </summary>
         /// <param name="cle_ihm_inWebConfig"></param>
         /// <param name="cle_1"></param>
         /// <param name="cle_2"></param>
         /// <returns></returns>
         public string GetXmlNodeinNewIhmFile_old(string cle_ihm_inWebConfig, string cle_1, string cle_2)
         {
             string strMessage = "";
             XmlDocument xmlDoc = new XmlDocument();

             Initialisations myInit = new Initialisations();
             string Nom_Fichier = myInit.GetFile(cle_ihm_inWebConfig, ".xml");
             if (File.Exists(Nom_Fichier))
             {

                 xmlDoc.Load(Nom_Fichier);
                 XmlNode xmlNodeCherche = xmlDoc.SelectSingleNode("//page//" + cle_1 + "//" + cle_2);
                 if (xmlNodeCherche != null && xmlNodeCherche.InnerText != null)
                 {
                     strMessage = xmlNodeCherche.InnerText;
                 }
             }

             return strMessage;
         }*/

        /*/// <summary>
        /// recherche la cl� dans le fichier (en rechargeant ce fichier : ne tient pas compte de xmlfile d�j� charg�)
        /// </summary>
        /// <param name="cle_ihm_inWebConfig"></param>
        /// <param name="attribute">valeur de l'attribut trad</param>
        /// <returns></returns>
        public static string GetXmlMessageTranslate(string keyTranslateFileGeneral,string keyTranslateFileCustomer,string profilAcheteurID, string eventID, string attribute)
        {
            string strMessage = "";
            XmlDocument xmlDoc = new XmlDocument();
            bool wordFound = false;

            

            Initialisations myInit = new Initialisations();
            string Nom_Fichier = myInit.GetFile(keyTranslateFileCustomer, ".xml");
            if (File.Exists(Nom_Fichier))
            {
                xmlDoc.Load(Nom_Fichier);

                string xmlNodeCherche = xmlDoc.SelectSingleNode("//Root//").Attributes["trad"].Value = attribute;
                if (!string.IsNullOrEmpty(xmlNodeCherche))
                {
                    strMessage = xmlNodeCherche;
                    wordFound = true;
                }
            }

            //si l'attrbut n'a pas �t� trouv� on cherche dans le fichier g�n�ral
            if (!wordFound)
            {
                Nom_Fichier = myInit.GetFile(keyTranslateFileGeneral, ".xml");

                if (File.Exists(Nom_Fichier))
                {
                    xmlDoc.Load(Nom_Fichier);

                    string xmlNodeCherche = xmlDoc.SelectSingleNode("//Root//").Attributes["trad"].Value = attribute;
                    if (!string.IsNullOrEmpty(xmlNodeCherche))
                    {
                        strMessage = xmlNodeCherche;
                        wordFound = true;
                    }
                }
            }

            return strMessage;
        }*/

        public void SetValidationExpression(string cle, ref RegularExpressionValidator rexp)
        {
            if (htFormat.ContainsKey(cle))
                rexp.ValidationExpression = htFormat[cle].ToString();
            if (htLibelle.ContainsKey(cle))
                rexp.ErrorMessage = htLibelle[cle].ToString();
        }
        /// <summary>
        /// renseigne l'expression reguliere du password � partir du config.ini.xml
        /// </summary>
        /// <param name="revPWD"></param>
        public void SetValidationExpressionPassWord(int structureid, ref RegularExpressionValidator revPWD, string idpa, int eventId)
        {
            //MyDictionary mySSC = (MyDictionary)System.Web.HttpContext.Current.Session["SVarThemisIniFile"];
            MyDictionary mySSC = new MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(structureid);
            if (mySSC.Contains("PASSWORDVALIDATIONEXPRESSION"))
            {
                string strValidationExpression = mySSC["PASSWORDVALIDATIONEXPRESSION"];
                revPWD.ValidationExpression = strValidationExpression;


                /*   string xmlPath = Initialisations.GetKeyAppSettings("TraductionXmlCustomer");

                   logger.Debug("SetValidationExpressionPassWord - get cl� TraductionXmlCustomer dans web.config " + xmlPath);
                   if (string.IsNullOrEmpty(xmlPath))
                       logger.Error("key TraductionXmlCustomer n'existe pas");

              
                   string strMessage = utilitaires2010.SharedMethods.GetXmlMessageTranslateByLangOrPa(xmlPath, eventId, int.Parse(idpa), Initialisations.GetUserLanguage(), "error_format_password", logger);
                      */

                string strMessage = App_Code.Initialisations.GetMessageTranslate("error_format_password");
                //string strMessage = GetXmlNodeinNewIhmFile("TraductionXmlCustomer", "LoginCustomer", "ErrorFormatPWD");

                // GetXmlNodeinNewIhmFile("TraductionXmlCustomer", "ErrorFormatPWD");
                //if (strMessage == "") strMessage = GetXmlNodeinNewIhmFile("TraductionXmlGeneral", "ErrorFormatPWD");
                revPWD.ErrorMessage = strMessage;
            }
        }

        public string GetUrlCssGenerale(string sIdStructure)
        {
            string CssPaths = System.Configuration.ConfigurationManager.AppSettings["CssPaths"].ToString();
            CssPaths = CssPaths.Replace("[idstructure]", sIdStructure);
            int posLastPoint = CssPaths.LastIndexOf('.');
            string CssPathRacine = CssPaths.Substring(0, posLastPoint);
            string CssPathExtension = CssPaths.Substring(posLastPoint);

            // exemple dans Web.Config : <add key="CssPaths" value=".\DifferentesCss\[idstructure].css" />
            // la chaine [idstructure] est remplac�e par l'id du client

            if (!File.Exists(HttpContext.Current.Server.MapPath(CssPaths)))
            {   // fichier css n'existe pas 
                return null;
            }
            else
            {   // fichier Css trouv� : on l'applique
                return CssPaths;
            }
        }

        /// <summary>
        /// url du logo de la manif
        /// </summary>
        /// <param name="sIdStructure"></param>
        /// <param name="EventId"></param>
        /// <returns></returns>
        public string GetLogoPath(int sIdStructure, string EventId)
        {
            string cle = "imgLogo";
            if (htVisible[cle] != null && (bool)htVisible[cle] == true)
            {
                string ImagesPaths = System.Configuration.ConfigurationManager.AppSettings["ImagesPaths"].ToString();
                ImagesPaths = ImagesPaths.Replace("[idstructure]", sIdStructure.ToString("0000"));
                string ImagesPathsManif = ImagesPaths + @"manifestations/";

                if (File.Exists(HttpContext.Current.Server.MapPath(ImagesPathsManif + EventId + ".gif")))
                    return ImagesPathsManif + EventId + ".gif";
                else
                    return null;
            }
            else
                return null;
        }
        /// <summary>
        /// obtenir le chemin du fichier (htm)
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="templatefile"></param>
        /// <returns></returns>
        public string GetTemplateFilePath(string eventId, string idpa, string webConfigKey)
        {

            //string clePath = "./templates/default/";
            App_Code.Initialisations myInit = new App_Code.Initialisations();
            string Nom_Fichier = myInit.GetFile(webConfigKey, eventId, idpa, ".htm", true);
            return Nom_Fichier;
            //return System.Web.HttpContext.Current.Server.MapPath(clePath + templatefile);
        }
        /// <summary>
        /// obtenir le contenu du fichier
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="templatefile"></param>
        /// <returns></returns>
        public string GetTemplate(string eventId, string idpa, string webConfigKey, bool isAuthorized = true)
        {

            if (isAuthorized)
            {

                logger.Debug("GetTemplate " + eventId + " " + idpa + " " + webConfigKey);
                //string clePath = "./templates/default/";
                App_Code.Initialisations myInit = new App_Code.Initialisations();
                string Nom_Fichier = myInit.GetFile(webConfigKey, eventId, idpa, ".htm", true);
                // logger.Debug("GetTemplate Nom_Fichier " + Nom_Fichier);
                string templateLine = "";
                if (System.IO.File.Exists(Nom_Fichier))
                {
                    System.IO.StreamReader streamRead =
                     new System.IO.StreamReader(Nom_Fichier, System.Text.Encoding.UTF8);
                    System.Text.Encoding encoding = streamRead.CurrentEncoding;
                    templateLine = streamRead.ReadToEnd();
                    streamRead.Dispose();
                }
                return templateLine;
            }
            else
            {
                return "";
            }

        }






        /* public string GetUrlToMODescription(int sIdStructure, string eventId, string moId)
         {
             string templatefile = System.Configuration.ConfigurationManager.AppSettings["templateUrlToMoDescription"].ToString();
             templatefile = templatefile.Replace("[idmo]", moId);
             templatefile = templatefile.Replace("[idstructure]", sIdStructure.ToString("0000"));

             int posLastSlash = templatefile.LastIndexOf('/') + 1;
             string PathRacine = templatefile.Substring(0, posLastSlash);
             int posLastPoint = templatefile.LastIndexOf('.') + 1;
             string Filename = templatefile.Substring(posLastSlash, posLastPoint - posLastSlash - 1);
             string Extension = templatefile.Substring(posLastPoint);

            // Urls url = new Urls();


             string urltoreturn = url.GetRelativePath(PathRacine, Filename, Extension, int.Parse(eventId), true);




             return urltoreturn;

         }*/


        /// <summary>
        /// appliquer la Css du client
        /// </summary>
        /// <param name="sIdStructure"></param>
        /// <param name="StyleLink"></param>
        /// <param name="resulEvent"></param>
        public void SetVisualCss(int idStructure, ref HtmlLink StyleLink, int idEvent, Boolean detectMobile)
        {

            string physicalPathOfCss = App_Code.Initialisations.GetKeyAppSettings("physicalPathOfCss");
            physicalPathOfCss = physicalPathOfCss.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", idStructure));

            int ProfilAcheteur = 0;
            string strSuffixeLangue = string.Empty;

            if (System.Web.HttpContext.Current.Session["ProfilAcheteurId"] != null)
                ProfilAcheteur = int.Parse(System.Web.HttpContext.Current.Session["ProfilAcheteurId"].ToString());


            if (System.Web.HttpContext.Current.Session["SVarLangue"] != null)
            {
                strSuffixeLangue = "." + System.Web.HttpContext.Current.Session["SVarLangue"].ToString();
            }
            else
                strSuffixeLangue = "." + Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName;


            // physicalPathOfCss = physicalPathOfCss.Replace("[idstructure]", idStructure);
            //  int posLastPoint = CssPaths.LastIndexOf('/') + 1;
            //  string CssPathRacine = CssPaths.Substring(0, posLastPoint);

            //   Urls url = new Urls();

            bool hasFindUrl = false;
            List<string> LstOfFilesToFind = App_Code.Initialisations.GetListFilePathCss(physicalPathOfCss, idStructure, idEvent.ToString(), ProfilAcheteur.ToString(), strSuffixeLangue);


            string relativeUrlOfCss = App_Code.Initialisations.GetKeyAppSettings("relativePathCssOfSite");
            relativeUrlOfCss = relativeUrlOfCss.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", idStructure));

            foreach (var item in LstOfFilesToFind)
            {
                //FileToFind = Path.GetDirectoryName(FileToFind_temp) + "\\" + item;
                if (File.Exists(item))
                {
                    //  logger.Debug(item + " found");
                    StyleLink.Href = relativeUrlOfCss + Path.GetFileName(item);
                    hasFindUrl = true;
                }
            }

            if (!hasFindUrl)
            {
                StyleLink.Visible = false;
            }


            // string urlCssGenerale = url.GetRelativePath(CssPathRacine, sIdStructure, "css", resulEvent, detectMobile);

            //string urlCssGenerale =  GetUrlCssGenerale();
            /* if (urlCssGenerale != null)
                 StyleLink.Href = urlCssGenerale;
             else
                 StyleLink.Visible = false;*/


        }

        /// <summary>
        /// appliquer la banni�re du client
        /// </summary>
        /// <param name="sIdStructure"></param>
        /// <param name="imgBanner"></param>
        /// <param name="resulEvent"></param>
        public void SetBanner(string sIdStructure, ref Image imgBanner, int resulEvent, Boolean detectMobile)
        {
            // logger.Debug("SetBanner :" + sIdStructure +" - " + resulEvent);
            string imagesPaths = App_Code.Initialisations.GetKeyAppSettings("physicalPathOfImages");
            imagesPaths = imagesPaths.Replace("[idstructureSur4zeros]", sIdStructure);


            //  logger.Debug("SetBanner imagesPaths:" + imagesPaths  );
            // Urls url = new Urls();
            //  string bannerRelativePath = url.GetRelativePath(ImagesPaths, "banner", "gif", resulEvent, detectMobile);
            bool hasFindFile = false;

            if (Directory.Exists(imagesPaths))
            {
                //   logger.Debug("imagesPaths existe :" + imagesPaths);
                DirectoryInfo directory = new DirectoryInfo(imagesPaths);
                string[] extensionsBanner = new string[] { ".gif", ".png", ".jpg" };

                HashSet<string> allowedExtensions = new HashSet<string>(extensionsBanner, StringComparer.OrdinalIgnoreCase);
                var files = Array.FindAll(directory.GetFiles(), f => allowedExtensions.Contains(f.Extension));


                string relativeImagesPath = App_Code.Initialisations.GetKeyAppSettings("relativePathImagesOfSite");
                relativeImagesPath = relativeImagesPath.Replace("[idstructureSur4zeros]", sIdStructure);

                foreach (var item in files)
                {

                    string nameWithoutExtension = item.Name.Substring(0, item.Name.Length - item.Extension.Length);
                    //   logger.Debug("nameWithoutExtension "+ nameWithoutExtension);

                    if (nameWithoutExtension.ToLower() == "banner")
                    {
                        imgBanner.ImageUrl = relativeImagesPath + item.Name;
                        //    logger.Debug("imgBanner.ImageUrl " + imgBanner.ImageUrl);

                        hasFindFile = true;
                    }
                }

            }


            if (!hasFindFile)
            {
                imgBanner.Visible = false;
            }
        }

        /// <summary>
        /// remplace dans templateIni les cl�s par leur valeur dans IndentiteEntity 
        /// </summary>
        /// <param name="templateIni"></param>
        /// <param name="idEnt"></param>
        /// <returns></returns>
        public string ReplaceKeyByVal(string templateIni, IdentiteEntity idEnt)
        {
            string templateWork = templateIni;
            templateWork = ReplaceIndiferentCase(templateWork, "<ID>", idEnt.Identite_id.ToString());
            //nom de l'entreprise
            templateWork = ReplaceIndiferentCase(templateWork, "<identiteComplement>", idEnt.IdentiteComplement);
            templateWork = ReplaceIndiferentCase(templateWork, "<nom>", idEnt.SurName);
            templateWork = ReplaceIndiferentCase(templateWork, "<prenom>", idEnt.FirstName);
            templateWork = ReplaceIndiferentCase(templateWork, "<dob>", idEnt.DateOfBirthday.ToShortDateString());

            templateWork = ReplaceIndiferentCase(templateWork, "<address1>", idEnt.Address1);
            templateWork = ReplaceIndiferentCase(templateWork, "<address2>", idEnt.Address2);

            templateWork = ReplaceIndiferentCase(templateWork, "<adresse3>", idEnt.Address3);
            templateWork = ReplaceIndiferentCase(templateWork, "<adresse4>", idEnt.Address4);

            templateWork = ReplaceIndiferentCase(templateWork, "<codepostal>", idEnt.PostalCode);
            templateWork = ReplaceIndiferentCase(templateWork, "<ville>", idEnt.City);
            templateWork = ReplaceIndiferentCase(templateWork, "<pays>", idEnt.Country);

            templateWork = ReplaceIndiferentCase(templateWork, "<email>", idEnt.Email);


            string htmlTel = "";


            if (!string.IsNullOrEmpty(idEnt.PhoneNumber1))
                htmlTel += "<span class='tel tel1'>" + idEnt.PhoneNumber1 + "</span>";

            if (!string.IsNullOrEmpty(idEnt.PhoneNumber2))
                htmlTel += "<span class='tel tel2'>" + idEnt.PhoneNumber2 + "</span>";

            if (!string.IsNullOrEmpty(idEnt.PhoneNumber3))
                htmlTel += "<span class='tel tel3'>" + idEnt.PhoneNumber3 + "</span>";

            if (!string.IsNullOrEmpty(idEnt.PhoneNumber4))
                htmlTel += "<span class='tel tel4'>" + idEnt.PhoneNumber4 + "</span>";


            templateWork = ReplaceIndiferentCase(templateWork, "<tel>", htmlTel);

            /*  templateWork = ReplaceIndiferentCase(templateWork, "<tel1>", idEnt.PhoneNumber1);
              templateWork = ReplaceIndiferentCase(templateWork, "<tel2>", idEnt.PhoneNumber2);
              templateWork = ReplaceIndiferentCase(templateWork, "<tel3>", idEnt.PhoneNumber3);
              templateWork = ReplaceIndiferentCase(templateWork, "<tel4>", idEnt.PhoneNumber4);
              */


            return templateWork;
        }

        /// <summary>
        /// remplace dans templateIni les cl�s par leur valeur dans BasketEntity 
        /// </summary>
        /// <param name="templateIni"></param>
        /// <param name="idEnt"></param>
        /// <returns></returns>
        public string ReplaceKeyByVal(string templateIni, BasketEntity idEnt)
        {
            string templateWork = templateIni;

            templateWork = ReplaceIndiferentCase(templateWork, "<panierid>", idEnt.BasketId.ToString());

            templateWork = ReplaceIndiferentCase(templateWork, "<datepaiement>", idEnt.Date_paiement.ToShortDateString() + " " + idEnt.Date_paiement.ToShortTimeString());
            templateWork = ReplaceIndiferentCase(templateWork, "<dateoperation>", idEnt.Date_operation.ToShortDateString() + " " + idEnt.Date_operation.ToShortTimeString());

            templateWork = ReplaceIndiferentCase(templateWork, "<cardnumber>", idEnt.Card_number);
            templateWork = ReplaceIndiferentCase(templateWork, "<cardtype>", idEnt.Card_type);
            templateWork = ReplaceIndiferentCase(templateWork, "<certificate>", idEnt.Certificate);
            templateWork = ReplaceIndiferentCase(templateWork, "<webtransact>", idEnt.Transaction_id);

            return templateWork;
        }

        public string ReplaceIndiferentCase(string stringIn, string key, string value)
        {
            return stringIn.Replace(key.ToLower(), value).Replace(key.ToUpper(), value);
        }



        //public void SetSocialDiv(string sIdStructure, ref HtmlGenericControl socialDiv, int resulEvent)
        //{
        //    string SocialDivPaths = System.Configuration.ConfigurationManager.AppSettings["SocialDivPaths"].ToString();
        //    SocialDivPaths = SocialDivPaths.Replace("[idstructure]", sIdStructure);

        //    Urls url = new Urls();
        //    string bannerRelativePath = url.GetRelativePath(SocialDivPaths, "socialDiv", "html", resulEvent);


        //}





    }
}
