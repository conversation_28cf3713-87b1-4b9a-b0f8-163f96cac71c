﻿
SELECT Distinct m.manifestation_id as eventId, manifestation_nom as eventName, s.seance_id as sessionId, 
seance_date_deb as sessionStartDate, 
l.lieu_nom as placeName,l.lieu_id as placeId, lc.lieu_config_id,lc.lieu_config_nom,
m.manifestation_groupe_id, m.ID_genre as sous_genre_id, mgg.id as genre_id,
SUM(distinct  case sc.Cible_id when 0 then 0  else POWER(2,sc.Cible_id) end) as cible,
 resume_manif, mise_en_scene, distribution, duree, horaire, 
 case s.NEPASAFFICHERDATE 
 when 'O' then 0
 when 'N' then 1
 when '' then 1
 when null then 1
 END
 as afficherLaDate
FROM seance s 
INNER JOIN manifestation m ON m.manifestation_id = s.manifestation_id
inner join manifestation_infos mi on mi.manifestation_id = s.manifestation_id

INNER JOIN lieu l ON l.lieu_id=s.lieu_id
INNER JOIN lieu_configuration lc ON lc.lieu_config_id=s.lieu_config_id
LEFT OUTER JOIN manifestation_genre mg ON m.ID_genre = mg.id
LEFT OUTER JOIN manifestation_groupe_genre mgg ON mgg.id = mg.groupe_id
LEFT OUTER JOIN seance_cible sc on sc.Seance_id=s.seance_Id 
LEFT OUTER JOIN cible c on c.id=sc.cible_id 
WHERE
s.seance_date_deb > getdate() 
and s.seance_date_deb < dateadd(year,4, getdate() )
 group by m.manifestation_id , manifestation_nom, 
seance_date_deb ,  s.seance_id,
l.lieu_nom , l.lieu_id  , lc.lieu_config_id,lc.lieu_config_nom,
m.manifestation_groupe_id, m.ID_genre  , mgg.id  ,
resume_manif, mise_en_scene, distribution, duree, horaire, NEPASAFFICHERDATE
order by manifestation_nom, seance_date_deb