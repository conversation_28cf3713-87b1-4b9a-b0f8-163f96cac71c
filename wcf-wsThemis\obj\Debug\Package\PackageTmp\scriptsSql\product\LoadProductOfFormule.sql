﻿SELECT DISTINCT
             ap.produit_id as ProductID
            ,produit_nom as ProductName
            ,produit_code as ProductCode
            ,nbmin as NbProduct
            ,jauge as Capacity
            ,restant as Remainder
            ,(ps.montant1 + ps.montant2)*100 as TotalAmount
            , ps.montant2*100 as Charge
			,ap.formule_id as formulaId
			,p.maquettebillet_id as TicketModelID
			,p.pref_affichage
FROM abonnement_produit ap 
INNER JOIN produit p ON p.produit_id = ap.produit_id 
INNER JOIN produit_stock ps ON ps.produit_id = ap.produit_id
WHERE ap.formule_id IN ([listformulasID])
ORDER BY p.pref_affichage

