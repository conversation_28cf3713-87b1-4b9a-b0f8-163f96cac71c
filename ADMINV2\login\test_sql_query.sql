-- Test de la requête SQL pour vérifier que la colonne numero_billets fonctionne
-- Re<PERSON>lacez les valeurs par vos données de test

DECLARE @datedeb varchar(18); 
SET @datedeb='20240101 00:00:00';

DECLARE @datefin varchar(18);
SET @datefin='20241231 23:59:59';

SELECT TOP 10 
    '' as img, 
    '' as imgaction, 
    web_user_id, 
    p.identite_id, 
    p.panier_id, 
    p.structure_id, 
    p.date_operation, 
    '' as sdate_operation, 
    p.etat, 
    commande_id, 
    transaction_id, 
    certificate, 
    card_number, 
    card_type, 
    email, 
    date_paiement,
    '' as sdate_paiement,
    haspdf = CASE WHEN sum(isnull(pe.maquette_id,0)) + SUM(isnull(pea.maquette_id, 0)) + SUM(isnull(pfte.maquette_id,0)) > 0 THEN 1 
        ELSE 0 
        END,
    NULLIF(STUFF((SELECT DISTINCT ', ' + CAST(NULLIF(r.numbille<PERSON>,'') AS VARCHAR) FROM panier_entree pe2
       LEFT JOIN recette r ON r.entree_id = pe2.entree_id
       WHERE pe2.panier_id = p.panier_id AND r.numbillet IS NOT NULL AND r.numbillet != ''
       FOR XML PATH('')), 1, 2, ''), '') AS numero_billets
FROM panier p
LEFT OUTER JOIN panier_entree pe on p.panier_id=pe.panier_id
LEFT OUTER JOIN panier_entree_abo pea on p.panier_id=pea.panier_id
LEFT OUTER JOIN panier_formule_tarif pft on pft.panier_id = p.panier_id
LEFT OUTER JOIN panier_formule_tarif_entree pfte on pft.panier_formule_tarif_id = pfte.panier_formule_tarif_id
INNER JOIN panier_request pr on pr.panier_id = p.panier_id 
WHERE p.date_operation > cast(@datedeb as datetime) 
AND p.date_operation < cast(@datefin as datetime) 
AND p.structure_id = 1  -- Remplacez par votre ID de structure
GROUP BY
    p.identite_id, p.panier_id, 
    p.structure_id, p.date_operation, p.etat, commande_id, transaction_id, 
    certificate, card_number, card_type, email, date_paiement, web_user_id 
ORDER BY panier_id DESC;
