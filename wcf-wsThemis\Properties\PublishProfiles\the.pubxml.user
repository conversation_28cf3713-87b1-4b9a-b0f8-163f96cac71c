<?xml version="1.0" encoding="utf-8"?>
<!--
<PERSON> fichier est utilisé par le processus de publication/package de votre projet web. Vous pouvez personnaliser le comportement de ce processus
en modifiant ce fichier MSBuild. Pour en savoir plus à ce sujet, visitez https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project>
  <PropertyGroup>
    <_PublishTargetUrl>D:\PUBLISH\WCFTHEMIS</_PublishTargetUrl>
    <History>True|2025-10-08T09:20:35.4429733Z||;</History>
    <LastFailureDetails />
  </PropertyGroup>
  <ItemGroup>
    <File Include="bin/BCrypt.Net.dll">
      <publishTime>11/21/2013 17:53:24</publishTime>
    </File>
    <File Include="bin/BCrypt.Net.pdb">
      <publishTime>11/21/2013 17:53:24</publishTime>
    </File>
    <File Include="bin/EntityFramework.dll">
      <publishTime>03/02/2015 10:32:26</publishTime>
    </File>
    <File Include="bin/EntityFramework.SqlServer.dll">
      <publishTime>03/02/2015 10:32:26</publishTime>
    </File>
    <File Include="bin/ISO3166.dll">
      <publishTime>05/19/2022 10:57:22</publishTime>
    </File>
    <File Include="bin/log4net.dll">
      <publishTime>05/07/2025 19:29:54</publishTime>
    </File>
    <File Include="bin/log4net.pdb">
      <publishTime>05/07/2025 19:29:54</publishTime>
    </File>
    <File Include="bin/Microsoft.IdentityModel.Abstractions.dll">
      <publishTime>10/17/2023 04:21:22</publishTime>
    </File>
    <File Include="bin/Microsoft.IdentityModel.JsonWebTokens.dll">
      <publishTime>10/17/2023 03:53:36</publishTime>
    </File>
    <File Include="bin/Microsoft.IdentityModel.Logging.dll">
      <publishTime>10/17/2023 03:55:58</publishTime>
    </File>
    <File Include="bin/Microsoft.IdentityModel.Tokens.dll">
      <publishTime>10/17/2023 04:01:22</publishTime>
    </File>
    <File Include="bin/Microsoft.Web.Infrastructure.dll">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="bin/MySqlConnector.dll">
      <publishTime>11/12/2024 13:50:28</publishTime>
    </File>
    <File Include="bin/Newtonsoft.Json.dll">
      <publishTime>03/17/2021 19:03:36</publishTime>
    </File>
    <File Include="bin/QRCodeEncoderLibrary.dll">
      <publishTime>07/23/2019 16:44:28</publishTime>
    </File>
    <File Include="bin/RestSharp.dll">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="bin/scriptsSql/abonnement/LoadGestionPlaceParentOfFormulas.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="bin/Swagger.Net.dll">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="bin/Swagger.Net.pdb">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="bin/System.Buffers.dll">
      <publishTime>02/25/2025 17:42:24</publishTime>
    </File>
    <File Include="bin/System.Diagnostics.DiagnosticSource.dll">
      <publishTime>02/25/2025 17:42:24</publishTime>
    </File>
    <File Include="bin/System.IdentityModel.Tokens.Jwt.dll">
      <publishTime>10/17/2023 03:52:14</publishTime>
    </File>
    <File Include="bin/System.Memory.dll">
      <publishTime>02/25/2025 17:42:24</publishTime>
    </File>
    <File Include="bin/System.Numerics.Vectors.dll">
      <publishTime>02/25/2025 17:42:24</publishTime>
    </File>
    <File Include="bin/System.Runtime.CompilerServices.Unsafe.dll">
      <publishTime>02/25/2025 17:42:24</publishTime>
    </File>
    <File Include="bin/System.Text.Encodings.Web.dll">
      <publishTime>02/16/2021 20:13:28</publishTime>
    </File>
    <File Include="bin/System.Threading.Tasks.Extensions.dll">
      <publishTime>02/25/2025 17:42:24</publishTime>
    </File>
    <File Include="bin/Themis.Libraries.DTO.dll">
      <publishTime>09/09/2025 09:03:13</publishTime>
    </File>
    <File Include="bin/Themis.Libraries.DTO.dll.config">
      <publishTime>07/28/2025 17:01:35</publishTime>
    </File>
    <File Include="bin/Themis.Libraries.DTO.pdb">
      <publishTime>09/09/2025 09:03:13</publishTime>
    </File>
    <File Include="bin/Themis.Libraries.Utilities.dll">
      <publishTime>08/06/2025 17:14:56</publishTime>
    </File>
    <File Include="bin/Themis.Libraries.Utilities.pdb">
      <publishTime>08/06/2025 17:14:56</publishTime>
    </File>
    <File Include="bin/utilitaires2010.dll">
      <publishTime>10/01/2025 10:03:56</publishTime>
    </File>
    <File Include="bin/utilitaires2010.dll.config">
      <publishTime>07/28/2025 17:20:33</publishTime>
    </File>
    <File Include="bin/utilitaires2010.pdb">
      <publishTime>10/01/2025 10:03:56</publishTime>
    </File>
    <File Include="bin/wcf-wsThemis.dll">
      <publishTime>10/08/2025 11:20:34</publishTime>
    </File>
    <File Include="bin/wcf-wsThemis.pdb">
      <publishTime>10/08/2025 11:20:34</publishTime>
    </File>
    <File Include="bin/WebActivator.dll">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="bin/ws_bll.dll">
      <publishTime>10/01/2025 10:05:59</publishTime>
    </File>
    <File Include="bin/ws_bll.dll.config">
      <publishTime>07/28/2025 16:56:42</publishTime>
    </File>
    <File Include="bin/ws_bll.pdb">
      <publishTime>10/01/2025 10:05:59</publishTime>
    </File>
    <File Include="bin/ws_DTO.dll">
      <publishTime>09/26/2025 09:17:12</publishTime>
    </File>
    <File Include="bin/ws_DTO.dll.config">
      <publishTime>07/28/2025 17:01:35</publishTime>
    </File>
    <File Include="bin/ws_DTO.pdb">
      <publishTime>09/26/2025 09:17:12</publishTime>
    </File>
    <File Include="Global.asax">
      <publishTime>07/28/2025 16:49:48</publishTime>
    </File>
    <File Include="log4netconfig.xml">
      <publishTime>07/28/2025 16:49:48</publishTime>
    </File>
    <File Include="packages.config">
      <publishTime>07/28/2025 16:49:48</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/createProS_[dbo].[SP_WS_LoadListAllFormuleTarif]">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/LoadContraintesGroupOfFormula.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/LoadEventsHorsAbo.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/LoadEventsOfFormula.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/LoadFormulas.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/LoadGestionPlaceParentOfFormulas.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/LoadGroupFormulas.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/LoadPriceOfFormulas.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/SelectCurrentBasket.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/SelectCurrentFormulesBasket.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/SelectCurrentHorsFormulesBasket.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/abonnement/SelectSeancesListOfBasket.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/buyerProfil/getDistinctsCmdsWT.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/buyerProfil/getHisto.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/buyerProfil/_createProStock.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/consumers/AttachedConsumer.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/CoupeFile/SelectIdentiteWithCommande.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/distanciation/GetDistanciation.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/events/getManifestationInfos.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/events/LoadEventsAndSessionInGp.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/events/LoadEventsForToday.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/events/LoadEvents_listeManifsFiliere.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/events/LoadEvents_listeManifsIndiv.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/events/propertiesOfEvents.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/cacheRefreshManager.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/create[gestion_place_multiplicateur_nbmax].sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/create[SP_WS_GETGRILLEALLTARIFS_BIGONE].sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/create[SP_WS_GETOFFRES_TOTABLE].sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/flagAbos.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/flagAbosFermeMemePlace.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/flagOneSeatTempo.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/getReservesAbo.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/LoadAllInternetGrilleTarif.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/LoadAllInternetGrilleTarif_dispoInGp.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/LoadGrilleTarif.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/LoadGrilleTarifHA.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/LoadGrilleTarifPlaceSupp.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/ReflagSeatsTempo.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/UnflagOneSeatTempo.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/UnFlagOnSession.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/gestionPlacesScripts/UnflagSeatsTempo.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/grilletarif/grilleTarif.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/consommateursGet.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/DeleteIdentiteConsommateur.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/DeleteMyAccount.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/getIdentiteForPatHome.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/InsertIdentite.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/InsertIdentiteComplement.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/InsertIdentiteComplementWeb.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/InsertIdentiteConsommateur.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/InsertIdentiteInfosComp.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/InsertIdentiteInfosCompPhysique.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/InsertListInfoCompOnIdentite.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/SelectCivilityNaming.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/SelectFanCard.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/SelectIdentite.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/SelectInfoCompOnIdentite.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/SelectTitre.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/UpdateIdentite.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/UpdateIdentiteComplement.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/UpdateIdentiteComplementWeb.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/UpdateIdentiteInfosComp.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/UpdateOrInsertIdentiteInfosComp.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/identite/VerifExistingEmail.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/infoComp/InsertInfoComp.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/language/loadGlobalLanguage.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/language/loadlanguages.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/paiement/getAcomptesIdentite.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/paiement/getmodepaiementCmd.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/paiement/getModespaiementForprofilAcheteur.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/paiement/markCommandWait.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/planSalle/loadPlanSalle.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/planSalle/loadPlanSalleTexts.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/product/LoadCommonsMOofFormulas.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/product/LoadFraisProductOfFormule.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/product/LoadMaquettesOfGpByMoId.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/product/LoadProductOfFormule.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/product/LoadQuestionnaireProductsOfFormulas.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/reservations/createBasketReservation.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/reservations/getInfoResaForBasket.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/reservations/getReservationsOfIdentity.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/reservations/reservationDetail.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/seances/getLieuDescriptionBySeance.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/seances/getLieuOfSeance.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/seances/getSeanceInfos.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/seances/SelectSeanceAndLieu.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/waitList/DeleteIdentiteOfWaitList.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/waitList/InsertListeAttente.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/waitList/SelectListeAttenteOfIdentite.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="scriptsSql/waitList/SelectManifestationsAndSeancesWaitList.sql">
      <publishTime>07/28/2025 16:49:49</publishTime>
    </File>
    <File Include="wcf-wsThemis.svc">
      <publishTime>07/28/2025 16:49:48</publishTime>
    </File>
    <File Include="Web.config">
      <publishTime>10/08/2025 11:20:34</publishTime>
    </File>
  </ItemGroup>
</Project>