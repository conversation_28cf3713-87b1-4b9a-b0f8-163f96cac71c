﻿SELECT mode_paiement_id, cc_solde*100 as cc_solde, cc_numpaiement 
 FROM compte_client cc
WHERE cc_intitule_operation in ('MACPTE','MACPTANNU') and
identite_id=[Identite_ID]
-- and commande_id=0 and manif_id=0 ??
 AND mode_paiement_id IN ([List_ModesPaiement],1)
and cc_credit>isnull(  
(  
	SELECT SUM(cc_debit) FROM compte_client  
	WHERE identite_id=[Identite_ID] AND cc_intitule_operation ='RACPTE'   
	and num_acompte= cc.cc_numpaiement  
),0) and cc_solde>0