﻿
SELECT 
case when iic.supprimer is null
 then 'PASVU' 
else 
	 case iic.supprimer 
	 when 'O' then 'SUPP' 
	 else 'VALIDE'
	 end 
end as state,

   id, libelle, identite_id, ic.info_comp_id, supprimer, valeur1, valeur2, valeur3, valeur4, type_info_comp, valeur_type,datecreation
FROM info_comp   ic
LEFT OUTER JOIN identite_infos_comp iic on ic.info_comp_id = iic.info_comp_id and iic.identite_id=[IDENTITEID]
where   ic.info_comp_id in ([LISTINFOCOMP])
order by datecreation desc;
