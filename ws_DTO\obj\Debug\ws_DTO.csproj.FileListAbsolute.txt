D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\ws_DTO.dll.config
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\ws_DTO.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\ws_DTO.pdb
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\DTO.XML
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\Themis.Libraries.DTO.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.Text.Json.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.Memory.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.IO.Pipelines.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.Threading.Tasks.Extensions.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.ValueTuple.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.Text.Encodings.Web.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.Numerics.Vectors.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.Buffers.dll
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\Themis.Libraries.DTO.pdb
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\Themis.Libraries.DTO.dll.config
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.Text.Json.xml
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.IO.Pipelines.xml
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\System.Text.Encodings.Web.xml
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
D:\WORK\VIEUXPROJETS\ws_DTO\obj\Debug\ws_DTO.csproj.AssemblyReference.cache
D:\WORK\VIEUXPROJETS\ws_DTO\obj\Debug\ws_DTO.csproj.CoreCompileInputs.cache
D:\WORK\VIEUXPROJETS\ws_DTO\obj\Debug\ws_DTO.csproj.Up2Date
D:\WORK\VIEUXPROJETS\ws_DTO\obj\Debug\ws_DTO.dll
D:\WORK\VIEUXPROJETS\ws_DTO\obj\Debug\ws_DTO.pdb
D:\WORK\VIEUXPROJETS\ws_DTO\bin\Debug\Newtonsoft.Json.dll
