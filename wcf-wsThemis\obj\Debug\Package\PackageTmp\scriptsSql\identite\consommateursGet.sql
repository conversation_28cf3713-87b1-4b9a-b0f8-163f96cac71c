﻿select * from (
	select  donneur_ordre_identite_id,  consomateur_identite_id, identite_id,  identite_nom, identite_prenom, postal_rue1, postal_rue2, postal_rue3, postal_rue4, postal_cp,
	postal_region, postal_pays, postal_ville, postal_tel1,postal_tel2,postal_tel3,postal_tel4,postal_tel5,postal_tel6,postal_tel7,appellation_id,identite_date_naissance,FicheSupprimer, filiere_id
	from groupe_consomateur gc
	inner join identite i on i.identite_id=gc.consomateur_identite_id
	 WHERE  i.FicheSupprimer <> 'O' AND donneur_ordre_identite_id=[IDENTITE_ID] 
	union
	select [IDENTITE_ID]  as donneur_ordre_identite_id, [IDENTITE_ID]  as consomateur_identite_id, identite_id,  identite_nom, identite_prenom, postal_rue1, postal_rue2, postal_rue3, postal_rue4, postal_cp,
	postal_region, postal_pays, postal_ville, postal_tel1,postal_tel2,postal_tel3,postal_tel4,postal_tel5,postal_tel6,postal_tel7,appellation_id,identite_date_naissance,FicheSupprimer, filiere_id
	from identite i 
	 WHERE  i.FicheSupprimer <> 'O' AND i.identite_id=[IDENTITE_ID]  
	 union
	 select [IDENTITE_ID]    as donneur_ordre_identite_id, [IDENTITE_ID]   as consomateur_identite_id, identite_id,  identite_nom, identite_prenom, postal_rue1, postal_rue2, postal_rue3, postal_rue4, postal_cp,
	postal_region, postal_pays, postal_ville, postal_tel1,postal_tel2,postal_tel3,postal_tel4,postal_tel5,postal_tel6,postal_tel7,appellation_id,identite_date_naissance,FicheSupprimer, filiere_id
	from identite i 
	 WHERE  i.FicheSupprimer <> 'O' AND 
	 i.identite_id=[IDENTITE_ID]  

) as identconsom
ORDER BY identite_nom, identite_prenom