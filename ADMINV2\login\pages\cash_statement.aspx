﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Principale.Master" AutoEventWireup="true"
    CodeBehind="cash_statement.aspx.cs" Inherits="login.pages_stats.cash_statement" culture="auto" meta:resourcekey="PageResource1" uiculture="auto" %>

<asp:Content ID="Content1" ContentPlaceHolderID="HeadContent" runat="server">
    <!-- Inclusion du fichier de traduction des colonnes -->
    <script src="../assets/js/column-header-translation.js" type="text/javascript"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
  <%--  <div id="error">
        <asp:Label ID="lblError" CssClass="hidden" runat="server" 
            meta:resourcekey="lblErrorResource1"></asp:Label>
    </div>--%>
    <asp:Label ID="lblErrorSelectManifs" runat="server" Text="Sélectionnez au moins une manifestation" meta:resourcekey="lblErrorSelectManifsResource1" ClientIDMode="Static" CssClass="hidden"></asp:Label>

     
    <div class="widget-box">
        <div class="widget-header widget-header-blue widget-header-flat">
            <h4 class="lighter"><span data-trad="statment_fund">Etat de caisse</span> </h4>
        </div>
        <div class="widget-body">
            <div class="widget-main">
                <div id="fuelux-wizard" class="row" data-target="#step-container">
                    <ul class="wizard-steps">
                        <li data-target="#step1" class="active"><span class="step">1</span>
                            <span class="title"><label data-trad="title_manifestations"></label></span> 
                        </li>
                        <li data-target="#step2"><span class="step">2</span> 
                            <span class="title"><label data-trad="title_tarif"></label></span> 
                        </li>
                        <li data-target="#step3"><span class="step">3</span> 
                            <span class="title"><label data-trad="title_date"></label></span>
                        </li>
                        <li data-target="#step4"><span class="step">4</span> 
                            <span class="title"><label data-trad="title_resume"></label></span> 
                        </li>
                    </ul>
                </div>
                <hr />
                <div class="row wizard-actions">
                  <button class="btn btn-primary btn-export" disabled="disabled" id="btnPdfExport" type="button">
                        <i class="icon-download"></i>
                        <label data-trad="export_pdf"></label>
                    </button>
           

                    <button class="btn btn-primary btn-export" disabled="disabled"  id="btnExcelExport" type="button" >
                        <i class="icon-download"></i>
                        <label data-trad="export_xls"></label>
                    </button>

                    <button class="btn btn-prev" disabled="disabled" type="button">
                        <i class="icon-arrow-left"></i>
                        <span data-trad="previous"></span>
                    </button>
                    <button class="btn btn-success btn-next" data-last="Finish" type="button" id="btnNext">
                        <span data-trad="next"></span>
                        <i class="icon-arrow-right icon-on-right"></i>
                    </button>
                </div>
                <div class="step-content row position-relative" id="step-container">
                    <div class="step-pane active" id="step1">
                        <div class="col-lg-11  col-lg-offset-1">
                            <div class="widget-box">
                                <div class="widget-header header-color-blue2">
                                    <h4 class="lighter white "><span data-trad="select_all_manifestations">Sélectionnez les manifestations</span></h4>
                                     <div class="widget-toolbar">
                                        <label>
                                            <small class="lighter white ">
                                                <b>
                                                    <label data-trad="activer"></label>
                                                    /
                                           <label data-trad="desactiver"></label>
                                                </b></small>
                                            <input id="id-check-futuresessions" type="checkbox" checked class="ace ace-switch ace-switch-6" />
                                            <span class="lbl"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="widget-body">
                                    <div class="widget-body-inner" style="display: block;">
                                        <div class="widget-main" id="optionEventsDates">
                                            <div class="form-group col-lg-5">
                                                <label class="col-lg-4 control-label" data-trad="date_debut"></label>
                                                <input type="date" class="controls" id="fromEventDate" />
                                            </div>
                                            <label class="col-lg-1 control-label" data-trad="date_fin"></label>
                                            <input type="date" class="controls" id="toEventDate" />


                                            <button class="btn btn-primary" id="btnEventsDatesValid" type="button">
                                                <i class="icon-search"></i>
                                                <label data-trad="valider">Valider</label>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="widget-body">
                                    <div class="widget-main padding-8">
                                      <%--  <input type="text" name="search" value="" id="id_search_list">
                                        --%>
                                         <button class="btn btn-success "  type="button" id="btn_select_manifs" data-trad="select_all_manifestations"></button>
                                        <!-- tree view -->
                                        <div id="Eventstree">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="step-pane" id="step2">
                        <div class="col-lg-12 ">
                            <div class="widget-box">
                                <div class="widget-header header-color-blue2">
                                    <h4 class="lighter white ">
                                        <span data-trad="title_tarif"></span> 
                                    </h4>
                                </div>
                                <div class="widget-body">
                                     <div class="row">
                                        <div class="col-lg-12 ">
                                            <button class="btn btn-success btn-block" type="button" id="select_all_prices" data-state="uncheck" data-trad="select_all_prices">Sélectionnez tous les tarifs</button>
                                        </div>
                                    </div>
                                    <div class="widget-main padding-8" id="tarifManifs">
                                        <%-- Ici sera intégrer la liste des manifs avec une checkbox a coté--%>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="step-pane" id="step3">
                        <div class="widget-box">
                            <div class="widget-header header-color-blue2">
                               <h4 class="lighter white">
                                    <label data-trad="title_active_resume"></label>
                                </h4>
                                <div class="widget-toolbar">
                                    <label>
                                       <small class="lighter white ">
                                        <b>
                                           <label data-trad="activer"></label> /
                                           <label  data-trad="desactiver"></label>
                                        </b></small>
                                        <input id="id-check-horizontal" type="checkbox" class="ace ace-switch ace-switch-6" />
                                        <span class="lbl"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="widget-body">
                                <div class="widget-body-inner" style="display: block;">
                                    <div class="widget-main" id="optionDates">
                                        <div class="form-group col-lg-5">
                                            <label class="col-lg-4 control-label" data-trad="date_debut"></Label>
                                            <input type="text" class="controls" id="fromDate" disabled="true" />
                                        </div>
                                            <label class="col-lg-1 control-label" data-trad="date_fin"></Label>
                                        <input type="text" class="controls" id="toDate" disabled="true" />
                                        
                                    </div>
                                </div>
                            </div>
                    </div>

                    <div class="space-10"></div>

                    <div class="widget-box">
                            <div class="widget-header header-color-blue2">
                                <h4 class="lighter white" >
                                    <label data-trad="title_choix_etats"></label>
                                </h4>
                            </div>

                            <div class="widget-body">
                                <div class="widget-body-inner" style="display: block;">
                                    <div class="widget-main" >
                                       <div class="checkbox"><label><input type="checkbox" class="ace" value="R" name="etat" /> <span class="lbl" data-trad="etat_reserver"> </span> </label> </div>
                                       <div class="checkbox"><label><input type="checkbox" class="ace" value="P" name="etat" /> <span class="lbl" data-trad="etat_payer"> </span> </label> </div>
                                       <div class="checkbox"><label><input type="checkbox" class="ace" value="B" name="etat" /> <span class="lbl" data-trad="etat_editer"> </span> </label> </div>
<%--                                       <div class="checkbox"><label><input type="checkbox" class="ace" value="D" name="etat" /> <span class="lbl" data-trad="etat_dupliquer"> </span> </label> </div>--%>

                                    </div>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="step-pane" id="step4">

            <div id="resume"></div>
            <div class="dataTables">
                <%--ici sera toutes les tables pour les états--%>
            </div>
        </div>
        <hr />
    </div>
    <!-- /widget-main -->
    </div>
    <!-- /widget-body -->
    

      <iframe id="iframe" style="display:none"></iframe>

    <script src="../assets/js/quickSearch/jquery.quicksearch.js" type="text/javascript"></script>
     <script src="../assets/js/date-time/jquery.datetimepicker.js" type="text/javascript"></script>
    <script src="../assets/js/date-time/dataTimePickerCustom.js" type="text/javascript"></script>
      <script src="../assets/js/fuelux/fuelux.wizard.min.js" type="text/javascript"></script>
    <script src="../assets/js/ace-elements.min.js" type="text/javascript"></script>
    <script src="../assets/js/bibliotheque_commune.js" type="text/javascript"></script>
    <script src="../assets/js/pages/cash_statement.js" type="text/javascript"></script>

   
</asp:Content>
