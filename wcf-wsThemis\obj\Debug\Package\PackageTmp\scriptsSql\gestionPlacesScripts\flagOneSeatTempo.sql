﻿
update entree_[eventID] set flag_selection='[flagCode]', dateoperation=getdate() WHERE entree_id=[seatID] and seance_id =[sessionID] and entree_etat='L' and (flag_selection is null OR flag_selection='')
select * FROM entree_[eventID] WHERE entree_id=[seatID] and seance_id =[sessionID] and entree_etat='L' and flag_selection='[flagCode]'

 --sp_ws_autoplaces 'entree_[eventID]', [sessionID], [categID], '[listReservesID]', [nbrToFlag], 'T[userID]', 0,0,0,0,0