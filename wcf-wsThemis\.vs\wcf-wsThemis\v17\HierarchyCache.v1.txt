﻿++Solution ' wcf-wsThemis ' ‎ (8 sur 8 de projets)
i:{00000000-0000-0000-0000-000000000000}:wcf-wsThemis.sln
++WebTracing2010
i:{00000000-0000-0000-0000-000000000000}:WebTracing2010
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++Properties
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\properties\
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\properties\
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\properties\
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\properties\
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\properties\
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\properties\
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\properties\
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\properties\
++Références
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++app.config
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\app.config
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\app.config
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\app.config
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\app.config
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\app.config
++BasketLineEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\basketlineentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\basketlineentity.cs
++BasketManager.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\basketmanager.cs
++Baskets.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\baskets.cs
++CategoryPriceEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\categorypriceentity.cs
++ConnexionEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\connexionentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\connexionentity.cs
++ConnexionManager.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\connexionmanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\connexionmanager.cs
++ConnexionTypeEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\connexiontypeentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\connexiontypeentity.cs
++EventEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\evententity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\evententity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\eventssessions\evententity.cs
++EventsGroupsEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\eventsgroupsentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\eventsgroupsentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\eventsgroupsentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\eventssessions\eventsgroupsentity.cs
++FileAttente.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\fileattente.cs
++FormulaGroupeEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\formulagroupeentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\formulagroupeentity.cs
++FormulaGroupeManager.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\formulagroupemanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\formulagroupemanager.cs
++GestionTrace.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\gestiontrace.cs
++GroupUsersEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\groupusersentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\groupusersentity.cs
++LieuEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\lieuentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\lieuentity.cs
++ProductEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\productentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\productentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\productentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\products\productentity.cs
++ProductEntityManager.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\productentitymanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\productentitymanager.cs
++ProfilAcheteurOfAdministratorEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\profilacheteurofadministratorentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\profilacheteurofadministratorentity.cs
++RevendeurEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\revendeurentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\revendeurentity.cs
++RevendeurManager.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\revendeurmanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\revendeurmanager.cs
++SeatAboEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\seataboentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\seataboentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\seats\seataboentity.cs
++SeatEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\seatentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\seatentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\seats\seatentity.cs
++SeatFormulaGroupeEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\seatformulagroupeentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\seatformulagroupeentity.cs
++SeatFormulaGroupeManager.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\seatformulagroupemanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\seatformulagroupemanager.cs
++SeatUnitSalesEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\seatunitsalesentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\seatunitsalesentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\seats\seatunitsalesentity.cs
++SessionEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\sessionentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\sessionentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\eventssessions\sessionentity.cs
++StructureEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\structureentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\structureentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\structureentity.cs
++StructuresManager.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\structuresmanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\structuresmanager.cs
++UserOnStructuresEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\useronstructuresentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\useronstructuresentity.cs
++UserOnStructuresManager.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\useronstructuresmanager.cs
++UsersEntity.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\usersentity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\usersentity.cs
++UsersManager.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\usersmanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\usersmanager.cs
++WebTracingManager.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\webtracingmanager.cs
++WebUser.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\webuser.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\webuser.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\wtobjects\webuser.cs
++WTDataBase.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\wtdatabase.cs
++AssemblyInfo.cs
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:d:\work\vieuxprojets\webtracing2010\properties\assemblyinfo.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\properties\assemblyinfo.cs
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\properties\assemblyinfo.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\properties\assemblyinfo.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\properties\assemblyinfo.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\properties\assemblyinfo.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\properties\assemblyinfo.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\properties\assemblyinfo.cs
++Microsoft.CSharp
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++OpenEntity2010
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{00000000-0000-0000-0000-000000000000}:OpenEntity2010
++System
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.configuration
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Core
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Data
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Data.DataSetExtensions
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Runtime.Serialization
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Web
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Xml
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Xml.Linq
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++utilitaires2010
i:{a2facd66-71d3-489a-9ba0-beb74ac55956}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:
i:{00000000-0000-0000-0000-000000000000}:utilitaires2010
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++ws_DTO
i:{00000000-0000-0000-0000-000000000000}:ws_DTO
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++adhesion_offres
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\adhesion_offres\
++distanciation
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\distanciation\
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\distanciation\
++mailing
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\mailing\
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\mailing\
++objets_liaisons
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\
++wapiModelsObjects
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wapimodelsobjects\
++wt
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\
++Abonnement.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\abonnement.cs
++BasketEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\basketentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\basketentity.cs
++BonCadeauEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\boncadeauentity.cs
++CategoryEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\categoryentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\categoryentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\eventssessions\categoryentity.cs
++CibleEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\cibleentity.cs
++Commande.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\commande.cs
++CommandeEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\commandeentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\commandeentity.cs
++CommandeLigne.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\commandeligne.cs
++ConsommateurEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\consommateurentity.cs
++ContrainteEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\contrainteentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\contrainteentity.cs
++CoupeFileEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\coupefileentity.cs
++CouponReductionEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\couponreductionentity.cs
++DepotVenteEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\depotventeentity.cs
++DeviseEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\deviseentity.cs
++Dossier.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\dossier.cs
++DossierProduit.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\dossierproduit.cs
++Entree.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\entree.cs
++EventInfo.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\eventinfo.cs
++EventsGenreEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\eventsgenreentity.cs
++EventsSousGenreEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\eventssousgenreentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\eventssessions\eventssousgenreentity.cs
++FloorEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\floorentity.cs
++FormulaEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\formulaentity.cs
++GestionPlaceEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\gestionplaceentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\gestionplaceentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\eventssessions\gestionplaceentity.cs
++GlobalAppellationEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\globalappellationentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\identity\globalappellationentity.cs
++GlobalTitreEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\globaltitreentity.cs
++GroupeFormulaEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\groupeformulaentity.cs
++IdentiteEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\identiteentity.cs
++IdentityInfoCompEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\identityinfocompentity.cs
++IdentityRIBEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\identityribentity.cs
++InfoCompEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\infocompentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\identity\infocompentity.cs
++LanguageEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\languageentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\languageentity.cs
++LigneChoixPrixEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\lignechoixprixentity.cs
++ListeAttenteEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\listeattenteentity.cs
++ModePaiementEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\modepaiemententity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\modepaiemententity.cs
++OffreEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\offreentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\offreentity.cs
++packages.config
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\packages.config
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\packages.config
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\packages.config
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\packages.config
++PartenaireEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\partenaireentity.cs
++PayementParameters.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\payementparameters.cs
++PhysicSeatEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\physicseatentity.cs
++PriceEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\priceentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\eventssessions\priceentity.cs
++Produit.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\produit.cs
++ProfilAcheteurEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\profilacheteurentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\profilacheteurentity.cs
++ProfilAcheteurOnStructureEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\profilacheteuronstructureentity.cs
++PropertiesEventsEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\propertieseventsentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\propertieseventsentity.cs
++RecetteEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\recetteentity.cs
++ReservationEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\reservationentity.cs
++ReserveEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\reserveentity.cs
++SectionEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\sectionentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\sectionentity.cs
++SettingsLieuEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\settingslieuentity.cs
++ZoneEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\zoneentity.cs
++Microsoft.Web.Infrastructure
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
++Newtonsoft.Json
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++Swashbuckle.Core
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
++System.Configuration
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Net.Http
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Net.Http.Formatting
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
++System.Net.Http.WebRequest
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
++System.Web.Extensions
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Web.Http
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
++System.Web.Http.WebHost
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
++Themis.Libraries.DTO
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
i:{00000000-0000-0000-0000-000000000000}:Themis.Libraries.DTO
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++WebActivatorEx
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:
++AdhesionCatalogEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\adhesion_offres\adhesioncatalogentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\adhesioncatalogentity.cs
++AdhesionCatalogPropertyEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\adhesion_offres\adhesioncatalogpropertyentity.cs
++AdhesionEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\adhesion_offres\adhesionentity.cs
++AdhesionPriceEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\adhesion_offres\adhesionpriceentity.cs
++DistanciationEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\distanciation\distanciationentity.cs
++activeTrailObjets.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\mailing\activetrailobjets.cs
++campagne.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\mailing\campagne.cs
++cible.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\mailing\cible.cs
++CustomAbo.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\customabo.cs
++CustomBasket.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\custombasket.cs
++CustomEventHA.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\customeventha.cs
++CustomFlaggedSeat.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\customflaggedseat.cs
++CustomFormulas.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\customformulas.cs
++CustomJsonSettings.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\customjsonsettings.cs
++CustomSession.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\customsession.cs
++CustomTarif.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\customtarif.cs
++CustomTarifHA.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\customtarifha.cs
++CustomTauxTVA.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\customtauxtva.cs
++DossierIdentiteEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\dossieridentiteentity.cs
++LigneGrilleTarifEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\lignegrilletarifentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\lignegrilletarifentity.cs
++LinkProductGestionPlace.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\linkproductgestionplace.cs
++SessionForChangeSelect.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\objets_liaisons\sessionforchangeselect.cs
++eBasket.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wapimodelsobjects\ebasket.cs
++eCateg.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wapimodelsobjects\ecateg.cs
++eEvent.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wapimodelsobjects\eevent.cs
++eIdentite.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wapimodelsobjects\eidentite.cs
++ePlace.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wapimodelsobjects\eplace.cs
++ePrice.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wapimodelsobjects\eprice.cs
++eProduct.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wapimodelsobjects\eproduct.cs
++eSession.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wapimodelsobjects\esession.cs
++BasketEntreeReaboEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\basketentreereaboentity.cs
++BasketPaiementRequest.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\basketpaiementrequest.cs
++DemandPasswordResetEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\demandpasswordresetentity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\demandpasswordresetentity.cs
++LogEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\logentity.cs
++PanierRequestEntity.cs
i:{817a794c-460d-4585-83c3-2c0b5041a8a9}:d:\work\vieuxprojets\ws_dto\wt\panierrequestentity.cs
++wcf-wsThemis
i:{00000000-0000-0000-0000-000000000000}:wcf-wsThemis
++App_Code
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\app_code\
++App_Data
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\app_data\
++App_Start
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\app_start\
++scriptsSql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\
++Global.asax
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\global.asax
++Iwcf-wsThemis.cs
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\iwcf-wsthemis.cs
++log4netconfig.xml
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\log4netconfig.xml
++mChunks.cs
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\mchunks.cs
++wcf-wsThemis.svc
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\wcf-wsthemis.svc
++Web.config
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\web.config
++PublishProfiles
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\properties\publishprofiles\
++Interop.iSED
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++log4net
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++RestSharp
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++Swagger.Net
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
++System.Drawing
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.EnterpriseServices
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.ServiceModel
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.ServiceModel.Web
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
++System.Web.ApplicationServices
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
++System.Web.DynamicData
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
++System.Web.Entity
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
++System.Web.Services
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++WebActivator
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
++ws_BLL
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:
i:{00000000-0000-0000-0000-000000000000}:ws_BLL
++AppClasses.cs
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\app_code\appclasses.cs
++abonnement
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\
++buyerProfil
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\buyerprofil\
++consumers
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\consumers\
++CoupeFile
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\coupefile\
++events
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\events\
++gestionPlacesScripts
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\
++grilletarif
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\grilletarif\
++identite
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\
++infoComp
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\infocomp\
++language
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\language\
++paiement
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\paiement\
++planSalle
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\plansalle\
++product
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\product\
++reservations
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\reservations\
++seances
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\seances\
++waitList
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\waitlist\
++Global.asax.cs
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\global.asax.cs
++wcf-wsThemis.svc.cs
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\wcf-wsthemis.svc.cs
++Web.Debug.config
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\web.debug.config
++Web.Release.config
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\web.release.config
++the.pubxml
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\properties\publishprofiles\the.pubxml
++createProS_[dbo].[SP_WS_LoadListAllFormuleTarif]
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\createpros_[dbo].[sp_ws_loadlistallformuletarif]
++LoadContraintesGroupOfFormula.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\loadcontraintesgroupofformula.sql
++LoadEventsHorsAbo.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\loadeventshorsabo.sql
++LoadEventsOfFormula.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\loadeventsofformula.sql
++LoadFormulas.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\loadformulas.sql
++LoadGestionPlaceParentOfFormulas.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\loadgestionplaceparentofformulas.sql
++LoadGroupFormulas.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\loadgroupformulas.sql
++LoadPriceOfFormulas.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\loadpriceofformulas.sql
++SelectCurrentBasket.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\selectcurrentbasket.sql
++SelectCurrentFormulesBasket.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\selectcurrentformulesbasket.sql
++SelectCurrentHorsFormulesBasket.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\selectcurrenthorsformulesbasket.sql
++SelectSeancesListOfBasket.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\abonnement\selectseanceslistofbasket.sql
++_createProStock.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\buyerprofil\_createprostock.sql
++getDistinctsCmdsWT.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\buyerprofil\getdistinctscmdswt.sql
++getHisto.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\buyerprofil\gethisto.sql
++AttachedConsumer.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\consumers\attachedconsumer.sql
++SelectIdentiteWithCommande.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\coupefile\selectidentitewithcommande.sql
++GetDistanciation.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\distanciation\getdistanciation.sql
++getManifestationInfos.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\events\getmanifestationinfos.sql
++LoadEvents_listeManifsFiliere.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\events\loadevents_listemanifsfiliere.sql
++LoadEvents_listeManifsIndiv.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\events\loadevents_listemanifsindiv.sql
++LoadEventsAndSessionInGp.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\events\loadeventsandsessioningp.sql
++LoadEventsForToday.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\events\loadeventsfortoday.sql
++propertiesOfEvents.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\events\propertiesofevents.sql
++cacheRefreshManager.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\cacherefreshmanager.sql
++create[gestion_place_multiplicateur_nbmax].sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\create[gestion_place_multiplicateur_nbmax].sql
++create[SP_WS_GETGRILLEALLTARIFS_BIGONE].sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\create[sp_ws_getgrillealltarifs_bigone].sql
++create[SP_WS_GETOFFRES_TOTABLE].sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\create[sp_ws_getoffres_totable].sql
++flagAbos.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\flagabos.sql
++flagAbosFermeMemePlace.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\flagabosfermememeplace.sql
++flagOneSeatTempo.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\flagoneseattempo.sql
++getReservesAbo.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\getreservesabo.sql
++LoadAllInternetGrilleTarif.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\loadallinternetgrilletarif.sql
++LoadAllInternetGrilleTarif_dispoInGp.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\loadallinternetgrilletarif_dispoingp.sql
++LoadGrilleTarif.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\loadgrilletarif.sql
++LoadGrilleTarifHA.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\loadgrilletarifha.sql
++LoadGrilleTarifPlaceSupp.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\loadgrilletarifplacesupp.sql
++ReflagSeatsTempo.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\reflagseatstempo.sql
++UnflagOneSeatTempo.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\unflagoneseattempo.sql
++UnFlagOnSession.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\unflagonsession.sql
++UnflagSeatsTempo.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\gestionplacesscripts\unflagseatstempo.sql
++grilleTarif.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\grilletarif\grilletarif.sql
++consommateursGet.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\consommateursget.sql
++DeleteIdentiteConsommateur.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\deleteidentiteconsommateur.sql
++DeleteMyAccount.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\deletemyaccount.sql
++getIdentiteForPatHome.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\getidentiteforpathome.sql
++InsertIdentite.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\insertidentite.sql
++InsertIdentiteComplement.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\insertidentitecomplement.sql
++InsertIdentiteComplementWeb.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\insertidentitecomplementweb.sql
++InsertIdentiteConsommateur.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\insertidentiteconsommateur.sql
++InsertIdentiteInfosComp.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\insertidentiteinfoscomp.sql
++InsertIdentiteInfosCompPhysique.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\insertidentiteinfoscompphysique.sql
++InsertListInfoCompOnIdentite.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\insertlistinfocomponidentite.sql
++SelectCivilityNaming.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\selectcivilitynaming.sql
++SelectFanCard.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\selectfancard.sql
++SelectIdentite.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\selectidentite.sql
++SelectInfoCompOnIdentite.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\selectinfocomponidentite.sql
++SelectTitre.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\selecttitre.sql
++UpdateIdentite.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\updateidentite.sql
++UpdateIdentiteComplement.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\updateidentitecomplement.sql
++UpdateIdentiteComplementWeb.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\updateidentitecomplementweb.sql
++UpdateIdentiteInfosComp.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\updateidentiteinfoscomp.sql
++UpdateOrInsertIdentiteInfosComp.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\updateorinsertidentiteinfoscomp.sql
++VerifExistingEmail.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\identite\verifexistingemail.sql
++InsertInfoComp.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\infocomp\insertinfocomp.sql
++loadGlobalLanguage.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\language\loadgloballanguage.sql
++loadlanguages.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\language\loadlanguages.sql
++getAcomptesIdentite.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\paiement\getacomptesidentite.sql
++getmodepaiementCmd.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\paiement\getmodepaiementcmd.sql
++getModespaiementForprofilAcheteur.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\paiement\getmodespaiementforprofilacheteur.sql
++markCommandWait.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\paiement\markcommandwait.sql
++loadPlanSalle.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\plansalle\loadplansalle.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\plansalle\loadplansalle.sql
++loadPlanSalleTexts.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\plansalle\loadplansalletexts.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\plansalle\loadplansalletexts.sql
++LoadCommonsMOofFormulas.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\product\loadcommonsmoofformulas.sql
++LoadFraisProductOfFormule.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\product\loadfraisproductofformule.sql
++LoadMaquettesOfGpByMoId.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\product\loadmaquettesofgpbymoid.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\product\loadmaquettesofgpbymoid.sql
++LoadProductOfFormule.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\product\loadproductofformule.sql
++LoadQuestionnaireProductsOfFormulas.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\product\loadquestionnaireproductsofformulas.sql
++createBasketReservation.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\reservations\createbasketreservation.sql
++getInfoResaForBasket.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\reservations\getinforesaforbasket.sql
++getReservationsOfIdentity.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\reservations\getreservationsofidentity.sql
++reservationDetail.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\reservations\reservationdetail.sql
++getLieuDescriptionBySeance.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\seances\getlieudescriptionbyseance.sql
++getLieuOfSeance.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\seances\getlieuofseance.sql
++getSeanceInfos.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\seances\getseanceinfos.sql
++SelectSeanceAndLieu.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\seances\selectseanceandlieu.sql
++DeleteIdentiteOfWaitList.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\waitlist\deleteidentiteofwaitlist.sql
++InsertListeAttente.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\waitlist\insertlisteattente.sql
++SelectListeAttenteOfIdentite.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\waitlist\selectlisteattenteofidentite.sql
++SelectManifestationsAndSeancesWaitList.sql
i:{648c5a8d-0a62-4439-b2bc-1f377d7acaf3}:d:\work\vieuxprojets\wcf-wsthemis\scriptssql\waitlist\selectmanifestationsandseanceswaitlist.sql
++entities
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\
++Manager
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\
++Common.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\common.cs
++Settings.settings
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\properties\settings.settings
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\properties\settings.settings
++Enums
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\enums\
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\logger\enums\
++BoutiqueEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\boutiqueentity.cs
++BoutiqueFamilleEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\boutiquefamilleentity.cs
++BoutiqueSousFamilleEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\boutiquesousfamilleentity.cs
++ChampsTicketAtHome.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\champsticketathome.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\printathome\champsticketathome.cs
++CommandeLigneEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\commandeligneentity.cs
++CompteClientLineEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\compteclientlineentity.cs
++ConsomateurEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\consomateurentity.cs
++DepotVenteLineEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\depotventelineentity.cs
++DossierEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\dossierentity.cs
++FormuleAbonnementEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\formuleabonnemententity.cs
++GroupeFormuleEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\groupeformuleentity.cs
++IdentityEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\identityentity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\identity\identityentity.cs
++LanguagesEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\languagesentity.cs
++MajBddEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\majbddentity.cs
++MaquettesEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\maquettesentity.cs
++OCategoryEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\ocategoryentity.cs
++OEventEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\oevententity.cs
++OLieuEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\olieuentity.cs
++OperateurEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\operateurentity.cs
++OProductEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\oproductentity.cs
++OSessionEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\osessionentity.cs
++ProductInternetEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\productinternetentity.cs
++ProprietesRefOfEventsEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\proprietesrefofeventsentity.cs
++Reserve.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\reserve.cs
++RightsEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\rightsentity.cs
++SaleSettingsConsumerProfilEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\salesettingsconsumerprofilentity.cs
++SaleSettingsEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\salesettingsentity.cs
++SaleSettingsOfferEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\salesettingsofferentity.cs
++StructurePrefsEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\structureprefsentity.cs
++TraductionGpEventDTO.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\traductiongpeventdto.cs
++TypeEnvoiEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\typeenvoientity.cs
++TypeTarifEntity.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\typetarifentity.cs
++BoutiqueFamilleManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\boutiquefamillemanager.cs
++ContrainteManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\contraintemanager.cs
++DemandPasswordResetManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\demandpasswordresetmanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\demandpasswordresetmanager.cs
++EventManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\eventmanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\eventmanager.cs
++EventsGroupsManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\eventsgroupsmanager.cs
++GestionPlaceManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\gestionplacemanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\gestionplacemanager.cs
++LieuManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\lieumanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\lieumanager.cs
++MajBddManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\majbddmanager.cs
++ModePaiementManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\modepaiementmanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\modepaiementmanager.cs
++OffreContrainteManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\offrecontraintemanager.cs
++OffreManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\offremanager.cs
++OperateurPaiementManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\operateurpaiementmanager.cs
++ProductInternetManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\productinternetmanager.cs
++ProfilAcheteurManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\profilacheteurmanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\profilacheteurmanager.cs
++PropertiesEventsManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\propertieseventsmanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\propertieseventsmanager.cs
++ReserveManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\reservemanager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\reservemanager.cs
++StructurePrefsManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\structureprefsmanager.cs
++TypeEnvoiManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\typeenvoimanager.cs
++WidgetsManager.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\manager\widgetsmanager.cs
++Settings.Designer.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\properties\settings.designer.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\properties\settings.designer.cs
++ModePaiementTypeEnum.cs
i:{8f11805b-fc68-4ebc-bb35-d458ff8b9312}:d:\work\vieuxprojets\openentity2010\entities\enums\modepaiementtypeenum.cs
++API
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\api\
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\api\
++EventsSessions
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\eventssessions\
++exposedObjects
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\exposedobjects\
++identity
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\identity\
++obj
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\obj\
++Opinions
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\opinions\
++Orders
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\orders\
++PaymentPrestaReturnModel
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\paymentprestareturnmodel\
++Products
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\products\
++Seats
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\seats\
++Structures
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\structures\
++Translations
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\translations\
++WTObjects
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\wtobjects\
++BuyerProfilEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\buyerprofilentity.cs
++JaugeEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\jaugeentity.cs
++JaugeGroupEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\jaugegroupentity.cs
++PartnerEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\partnerentity.cs
++PartnerRoleEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\partnerroleentity.cs
++ProducteurEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\producteurentity.cs
++SaleChannelEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\salechannelentity.cs
++TranslationEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\translationentity.cs
++Microsoft.Bcl.AsyncInterfaces
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Buffers
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.IO.Pipelines
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Memory
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Numerics
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Numerics.Vectors
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Runtime.CompilerServices.Unsafe
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Text.Encodings.Web
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Text.Json
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Threading.Tasks.Extensions
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.ValueTuple
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++PriceGridEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\eventssessions\pricegridentity.cs
++ReserveEntitycs.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\eventssessions\reserveentitycs.cs
++SeatApi.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\exposedobjects\seatapi.cs
++WebUserApi.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\exposedobjects\webuserapi.cs
++GlobalTitleEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\identity\globaltitleentity.cs
++IdentityRibEnrity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\identity\identityribenrity.cs
++OpinionOrder
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\opinions\opinionorder\
++OpinionAverageEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\opinions\opinionaverageentity.cs
++details
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\orders\details\
++OrderEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\orders\orderentity.cs
++Cyberplus
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\paymentprestareturnmodel\cyberplus\
++StructureSellData.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\structures\structureselldata.cs
++TranslationAreaEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\translations\translationareaentity.cs
++TranslationFields.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\translations\translationfields.cs
++TranslationFieldsCode.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\translations\translationfieldscode.cs
++TranslationsEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\translations\translationsentity.cs
++TranslationSpecificFieldsEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\translations\translationspecificfieldsentity.cs
++TranslationTermEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\translations\translationtermentity.cs
++TranslationVariablesEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\translations\translationvariablesentity.cs
++ActionPaymentEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\wtobjects\actionpaymententity.cs
++PayementRequest.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\wtobjects\payementrequest.cs
++OpinionOrderQuestionEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\opinions\opinionorder\opinionorderquestionentity.cs
++OpinionOrderQuestionTypeEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\opinions\opinionorder\opinionorderquestiontypeentity.cs
++OpinionOrderResponse.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\opinions\opinionorder\opinionorderresponse.cs
++OpinionOrderResponseEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\opinions\opinionorder\opinionorderresponseentity.cs
++OpinionOrderResponseFormEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\opinions\opinionorder\opinionorderresponseformentity.cs
++DossierProductEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\orders\details\dossierproductentity.cs
++DossierSeatEntity.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\orders\details\dossierseatentity.cs
++CyberPlusBrandManagementModel.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\paymentprestareturnmodel\cyberplus\cyberplusbrandmanagementmodel.cs
++CyberPlusMultiPayementModel.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\paymentprestareturnmodel\cyberplus\cyberplusmultipayementmodel.cs
++CyberPlusTransactionModel.cs
i:{0f8fa9ce-1125-4096-90cb-50ff021a7cad}:d:\work\vieuxprojets\themis.libraries.dto\paymentprestareturnmodel\cyberplus\cyberplustransactionmodel.cs
++Themis.Libraries.Utilities
i:{00000000-0000-0000-0000-000000000000}:Themis.Libraries.Utilities
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++Crypto
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\crypto\
++CustomErrors
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\customerrors\
++DB
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\db\
++Logger
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\logger\
++Mail
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\mail\
++ThemisSql
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\themissql\
++ConfigIni.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\configini.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\configini.cs
++ConfigIniDictionnary.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\configinidictionnary.cs
++CsvConverter.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\csvconverter.cs
++Initialisations.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\initialisations.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\initialisations.cs
++ThemisFilesManager.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\themisfilesmanager.cs
++BCrypt.Net
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Runtime.Caching
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++CustomAuthentication.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\api\customauthentication.cs
++CustomResponse.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\api\customresponse.cs
++Utils.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\api\utils.cs
++DecryptDataBaseString.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\crypto\decryptdatabasestring.cs
++EncryptDataBaseString.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\crypto\encryptdatabasestring.cs
++Encryption64.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\crypto\encryption64.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\smartsoft\encryption64.cs
++Sha1.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\crypto\sha1.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\sha1.cs
++ThemisBCrypt.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\crypto\themisbcrypt.cs
++ThemisEncryptString.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\crypto\themisencryptstring.cs
++ThemisQueryStringHandler.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\crypto\themisquerystringhandler.cs
++CustomAuthenticationExceptions.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\customerrors\customauthenticationexceptions.cs
++CustomExceptions.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\customerrors\customexceptions.cs
++EvaluationBuyerException.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\customerrors\evaluationbuyerexception.cs
++SaleChannelException.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\customerrors\salechannelexception.cs
++DBFunctions.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\db\dbfunctions.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\dbfunctions.cs
++SqlCommandExt.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\db\sqlcommandext.cs
++Logger.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\logger\logger.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\logger.cs
++ThemisLogger.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\logger\themislogger.cs
++eMail.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\mail\email.cs
++mailMessageExtension.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\mail\mailmessageextension.cs
++ThemisSqlServer
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\themissql\themissqlserver\
++FilesForSqlRequestsManager.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\themissql\filesforsqlrequestsmanager.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\filesforsqlrequestsmanager.cs
++SqlServerConnexion.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\themissql\sqlserverconnexion.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\sql\sqlserver\sqlserverconnexion.cs
++LogLevel.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\logger\enums\loglevel.cs
++LogModes.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\logger\enums\logmodes.cs
++RequeteInsert.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\themissql\themissqlserver\requeteinsert.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\sql\sqlserver\requeteinsert.cs
++RequeteMixte.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\themissql\themissqlserver\requetemixte.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\sql\sqlserver\requetemixte.cs
++RequeteSelect.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\themissql\themissqlserver\requeteselect.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\sql\sqlserver\requeteselect.cs
++RequeteUpdate.cs
i:{1cb681b9-e27f-44eb-acd2-cd56177f1fdd}:d:\work\vieuxprojets\themis.libraries.utilities\themissql\themissqlserver\requeteupdate.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\sql\sqlserver\requeteupdate.cs
++crypto
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\
++CustomObjets
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\customobjets\
++Enum
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\enum\
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\enum\
++Exceptions
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\exceptions\
++Extensions
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\extensions\
++SmartSoft
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\smartsoft\
++sql
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\sql\
++DataSetHelper.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\datasethelper.cs
++FichiersIhmManager.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\fichiersihmmanager.cs
++FilesForPrestasManager.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\filesforprestasmanager.cs
++LICENSE.txt
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\license.txt
++MyDictionary.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\mydictionary.cs
++SharedMethods.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\sharedmethods.cs
++utilitaires2010.snk
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\utilitaires2010.snk
++XElementExtensions.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\xelementextensions.cs
++BouncyCastle.Crypto
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++DevOne.Security.Cryptography.BCrypt
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++Microsoft.Extensions.DependencyInjection.Abstractions
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++Microsoft.Extensions.Logging.Abstractions
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++Microsoft.VisualBasic
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++MySqlConnector
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++Office
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Diagnostics.DiagnosticSource
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++System.Transactions
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:
++rodcb
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\rodcb\
++bcrypt.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\bcrypt.cs
++crypto_aes.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\crypto_aes.cs
++Securities.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\securities.cs
++ManifSeanceForReFlag.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\customobjets\manifseanceforreflag.cs
++MaquetteType.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\enum\maquettetype.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\enum\maquettetype.cs
++PrisePlace.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\enum\priseplace.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\enum\priseplace.cs
++ProductType.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\enum\producttype.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\enum\producttype.cs
++JaugesLimitExceededException.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\exceptions\jaugeslimitexceededexception.cs
++RecettesException.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\exceptions\recettesexception.cs
++RodrigueStringExtension.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\extensions\rodriguestringextension.cs
++CryptoQueryStringHandler.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\smartsoft\cryptoquerystringhandler.cs
++mysql
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\sql\mysql\
++sqlserver
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\sql\sqlserver\
++ChkSum.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\rodcb\chksum.cs
++CryptoRodCBBin.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\rodcb\cryptorodcbbin.cs
++CryptoRodCBClient.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\rodcb\cryptorodcbclient.cs
++CryptoRodCBRodrigue.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\crypto\rodcb\cryptorodcbrodrigue.cs
++MySqlConnexion.cs
i:{43eb2f59-7b43-4d1e-a62c-16c06e39e756}:d:\work\vieuxprojets\utilitaires\sql\mysql\mysqlconnexion.cs
++Service References
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\service references\
++Web References
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\web references\
++DAOOpen
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\daoopen\
++Distanciation
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\distanciation\
++PrintAtHome
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\printathome\
++WT
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\
++AdhesionManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\adhesionmanager.cs
++App.config
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\app.config
++BonCadeauManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\boncadeaumanager.cs
++CachingRefreshManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\cachingrefreshmanager.cs
++CodePaysManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\codepaysmanager.cs
++CommandeLigneManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\commandelignemanager.cs
++CommandeManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\commandemanager.cs
++CompteClientManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\compteclientmanager.cs
++ConsommateurManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\consommateurmanager.cs
++CoupeFileManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\coupefilemanager.cs
++CouponReductionManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\couponreductionmanager.cs
++CreditBilletsManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\creditbilletsmanager.cs
++customExceptions.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\customexceptions.cs
++DepotVenteManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\depotventemanager.cs
++DeviseManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\devisemanager.cs
++DossierManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\dossiermanager.cs
++DossierProduitManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\dossierproduitmanager.cs
++EntreeManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\entreemanager.cs
++FactureVirementManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\facturevirementmanager.cs
++FiliereManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\filieremanager.cs
++FormulaManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\formulamanager.cs
++GlobalTitreManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\globaltitremanager.cs
++GroupeFormulaManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\groupeformulamanager.cs
++IdentityInfoCompManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\identityinfocompmanager.cs
++IdentityManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\identitymanager.cs
++InfoCompManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\infocompmanager.cs
++JaugesManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\jaugesmanager.cs
++LanguageManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\languagemanager.cs
++List.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\list.cs
++ListeAttenteManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\listeattentemanager.cs
++OperateurManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\operateurmanager.cs
++PartenaireManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\partenairemanager.cs
++PartnerManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\partnermanager.cs
++PriceManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\pricemanager.cs
++ProductManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\productmanager.cs
++RecetteManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\recettemanager.cs
++ReservationManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\reservationmanager.cs
++SeanceManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\seancemanager.cs
++SeatManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\seatmanager.cs
++StructrurePrefsManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\structrureprefsmanager.cs
++ValeurTarifStockManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\valeurtarifstockmanager.cs
++EntityFramework
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++EntityFramework.SqlServer
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++ISO3166
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++Microsoft.IdentityModel.Tokens
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++QRCodeEncoderLibrary
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.ComponentModel.DataAnnotations
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Data.Entity
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Data.Linq
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.IdentityModel.Tokens.Jwt
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++System.Security
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:
++KombiTicket
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\web references\kombiticket\
++Connections
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\daoopen\connections\
++dbOpenModel.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\daoopen\dbopenmodel.cs
++identite.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\daoopen\identite.cs
++langue.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\daoopen\langue.cs
++DistanciationManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\distanciation\distanciationmanager.cs
++campagneActiveTrailManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\mailing\campagneactivetrailmanager.cs
++campagneInboxManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\mailing\campagneinboxmanager.cs
++campagneManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\mailing\campagnemanager.cs
++cibleInboxManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\mailing\cibleinboxmanager.cs
++cibleManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\mailing\ciblemanager.cs
++ConditionPrototype.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\printathome\conditionprototype.cs
++ETicketdico.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\printathome\eticketdico.cs
++TicketAtHomeManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\printathome\ticketathomemanager.cs
++BasketEntreeReaboManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\basketentreereabomanager.cs
++BasketsManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\basketsmanager.cs
++FileAttenteManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\fileattentemanager.cs
++GestionTraceManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\gestiontracemanager.cs
++SeatAboManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\seatabomanager.cs
++SeatUnitSalesManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\seatunitsalesmanager.cs
++TransactionsManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\transactionsmanager.cs
++WebUserManager.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\wt\webusermanager.cs
++RodEFConnectionOpen.cs
i:{ac2c39ad-a2d3-4e6b-9234-c30c13199e80}:d:\work\vieuxprojets\ws_bll\daoopen\connections\rodefconnectionopen.cs
