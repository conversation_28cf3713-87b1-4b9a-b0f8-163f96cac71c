﻿SELECT DISTINCT
             p.produit_id as ProductID
            , produit_nom as ProductName
            , produit_code as ProductCode
            , 1 as NbProduct
            , jauge as Capacity
            , restant as Remainder
            , (ps.montant1 + ps.montant2)*100 as TotalAmount
            ,  ps.montant2*100 as Charge
			,0 as formulaId 
			,p.maquettebillet_id as TicketModelID
			,p.pref_affichage
FROM produit p
INNER JOIN produit_stock ps on ps.produit_id = p.produit_id
INNER JOIN filieres_droits2 fd on fd.droit_id =p.produit_id 
INNER JOIN filiere f on f.filiere_id = fd.filiere_id
WHERE (p.groupe_id=1 /* frais d'envoi */
AND p.internet =1 
AND fd.nom='PRODUIT' 
AND fd.valeur='O'
AND f.filiere_code='INTERNET')
OR (p. produit_id in ( 3650022,2180260, 1930017)) /* pour garder la compatibilité avec les produits déjà en prod*/
ORDER By p.pref_affichage, p.produit_nom