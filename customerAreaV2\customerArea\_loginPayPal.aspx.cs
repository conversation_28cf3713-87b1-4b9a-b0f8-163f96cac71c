﻿using App_Code;
using customerArea.classes;
using log4net;
using RestSharp;
using RestSharp.Authenticators;
using rodrigue.utilitaires.crypto.SHA1;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Configuration;
using System.Web.Script.Serialization;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace customerArea
{
    public partial class _loginPayPal : System.Web.UI.Page
    {
        private static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        protected void Page_Load(object sender, EventArgs e)
        {
            // ========================================================================
            // NOTE IMPORTANTE : Ce code C# n'est JAMAIS exécuté dans le flux actuel !
            // ========================================================================
            //
            // Le flux OAuth PayPal fonctionne comme suit :
            //
            // 1. PayPal redirige vers _loginPayPal.aspx?code=XXX
            // 2. Le JavaScript dans _loginPayPal.aspx s'exécute IMMÉDIATEMENT
            // 3. Le JavaScript ferme la popup et recharge Login.aspx avec codePP=XXX
            // 4. Login.aspx détecte codePP et appelle Login.aspx.cs/ConnectPayPal via AJAX
            // 5. Login.aspx.cs/ConnectPayPal fait l'authentification réelle
            //
            // Le code C# ci-dessous (lignes 35-114) n'a jamais le temps de s'exécuter
            // car le JavaScript ferme la popup avant que le serveur ne traite la requête.
            //
            // Ce code est conservé pour référence historique mais n'est pas utilisé.
            // L'authentification réelle se fait dans Login.aspx.cs/ConnectPayPal.
            //
            // Pour plus de détails, voir :
            // - _loginPayPal.aspx (lignes 7-14) : JavaScript qui gère la redirection
            // - login.js (fonction ConnectPP) : Détecte codePP et appelle l'AJAX
            // - Login.aspx.cs (méthode ConnectPayPal) : Authentification réelle
            // ========================================================================

            /* CODE DÉSACTIVÉ - Non utilisé dans le flux actuel

            string codePP = "";
            if (Request.QueryString["code"] != null)
            {
                codePP = Request.QueryString["code"];
                logger.Debug("ConnectPayPal via PageLoad(" + codePP + ")....");

                System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                var client = new RestClient("https://api.sandbox.paypal.com/v1/identity/openidconnect/tokenservice");
                var request = new RestRequest(Method.POST);

                string userNamePayPal = "AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw";
                string passwordPayPal = "EO70VAO37ZELyJxtR7rR-lrFJVTTgRzWQsxtt1NAEcuj8CSKWP91c0n4ALcB0MXYnBjpXCXUsd4BnNDJ";

                client.Authenticator = new HttpBasicAuthenticator(userNamePayPal, passwordPayPal);
                request.AddHeader("cache-control", "no-cache");
                request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                request.AddParameter("application/x-www-form-urlencoded", "grant_type=authorization_code&code=" + codePP, ParameterType.RequestBody);

                IRestResponse response = client.Execute(request);

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    if (!string.IsNullOrEmpty(response.Content))
                    {
                        string stringResponse = response.Content;
                        JavaScriptSerializer js = new JavaScriptSerializer();
                        PayPalResponseGetToken jsonrespon = js.Deserialize<PayPalResponseGetToken>(stringResponse);

                        string myAccToken = jsonrespon.access_token;

                        client = new RestClient("https://api.sandbox.paypal.com/v1/identity/openidconnect/userinfo/?schema=openid");
                        request = new RestRequest(Method.GET);
                        request.AddHeader("Authorization", $"Bearer {myAccToken}");

                        response = client.Execute(request);

                        if (response.StatusCode == HttpStatusCode.OK)
                        {
                            if (!string.IsNullOrEmpty(response.Content))
                            {
                                stringResponse = response.Content;
                                logger.Debug("PayPal userinfo retrieved successfully: " + stringResponse);

                                PayPalResponseGetInfo jsonPayPalUser = js.Deserialize<PayPalResponseGetInfo>(stringResponse);

                                string structureId = HttpContext.Current.Session["idstructure"].ToString();
                                string cryptoKey = WebConfigurationManager.AppSettings["CryptoKey"].ToString();

                                Sha1 sha1 = new Sha1($"{int.Parse(structureId):0000}|0{cryptoKey}");
                                string myhash = sha1.getSha1();

                                CustomerEntity customer = new CustomerEntity()
                                {
                                    Email = jsonPayPalUser.email,
                                    Name = jsonPayPalUser.name,
                                    FirstName = jsonPayPalUser.name,
                                    Hash = myhash,
                                    Password = jsonPayPalUser.email,
                                    Address1 = jsonPayPalUser.address.street_address,
                                    City = jsonPayPalUser.address.locality,
                                    PostalCode = jsonPayPalUser.address.postal_code,
                                    Country = jsonPayPalUser.address.country
                                };

                                LoginDefault l = new LoginDefault();
                                int resultLogin = l.GetLogin(structureId, "0", customer);

                                if (resultLogin == 0)
                                {
                                    string result2 = Login.CreateCustomerWM(int.Parse(structureId), "0", customer, false, false);
                                }
                                else
                                {
                                    customer.Password = "PAYPALCONNECT";
                                    string result = Login.LoginCustomerWM(int.Parse(structureId), customer);
                                }
                            }
                        }
                        else
                        {
                            logger.Error("Failed to retrieve PayPal userinfo. Response: " + response.StatusCode);
                        }
                    }
                }
                else
                {
                    logger.Error("Failed to retrieve PayPal token. Response: " + response.StatusCode);
                }
            }

            FIN DU CODE DÉSACTIVÉ */
        }
    }

    // Note: Les classes PayPalResponseGetToken et PayPalResponseGetInfo
    // sont définies dans Login.aspx.cs car c'est là qu'elles sont utilisées.
    // Le code C# dans ce fichier (_loginPayPal.aspx.cs) n'est jamais exécuté
    // car le flux OAuth passe uniquement par le JavaScript dans _loginPayPal.aspx
}