﻿

declare @persType VARCHAR(1)
set @persType = '[PERSONNEPHYSIQUEMORALE]'

declare @filiereidRecue int --id de la filiere à inserer
declare @filiereid int --id de la filiere à inserer
declare @nF int
declare @commentaire varchar(max)

set @filiereidRecue =[FILIERE_ID] 
set @commentaire = '[COMMENT]'


select @nf = count(*) FROM filiere WHERE filiere_id=@filiereidRecue

if ( @nF=0)
begin
	set @commentaire = @commentaire + ' ??? Filiere=' + convert(varchar(50), @filiereidRecue);
	select top 1 @filiereid = filiere_id from filiere where filiere_type_vente=4
	
end
else
begin
	set @filiereid = @filiereidRecue
end


UPDATE cpt_client SET compteur = compteur +1, @id=compteur+1; 
SELECT @id;




INSERT INTO identite (identite_id,identite_nom,identite_complement,identite_titre_id,appellation_id,identite_date_naissance,postal_rue1
,postal_rue2,postal_rue3,postal_rue4,postal_cp,postal_ville,postal_region,postal_pays,postal_tel1,postal_tel1_libelle_id,postal_tel2,postal_tel2_libelle_id
,postal_tel3,postal_tel3_libelle_id,postal_tel4,postal_tel4_libelle_id,postal_tel5,postal_tel5_libelle_id,facture_rue1,facture_rue2
,facture_rue3,facture_rue4,facture_cp,facture_ville,facture_region,facture_pays,facture_tel1,facture_tel1_libelle_id,facture_tel2,facture_tel2_libelle_id
,facture_tel3,factue_tel3_libelle_id,facture_tel4,facture_tel4_libelle_id,marqueur_mailing_id,identite_validite_debut,identite_validite_fin
,identite_v,operateur_id,ref_compta,ref_perso,etiquette1,etiquette2,etiquette3,etiquette4,etiquette5,identite_libre1,identite_libre2,identite_libre3
,identite_libre4,identite_libre5,postal_tel6,postal_tel6_libelle_id,postal_tel7,postal_tel7_libelle_id,identite_prenom,filiere_id,identite_remise
,identite_remisedg,montant_credit,montant_debit,FicheSupprimer,statut_financier,appelinterloc_id,
--identite_groupe_id,,
IDENTITE_DATE_CREATION,
IDENTITE_DATE_MODIFICATION,
--operateurmodif_id,
facture_tel5,facture_tel5_libelle_id,facture_tel6,facture_tel6_libelle_id,facture_tel7
,facture_tel7_libelle_id,identite_password)
     VALUES
 (@id, '[NOM]', '[COMPL_NOM]', [TITRE_ID], [APPELATION_ID], CONVERT(DATETIME,'[DATEOFBIRTHDAY]',103), '[POSTAL_RUE1]','[POSTAL_RUE2]','[POSTAL_RUE3]','[POSTAL_RUE4]',
 '[POSTAL_CP]','[POSTAL_VILLE]','[POSTAL_REGION]','[POSTAL_PAYS]','[POSTAL_TEL1]',[POSTAL_TEL_LIB1],'[POSTAL_TEL2]',[POSTAL_TEL_LIB2],'[POSTAL_TEL3]',[POSTAL_TEL_LIB3],
 '[POSTAL_TEL4]',[POSTAL_TEL_LIB4],'[POSTAL_TEL5]',[POSTAL_TEL_LIB5],'[FACTURE_RUE1]','[FACTURE_RUE2]','[FACTURE_RUE3]','[FACTURE_RUE4]','[FACTURE_CP]','[FACTURE_VILLE]',
 '[FACTURE_REGION]','[FACTURE_PAYS]','[FACTURE_TEL1]','[FACTURE_TEL_LIB1]','[FACTURE_TEL2]','[FACTURE_TEL_LIB2]','[FACTURE_TEL3]','[FACTURE_TEL_LIB3]',
 '[FACTURE_TEL4]','[FACTURE_TEL_LIB4]','[MARQUEUR_MAILING_ID]','[VALIDITE_DEBUT]','[VALIDITE_FIN]',[IDENTITE_V],[OPERATEUR_ID],'[REF_COMPTA]',
 '[REF_PERSO]','[ETIQUETTE1]','[ETIQUETTE2]','[ETIQUETTE3]','[ETIQUETTE4]','[ETIQUETTE5]','[IDENTITE_LIBRE1]','[IDENTITE_LIBRE2]','[IDENTITE_LIBRE3]','[IDENTITE_LIBRE4]','[IDENTITE_LIBRE5]',
 '[POSTAL_TEL6]',[POSTAL_TEL_LIB6],'[POSTAL_TEL7]',[POSTAL_TEL_LIB7],'[PRENOM]',@filiereid,[IDENTITE_REMISE],[IDENTITE_REMISE_DG],[MONTANT_CREDIT],[MONTANT_DEBIT],
 '[FICHE_SUPPRIMER]',[STATUT_FINANCIER],[APPEL_INTERLOC_ID],
 --[GROUPE_ID], valeur par defaut 0
 [IDENTITE_DATE_CREATION],[IDENTITE_DATE_MODIFICATION],
 --[OPERATEUR_MODIF_ID],  valeur par defaut 0
 '[FACTURE_TEL5]',[FACTURE_TEL_LIB5],'[FACTURE_TEL6]',[FACTURE_TEL_LIB6],'[FACTURE_TEL7]',[FACTURE_TEL_LIB7],'[IDENTITE_PASSWORD]')


 if (@persType='M')


 begin /* personne morale */

 update identite set identite_complement =identite_prenom where identite_id=@id
 update identite set identite_prenom='' where identite_id=@id

 end


 
 -- si preference LNB --> generation auto de la fancard dans table fancard
DECLARE @IsLnb int
SELECT @IsLnb=count(*) from structure_prefs where preference_cle='LNB' and preference_valeur=1
SELECT @IsLnb
IF (@IsLnb=1) 
BEGIN
	DECLARE @clubId int
	DECLARE @code varchar(10)
	SELECT @clubId=preference_valeur, @code=c.code from structure_prefs sp
		INNER join club c on c.id=sp.preference_valeur
		WHERE preference_cle='GROUPE_SUPP'
	IF (@clubId>0)
	BEGIN	
		INSERT INTO fancard 
		(NumFancard,identite_id, club_id, typecarte, TypeSupporter, numCarteIdentite, interdictionStade, dateFinInterdiction,
		interdictionNationale, DateFininterdictionNationale, status, Edite, DateClaireMAJ, DateDebutInterdiction, DateDebutInterdictionNationale, TypeCreation, 
		DateDebInterdictionMotif3, DateFinInterdictionMotif3, block, rang, place, NbBillet)
		VALUES
		(RIGHT( @code,4)  + REPLICATE('0',8-LEN(RTRIM(@id))) + RTRIM(@id), -- code sur 4c + identite sur 8c
		@id, @clubId, 1, 0,0,'N','01/01/1900', 
		'N', '01/01/1900', 0, '', getdate(), '01/01/1900', '01/01/1900', 'RODRIGUE',
		 '01/01/1900', '01/01/1900', '','','', 0)

	end

end
 
