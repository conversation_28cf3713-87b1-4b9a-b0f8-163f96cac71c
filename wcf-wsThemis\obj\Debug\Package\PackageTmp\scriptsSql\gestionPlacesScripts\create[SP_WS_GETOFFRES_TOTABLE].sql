﻿
ALTER PROCEDURE [dbo].[SP_WS_GETOFFRES_TOTABLE]
 	-- Add the parameters for the stored procedure here
 	@identiteId int,
 	@profil_acheteur_id int
 	--,
 	--@manif_id int 	
 AS
 BEGIN
 	-- SET NOCOUNT ON added to prevent extra result sets from
 	-- interfering with SELECT statements.
 	SET NOCOUNT ON;
 
     -- Insert statements for procedure here
     -- Insert statements for procedure here
     IF (@profil_acheteur_id=0 AND @identiteId>0)
     BEGIN
 		SELECT distinct(o.offre_id),0 as checked,0 as ok 
 		INTO #tmp_offers  
 		FROM VueGestionPlaceParticuliers gp 
 		INNER JOIN offre_gestion_place ogp ON ogp.gestion_place_id=gp.gestion_place_id 
 		INNER JOIN offre o ON o.offre_id=ogp.offre_id 
 		INNER JOIN offre_contrainte oc ON oc.offre_id=ogp.offre_id
 		WHERE o.date_deb_validite<getdate()
 		AND o.date_fin_validite>getdate()
 		--AND gp.manif_id=@manif_id
 		GROUP BY o.offre_id, gp.gestion_place_id 
 
 		DECLARE @offre_id int 
 		WHILE exists(SELECT * from #tmp_offers where checked=0) -- parcourt des offres disponibles 
 		BEGIN
 		   set @offre_id =(SELECT top 1 offre_id FROM #tmp_offers where checked=0 order by 1)
 		   DECLARE contrainte_cursor CURSOR SCROLL FOR 
 		   SELECT sql_where FROM contrainte c, offre_contrainte oc 
 		   WHERE oc.contrainte_id =c.contrainte_id and offre_id=@offre_id 
 		   declare @sql_where varchar(1000) 
 		   OPEN contrainte_cursor 
 		   declare @AllContraintesOk bit 
 		   set @AllContraintesOk=1;
 		   FETCH NEXT FROM contrainte_cursor INTO @sql_where --parcourt des contraintes de l'offre
 		   WHILE @@FETCH_STATUS=0
 		   BEGIN 
 			   DECLARE @sql_to_exec varchar(1000) 
 			   SET @sql_to_exec=REPLACE(@sql_where,'@identite_id',@identiteId) 
 			   CREATE TABLE #results (nrows int) 
 	
 				   INSERT #results exec (@sql_to_exec) -- execution de la requete dans la colonne sql_where de la table contrainte (insert dans tbl tempo ne sert qu'à ne pas afficher le resultat) 
 				   IF (@@ROWCOUNT=0) BEGIN  SET @AllContraintesOk=0 --contrainte pas remplie, on sort 
 					   DROP TABLE #results BREAK; 
 				   END
 
 		   DROP TABLE #results
 		   FETCH NEXT FROM contrainte_cursor INTO @sql_where 
 		END
 		CLOSE contrainte_cursor
 		DEALLOCATE contrainte_cursor
 		UPDATE #tmp_offers SET checked=1,ok=@AllContraintesOk WHERE offre_id=@offre_id 
 		END
 		insert #myoffres (offre_id, offre_nom)
 		SELECT o.offre_id, offre_nom FROM #tmp_offers tmpo, offre o WHERE ok=1
 		AND tmpo.offre_id=o.offre_id ORDER BY o.offre_id
 
 		DROP TABLE #tmp_offers
 
 	END    
     IF (@profil_acheteur_id>0 AND @identiteId=0)
     BEGIN
 		insert #myoffres (offre_id, offre_nom)	
 		SELECT distinct(o.offre_id),offre_nom 
 		FROM VueGestionPlaceParticuliers gp 
 		INNER JOIN offre_gestion_place ogp ON ogp.gestion_place_id=gp.gestion_place_id 
 		INNER JOIN offre o ON o.offre_id=ogp.offre_id 
 		INNER JOIN offre_profil_acheteur opa ON o.offre_id=opa.offre_id 
 		WHERE o.offre_id not in (select offre_id FROM offre_contrainte oc) /* offres sans contraintes sql_where*/
 		AND opa.profil_acheteur_id=@profil_acheteur_id
 		AND o.date_deb_validite<getdate()
 		AND o.date_fin_validite>getdate()
 		--AND gp.manif_id=@manif_id
 	END
 	IF (@profil_acheteur_id>0 AND @identiteId>0)
     BEGIN
 
 		SELECT distinct(o.offre_id),0 as checked,0 as ok
 		INTO #tmp_offers2 
 		FROM VueGestionPlaceParticuliers gp 
 		INNER JOIN offre_gestion_place ogp ON ogp.gestion_place_id=gp.gestion_place_id 
 		INNER JOIN offre o ON o.offre_id=ogp.offre_id 
 		INNER JOIN offre_contrainte oc On oc.offre_id=ogp.offre_id
 		INNER JOIN offre_profil_acheteur opa ON opa.offre_id =o.offre_id
 		AND opa.profil_acheteur_id=@profil_acheteur_id
 		WHERE o.date_deb_validite<getdate()
 		AND o.date_fin_validite>getdate()
 		--AND gp.manif_id=@manif_id
 		GROUP BY o.offre_id, gp.gestion_place_id 
 
 		--DECLARE @offre_id int 
 		WHILE exists(SELECT * from #tmp_offers2 where checked=0) -- parcourt des offres disponibles 
 		BEGIN
 		   set @offre_id =(SELECT top 1 offre_id FROM #tmp_offers2 where checked=0 order by 1)
 		   DECLARE contrainte_cursor CURSOR SCROLL FOR 
 		   SELECT sql_where FROM contrainte c, offre_contrainte oc 
 		   WHERE oc.contrainte_id =c.contrainte_id and offre_id=@offre_id 
 		   --declare @sql_where varchar(500) 
 		   OPEN contrainte_cursor 
 		   --declare @AllContraintesOk bit 
 		   set @AllContraintesOk=1;
 		   FETCH NEXT FROM contrainte_cursor INTO @sql_where --parcourt des contraintes de l'offre
 		   WHILE @@FETCH_STATUS=0
 		   BEGIN 
 			   --DECLARE @sql_to_exec varchar(500) 
 			   SET @sql_to_exec=REPLACE(@sql_where,'@identite_id',@identiteId) 
 			   print @sql_to_exec
 			   CREATE TABLE #results2 (nrows int) 
 			   --BEGIN TRY
 				   INSERT #results2 exec (@sql_to_exec) -- execution de la requete dans la colonne sql_where de la table contrainte (insert dans tbl tempo ne sert qu'à ne pas afficher le resultat) 
 				   IF (@@ROWCOUNT=0) BEGIN  SET @AllContraintesOk=0 --contrainte pas remplie, on sort 
 					   DROP TABLE #results2 BREAK; 
 				   END
 			   --END TRY
 			   --BEGIN CATCH
    			--	SET @AllContraintesOk=0 --erreur dans sqlwhere, on sort
 				--   DROP TABLE #results BREAK;
 			   --END CATCH
 		   DROP TABLE #results2
 		   FETCH NEXT FROM contrainte_cursor INTO @sql_where 
 		END
 		CLOSE contrainte_cursor
 		DEALLOCATE contrainte_cursor
 		UPDATE #tmp_offers2 SET checked=1,ok=@AllContraintesOk WHERE offre_id=@offre_id 
 		END
 		insert #myoffres (offre_id, offre_nom)
 		SELECT o.offre_id, offre_nom from #tmp_offers2 tmpo, offre o where ok=1
 		AND tmpo.offre_id=o.offre_id 
 
 		DROP TABLE #tmp_offers2	
 		END	
 	
 	
 END
