﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A2FACD66-71D3-489A-9BA0-BEB74AC55956}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>WebTracing2010</RootNamespace>
    <AssemblyName>WebTracing2010</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>%24/WebTracing/WebTracing2010/WebTracing2010/WebTracing2010</SccProjectName>
    <SccLocalPath>.</SccLocalPath>
    <SccAuxPath>http://v-dev-tfs:8080/tfs/appscollection</SccAuxPath>
    <SccProvider>{4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisIgnoreGeneratedCode>true</CodeAnalysisIgnoreGeneratedCode>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BasketLineEntity.cs" />
    <Compile Include="BasketManager.cs" />
    <Compile Include="CategoryPriceEntity.cs" />
    <Compile Include="ConnexionEntity.cs" />
    <Compile Include="ConnexionManager.cs" />
    <Compile Include="ConnexionTypeEntity.cs" />
    <Compile Include="EventsGroupsEntity.cs" />
    <Compile Include="FileAttente.cs" />
    <Compile Include="FormulaGroupeManager.cs" />
    <Compile Include="GestionTrace.cs" />
    <Compile Include="GroupUsersEntity.cs" />
    <Compile Include="LieuEntity.cs" />
    <Compile Include="ProductEntityManager.cs" />
    <Compile Include="ProfilAcheteurOfAdministratorEntity.cs" />
    <Compile Include="RevendeurEntity.cs" />
    <Compile Include="RevendeurManager.cs" />
    <Compile Include="SeatFormulaGroupeEntity.cs" />
    <Compile Include="FormulaGroupeEntity.cs" />
    <Compile Include="Baskets.cs" />
    <Compile Include="EventEntity.cs" />
    <Compile Include="ProductEntity.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SeatAboEntity.cs" />
    <Compile Include="SeatEntity.cs" />
    <Compile Include="SeatFormulaGroupeManager.cs" />
    <Compile Include="SeatUnitSalesEntity.cs" />
    <Compile Include="SessionEntity.cs" />
    <Compile Include="StructuresManager.cs" />
    <Compile Include="StructureEntity.cs" />
    <Compile Include="UserOnStructuresEntity.cs" />
    <Compile Include="UserOnStructuresManager.cs" />
    <Compile Include="UsersEntity.cs" />
    <Compile Include="UsersManager.cs" />
    <Compile Include="WebTracingManager.cs" />
    <Compile Include="WebUser.cs" />
    <Compile Include="WTDataBase.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OpenEntity2010\OpenEntity2010.csproj">
      <Project>{8f11805b-fc68-4ebc-bb35-d458ff8b9312}</Project>
      <Name>OpenEntity2010</Name>
    </ProjectReference>
    <ProjectReference Include="..\utilitaires\utilitaires2010.csproj">
      <Project>{43eb2f59-7b43-4d1e-a62c-16c06e39e756}</Project>
      <Name>utilitaires2010</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>