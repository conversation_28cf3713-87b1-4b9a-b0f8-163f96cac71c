﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  Pour plus d'informations sur la configuration de votre application ASP.NET, consultez
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="login.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <appSettings>
    <!--<add key="PhysicalPathOfTheSite" value="\\*************\front\INDIV\"></add>-->
    <!--<add key="CssRelativeFile" value="/differentesCss/[idstructure].css"></add>-->
    <!--<add key="urlOfTheSite" value="https://www.themisweb.fr/rodwebshoptest/"></add>-->
    <add key="Version" value="2.5.2" />
    <add key="TypeRun" value="PROD" />
    <add key="ThemisIniPathProd" value="\\*************\customerfiles\PROD\[idstructureSur4zeros]\CONFIGSERVER\config.ini.xml" />
    <add key="ThemisIniPath" value="\\*************\customerfiles\TEST\[idstructureSur4zeros]\CONFIGSERVER\config.ini.xml" />
    <add key="ThemisIniPathLocal" value="D:\Themisserver\[idstructureSur4zeros]\CONFIGSERVER\config.ini.xml" />
    <add key="LogDirectory" value="D:\LOGS\" />
    <add key="listWTsConns" value="D:\LOGS\FLAGS\flags.txt" />
    <add key="MENU" value="~\templates\menugauche[.lang][.structureid].htm" />
    <add key="MENUADMIN" value="~\templates\menugaucheadmin[.lang][.structureid].htm" />
    <add key="DATEPICKER" value="~\templates\scriptDatepicker[.lang].htm" />
    <add key="PDFTEMPLATE" value="~\templates\templatePDFStats[.lang].htm" />
    <add key="PDFTEMPLATESTATMENTCASH" value="~\templates\templatePDFStatmentCash[.lang].htm" />
    <add key="FilePreviewTemplate" value="/templates/previewfilecss.aspx?pageName=[pageNameCurrent]" />
    <add key="UrlAccesChoixManifs" value="https://test.themisweb.fr/indivnew/fListeManifs.aspx?idstructure=[idstructure]&amp;idevent=[eventID]" />
    <add key="UrlAccesStatsPADirect" value="http://localhost:3091/StatsPADirect.aspx?structureid=[idstructure]&amp;paId=[paId]&amp;hash=[hash]" />
    <add key="CryptoKeyPA" value="St4tS4PA" />
    <!--<add key="wsPaiement" value="http://**************/wsthemispaiementtest/WSThemispaiement.asmx"/>-->
    <!--<add key="wsPaiement" value="http://**************/WS/Payment/WSThemispaiement.asmx"/>
    <add key="reprintPDF" value="\\**************\SITES\TEST\ReprintPDF"/>-->
    <!--<add key="wsPaiementProd" value="http://**************/WS/paymentV/V1.4.29/WSThemispaiement.asmx"/>-->
    <!--<add key="wsPaiement" value="http://**************/WSTEST/Payment/WSThemispaiement.asmx"/>-->
    <add key="reprintPDF" value="\\**************\SITES\TEST\ReprintPDF" />
    <add key="PathGutenbergIni" value="\\**************\sites\DEV\Admin\current\configs\maquettes_billets\gutenberg[.lang].ini" />
    <add key="limit_logos_maquettes" value="~\configs\logos_maquettes.xml" />
    <add key="UrlAccesDirectManifs" value="rodwebshoptest/fEventChoiceIsMade.aspx?idstructure=[idstructure]&amp;idevent=[idmanif]" />
    <add key="DomainsUrlsAccesDirectManifs" value="https://www.themisweb.fr/,https://www.themisweb.de/,https://www.themisweb.net/,https://themisweb.com/" />
    <!--TEMPLATES FILES-->
    <add key="FilesCommentaireTemplate" value="~\templates\commentaires.htm" />
    <add key="FilesMailsTemplate" value="~\templates\mails.htm" />
    <!--SPECIFIQUE SITE INDIV
    <add key="urlOfTheSite" value="http://*************/indiv"></add>
    <add key="FilesCommentaireIndiv" value="\\*************\front\INDIV\filesinfos\[idstructure]\[siteName][.lang].html"></add>
    <add key="FilesMailsInscriptionIndiv" value="C:\themisserver\[idstructure]\[siteName][.lang].html"></add>
    <add key="FilesMailsPasswordIndiv" value="C:\themisserver\[idstructure]\[siteName][.lang].html"></add>
    <add key="FilesMailsConfirmationIndiv" value="C:\themisserver\[idstructure]\[siteName][.lang].html"></add>
    <add key="FilesMailsErrorIndiv" value="C:\themisserver\[idstructure]\[siteName][.lang].html"></add>-->
    <!--<add key="PhysicalPathOfTheSite" value="\\**************\hebergement\Sites\RodWebShop\TEST\"></add>
    <add key="PhysicalPathOfTheSiteIndiv" value="\\*************\front\INDIV\differentesCss\[idstructure].css"></add> 
    <add key="FileTagsIndiv" value="\\*************\front\INDIV\filesinfos\[idstructure]\[siteName][.lang].html"></add>-->
    <!--SPECIFIQUE SITE INDIV-->
    <add key="FilesCommentaireIndiv" value="\\**************\customerfiles\PROD\[idstructure]\Indiv\FILESINFOS\[siteName][.lang].html" />
    <add key="FilesCommentaireCustomerV3" value="D:\customerfiles\DEV\[idstructure]\Customer\FILESINFOS\[siteName][.lang].htm" />
    <add key="FilesMailsInscriptionIndiv" value="D:\customerfiles\DEV\CONFIGSERVER\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsPasswordIndiv" value="D:\customerfiles\DEV\CONFIGSERVER\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsConfirmationIndiv" value="D:\customerfiles\DEV\CONFIGSERVER\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsErrorIndiv" value="D:\customerfiles\DEV\CONFIGSERVER\[idstructure]\[siteName][.lang].html" />
    <add key="FileTags" value="\filesinfos\[idstructure]\[siteName][.lang].html" />
    <add key="PhysicalPathOfTheSiteIndiv" value="D:\customerfiles\DEV\[idstructure]\Indiv\CSS\[idstructure].css" />
    <add key="FileTagsIndiv" value="D:\customerfiles\DEV\[idstructure]\Indiv\FILESINFOS\[siteName][.lang].html" />
    <!-- FIN SPECIFIQUE SITE INDIV-->
    <!--SPECIFIQUE SITE INDIV-->
    <!--<add key="FilesCommentaireIndiv" value="\\**************\customerfiles\PROD\[idstructure]\Indiv\FILESINFOS\[siteName][.lang].html"></add>
    <add key="FilesMailsInscriptionIndiv" value="\\**************\customerfiles\PROD\CONFIGSERVER\[idstructure]\[siteName][.lang].html"></add>
    <add key="FilesMailsPasswordIndiv" value="\\**************\customerfiles\PROD\CONFIGSERVER\[idstructure]\[siteName][.lang].html"></add>
    <add key="FilesMailsConfirmationIndiv" value="\\**************\customerfiles\PROD\CONFIGSERVER\[idstructure]\[siteName][.lang].html"></add>
    <add key="FilesMailsErrorIndiv" value="\\**************\customerfiles\PROD\CONFIGSERVER\[idstructure]\[siteName][.lang].html"></add>
    <add key="FileTags" value="filesinfos\[idstructure]\[siteName][.lang].html"></add>
  

    <add key="PhysicalPathOfTheSiteIndiv" value="\\**************\customerfiles\PROD\[idstructure]\Indiv\CSS\[idstructure].css"></add>
    <add key="FileTagsIndiv" value="\\**************\customerfiles\PROD\[idstructure]\Indiv\FILESINFOS[siteName][.lang].html"></add>-->
    <!-- FIN SPECIFIQUE SITE INDIV-->
    <!--<add key="FileTags" value="filesinfos\[idstructure]\[siteName][.lang].html"></add>-->
    <!-- FIN SPECIFIQUE SITE INDIV-->
    <!-- SPECIFIQUE SITE ABO -->
    <!--<add key="UrlOfTheSiteAbo" value="https://www.themisweb.fr/rodwebshopabo_test\"></add>-->
    <!--<add key="PhysicalPathOfTheSiteAbo" value="\\*************\FRONT\ABO\"></add>-->
    <add key="PhysicalPathOfTheSiteAbo" value="\\*************\front\ABO\differentesCss\[idstructure].css" />
    <add key="FilesCommentaireAbo" value="D:\customerfiles\DEV\[idstructure]\Abo\filesinfos\[siteName][.lang].html" />
    <add key="BanniereImageRelativeFileAbo" value="images/[idstructure]/banner.gif" />
    <add key="FilesMailsInscriptionAbo" value="D:\Themisserverr\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsPasswordAbo" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsConfirmationAbo" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsErrorAbo" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FileTagsAbo" value="\\*************\front\ABO\filesinfos\[idstructure]\[siteName][.lang].html" />
    <!-- FIN SPECIFIQUE SITE ABO -->
    <!-- SPECIFIQUE SITE PARK -->
    <add key="PhysicalPathOfTheSitePark" value="\\*************\FRONT\PARK\differentesCss\[idstructure].css" />
    <add key="FilesCommentairePark" value="\\*************\front\PARK\filesinfos\[idstructure]\[siteName][.lang].html" />
    <!--<add key="PhysicalPathOfTheSitePark" value="\\**************\hebergement\Sites\RodWebShopPark\TEST\"></add>-->
    <add key="BanniereImageRelativeFilePark" value="images/[idstructure]/banner.gif" />
    <add key="FilesMailsInscriptionPark" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsPasswordPark" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsConfirmationPark" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsErrorPark" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FileTagsPark" value="\\*************\front\PARK\filesinfos\[idstructure]\[siteName][.lang].html" />
    <!-- FIN SPECIFIQUE SITE PARK -->
    <!-- SPECIFIQUE MULTISITES -->
    <!--<add key="PhysicalPathOfTheSiteMultiSites" value="\\*************\FRONT\MULTISITES"></add>-->
    <add key="FilesCommentaireMultiSites" value="\\*************\front\MULTISITES\filesinfos\[idstructure]\[siteName][.lang].html" />
    <add key="PhysicalPathOfTheSiteMultiSites" value="\\*************\front\MULTISITES\differentesCss\[idstructure].css" />
    <add key="BanniereImageRelativeFileMultiSites" value="images/[idstructure]/banner.gif" />
    <add key="FilesMailsInscriptionMultiSites" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsPasswordMultiSites" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsConfirmationMultiSites" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsErrorMultiSites" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FileTagsMultiSites" value="\\*************\front\MULTISITES\filesinfos\[idstructure]\[siteName][.lang].html" />
    <!-- FIN SPECIFIQUE MULTISITES -->
    <!-- SPECIFIQUE SITE REABO -->
    <add key="FilesCommentaireReabo" value="\\*************\front\REABO\filesinfos\[idstructure]\[siteName][.lang].html" />
    <add key="PhysicalPathOfTheSiteReabo" value="\\*************\front\REABO\differentesCss\[idstructure].css" />
    <add key="BanniereImageRelativeFileReabo" value="images/[idstructure]/banner.gif" />
    <add key="FilesMailsInscriptionReabo" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsPasswordReabo" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsConfirmationReabo" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsErrorReabo" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FileTagsReabo" value="\\*************\front\REABO\filesinfos\[idstructure]\[siteName][.lang].html" />
    <!-- FIN SPECIFIQUE SITE REABO -->
    <!-- SPECIFIQUE SITE PAIEMENT -->
    <add key="FilesCommentairePaiement" value="\\*************\front\PAIEMENT\filesinfos\[idstructure]\[siteName][.lang].html" />
    <add key="PhysicalPathOfTheSitePaiement" value="\\*************\FRONT\PAIEMENT\differentesCss\[idstructure].css" />
    <add key="BanniereImageRelativeFilePaiement" value="images/[idstructure]/banner.gif" />
    <add key="FilesMailsInscriptionPaiement" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsPasswordPaiement" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsConfirmationPaiement" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FilesMailsErrorPaiement" value="D:\themisserver\DEV\[idstructure]\[siteName][.lang].html" />
    <add key="FileTagsPaiement" value="\\*************\front\PAIEMENT\filesinfos\[idstructure]\[siteName][.lang].html" />
    <!-- FIN SPECIFIQUE SITE PAIEMENT -->
    <add key="LogosPath" value="\\*************\customerfiles\TEST\[idstructureSur4zeros]\PAIEMENT\IMAGES\LogosMaquettes\" />
    <add key="UploadSizeFileMax" value="20000" />
    <!--<add key="LogosMaquettesPath" value="\\**************\hebergement\Sites\RodWebShop\TEST\images\[idstructureSur4zeros]\manifestations\"/>-->
    <add key="LogosMaquettesPath" value="\\Front-themis-01\customerfiles\TEST\[idstructureSur4zeros]\Indiv\IMAGES\manifestations\" />
    <add key="urlOfTheSiteLogosManifs" value="https://test.themisweb.fr/files/[structureid]/INDIV/images/manifestations/" />
    <add key="urlOfTheSiteLogosProduits" value="https://test.themisweb.fr/files/[structureid]/INDIV/images/produits/" />
    <add key="LogosProduitsIndivPath" value="\\Front-themis-01\customerfiles\TEST\[idstructureSur4zeros]\Indiv\IMAGES\produits\" />
    <!--<add key="FileHelp" value="\\*************\front\ADMIN\V2\help\[pageName][.lang].html" />-->
    <add key="ImagePdfPage" value="pages\\pdf\\images\\logo_rod_index.png" />
    <!--<add key="PdfFileDownloadStatsPaniers" value="\\*************\front\ADMIN\V2\PDF\paniers\[idstructure]\"></add> -->
    <!--<add key="PdfFileDownloadStatsEntrees" value="PDF\entrees\[idstructure]\"></add>-->
    <!--
    <add key="PdfFileDownloadStatsEntrees" value="C:\work\adminv2\login\pages\pdf\entrees\[idstructure]\"></add>-->
    <!--<add key="ExcelsFileDownloadStatsEntrees" value="D:\files\[idstructure]\ADMIN\EXPORTS\XLS\ENTREES/"></add>-->
    <add key="PdfFileDownloadStatsPaniers" value="/files/[idstructure]/ADMIN/EXPORTS/PDF/PANIERS/" />
    <add key="PdfFileDownloadStatsEntrees" value="/Front-themis-01\customerfiles\PROD/[idstructure]/ADMIN/EXPORTS/PDF/ENTREES/" />
    <add key="ExcelsFileDownloadStatsEntrees" value="/files/[idstructure]/ADMIN/EXPORTS/XLS/ENTREES/" />
    <add key="ExcelsFileDownloadStatsEntreesCompletePath" value="D:/files/[idstructure]/ADMIN/EXPORTS/XLS/ENTREES/" />
    <add key="DbWebTracingDataDirectory" value="D:\SQL\DATA" />
    <add key="DbWebTracingLogsDirectory" value="E:\SQL\LOG" />
    <add key="DbWebTracingDataDirectoryLocal" value="D:\BDDWebTracing" />
    <add key="DbWebTracingLogsDirectoryLocal" value="D:\BDDWebTracingLDF" />
    <add key="DbWebTracingName" value="WebTracingTest" />
    <add key="IPWebTracingServer" value="*************" />
    <add key="ResourcesPath" value="D:\customerfiles\DEV\default\AdminV2\Resources\" />
    <add key="ReusePassword" value="NO" />
    <add key="ResetPasswordMailPath" value="\\**************\sites\TEST\Admin\current\templates\resetpassword[.lang].htm" />
    <add key="CryptoKey" value="RodWebShop95" />
    <add key="EmailEnvoyes" value="c:\Themisserver\emails_envoyes" />
    <add key="SmtpClientIp" value="************" />
    <!--<add key="EmailCopyAddress" value="<EMAIL>"/>-->
    <add key="EmailSenderAddress" value="<EMAIL>" />
    <!--date correspond a date_blocage qui permet de dire que le compte est supprimé-->
    <add key="DateDeleteAccount" value="01/01/2001" />
    <add key="VerifSitesPath" value="\\[ipserver]\customerfiles\PROD\[idstructure]" />
    <add key="ResetCacheXmlPath" value="\\*************\customerfiles\settingsResetCache.xml" />
    <add key="FileBlockManifSeancelocal" value="C:\files\[idstructure]\INDIV/IHM/[xmlfilename][.lang].xml" />
    <!--<add key="FileBlockManifSeance" value="/files/TEST/[idstructure]/IHM/[idstructure]/[xmlfilename][.lang].xml"></add>-->
    <add key="FileBlockManifSeance" value="D:\customerfiles\DEV\[idstructure]\INDIV\IHM/[xmlfilename][.lang].xml" />
    <add key="JsonStepShowChoixSurPlan" value="D:\customerfiles\DEV\[idstructure]\Indiv\APPSETINGS\stepshowchoixsurplan.json" />
    <add key="xmlTranslatePlateform" value="D:\customerfiles\DEV\[idstructure]\[plateforme]\RESOURCES\translate[.lang].xml" />
    <add key="physicalPathOfSettingsJSON" value="D:\customerfiles\DEV\[idstructureSur4zeros]\[plateformCode]\APPSETTINGS\appsettings.json" />
    <add key="physicalPathOfSettingsGlobalJSON" value="D:\customerfiles\DEV\[idstructureSur4zeros]\[plateformCode]\appsettings.json" />
    <add key="physicalPathOfSettingsMergedJSON" value=" D:\customerfiles\DEV\[idstructureSur4zeros]\ADMIN\APPSETTINGS\[plateformCode]\" />
    <add key="path_script_sql_commons" value="D:\WORK\LIBRARIES\THEMIS_LIBRARIES_SQLSCRIPTS\Themis.Libraries.SqlScripts\Themis.Libraries.SqlScripts\[directory\][filename][.structureid].sql" />
    <add key="widgetJSCustomersPathDev" value="https://dev.themisweb.fr/sphere/widgets/customers/v1/widget-js" />
    <add key="widgetJSCustomersPath" value="https://localhost:44354/widget-js" />
    <add key="widgetJSAdminspherePath" value="https://localhost:44354/widget-js" />
    <add key="AdminSphereTranslationsUrl" value="https://dev.themisweb.fr/adminsphere/current/[idstructureSur4zeros]/[langCode]/translations" />
    <add key="widgetJSCustomersPath" value="https://dev2.themisweb.fr/widgets/customers/v1/widget-js" />
    <add key="widgetJSAdminPath" value="https://dev2.themisweb.fr/admin/v1/widget-js" />
    <add key="widgetAdminPath" value="https://dev2.themisweb.fr/admin/v1/" />
    <add key="RodriguePartnerName" value="RODRIGUE" />
    <add key="QueuingStructuresJson" value="\\**************\sites\DEV\Queue\configStructures.json" />
    <add key="QueuingV1Url" value="https://dev.themisweb.fr/" />
    <add key="QueuingV2Url_" value="https://dev.themisweb.fr/queue/inQueue/[idstructure]/[langcode]" />
    <add key="QueuingV2Url" value="https://waitingpage.themisweb.fr/[idstructureSur4zeros]/inQueue/[idstructure]/[langcode]" />
    <add key="MaintenanceUrl" value="https://dev.themisweb.fr/" />
    <add key="StatisticsUrl" value="https://dev.themisweb.fr/queue/adminPage/[idstructure]" />
    <add key="QueuingFileExtension" value="_[idstructureSur4zeros].htm" />
    <add key="MaxNumberOfPeople" value="2000" />
    <add key="DelaiBeforeInactivity" value="600" />
    <add key="NumberOfPeoplePerPassage" value="500" />
    <add key="WidgetModePagesAvailable" value="flistemanifs,fchoixSeance,fchoixSeanceOnMultiSeance,fpaniers" />
    <add key="AdminSphereCrossSellingUrl" value="https://dev2.themisweb.fr/admin/v1/[idstructureSur4zeros]/[langCode]/crossselling" />
    <add key="AdminSphereSalesSettingsUrl" value="https://dev2.themisweb.fr/admin/v1/[idstructureSur4zeros]/[langCode]/sales-settings" />
    <add key="AdminSphereHomeModularUrl" value="https://dev2.themisweb.fr/admin/v1/[idstructureSur4zeros]/[langCode]/home-modular" />
    <add key="AdminSphereOfferCatalogUrl" value="https://dev2.themisweb.fr/admin/v1/[idstructureSur4zeros]/[langCode]/CatalogCustomSettings" />
    <add key="DomainsUrlsIndiv" value="https://indiv.themisweb.fr/,https://indiv.themisweb.de/,https://indiv.themisweb.net/" />
    <add key="ThemisDomainNameStructurePrefs" value="THEMISDOMAINNAME" />
    <!--<add key="QueuingV1Url" value="https://dev.themisweb.fr/[fileName]_[idstructureSur4zeros].htm"/>
    <add key="QueuingV2Url" value="https://dev.themisweb.fr/queue/inQueue/[idstructure]/[langcode]"/>
    <add key="MaintenanceUrl" value="https://dev.themisweb.fr/[fileName]_[idstructureSur4zeros].htm"/>  
    <add key="StatisticsUrl" value="https://dev.themisweb.fr/queue/adminPage/[idstructure]"/>
    <add key="QueuingFileExtension" value="_[idstructureSur4zeros].htm"/>-->
    <!-- API URL 
	  <add key="apiAuthentifUrl" value="http://back-themis-ws/WSDEV/API/AUTHENTICATIONS/v1/api" />
	  <add key="apiAuthentifPartenaire" value="RODRIGUE" />
	  <add key="apiAuthentifSignature" value="ser5#E6V6Z#Mp-7" />-->
    <add key="apiAuthentifUrl" value="http://back-themis-ws/WS/API/AUTHENTICATIONS/v110/api" />
    <add key="apiAuthentifPartenaire" value="RODRIGUE" />
    <add key="apiAuthentifSignature" value="2f5*+1*gHafgff8" />
    <add key="apiCustomerUrlProd" value="http://**************/PROD/api_customers/v118/" />
    <add key="apiCustomerUrlDev" value="http://**************/dev/api_customers/v1/api/" />
    <add key="apiCustomerUrl" value="http://**************/test/api_customers/current/api/" />
    <add key="apiCatalogUrlLocal" value="https://localhost:44342/api/" />
    <add key="apiCatalogUrl" value="http://**************/test/api_catalog/current/api/" />
    <!--<add key="apiCustomerUrl" value="http://**************/PROD/api_customers/v119/api/" />-->
  </appSettings>
  <connectionStrings>
    <add name="WSAdminConnectionString" connectionString="Data Source=************;Initial Catalog=WSAdmin;User ID=SupportRod;Password=****************************************************************************************" providerName="System.Data.SqlClient" />
    <add name="WebLibraryConnectionString" connectionString="Data Source=************;Initial Catalog=WSAdmin;User ID=sa;Password=************************" providerName="System.Data.SqlClient" />
    <add name="WSAdminConnectionStringTest" connectionString="Data Source=*************;Initial Catalog=WSAdmin_test;User ID=SphereWebTest;Password=************************************************************************************************************" providerName="System.Data.SqlClient" />
    <add name="WebLibraryConnectionStringTest" connectionString="Data Source=*************;Initial Catalog=GlobalWebLibrary;User ID=SphereWebTest;Password=************************************************************************************************************" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <!--
    Pour obtenir une description des modifications de web.config, voir http://go.microsoft.com/fwlink/?LinkId=235367.

    Les attributs suivants peuvent être définis dans la balise <httpRuntime>.
      <system.Web>
        <httpRuntime targetFramework="4.5" />
      </system.Web>
  -->
  <system.web>
    <compilation targetFramework="4.8" />
    <authentication mode="Forms">
      <forms loginUrl="~/Login.aspx" timeout="2880" />
    </authentication>
    <pages controlRenderingCompatibilityVersion="4.0" />
    <httpRuntime maxRequestLength="**********" executionTimeout="3600" />
  </system.web>
  <system.web.extensions>
    <scripting>
      <webServices>
        <jsonSerialization maxJsonLength="50000000" />
      </webServices>
    </scripting>
  </system.web.extensions>
  <system.webServer>
    <modules runAllManagedModulesForAllRequests="true" />
    <!--<httpErrors errorMode="Custom" existingResponse="Replace"  >
			<remove statusCode="500"/>
			<error statusCode="500" path="/500.aspx" responseMode="Redirect"/>
			<remove statusCode="404"/>
			<error statusCode="404" path="/404.aspx" responseMode="Redirect"/>
			<remove statusCode="400"/>
			<error statusCode="400" path="/400.aspx" responseMode="Redirect"/>
		</httpErrors>-->
  </system.webServer>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="WSThemisAdminSoap" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="965536" maxReceivedMessageSize="965536" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="None">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
        <binding name="BasicHttpBinding_Iwcf_wsThemis" />
        <binding name="BasicHttpBinding_Iwcf_webPaiement" />
      </basicHttpBinding>
      <customBinding>
        <binding name="WSThemisAdminSoap12">
          <textMessageEncoding maxReadPoolSize="64" maxWritePoolSize="16" messageVersion="Soap12" writeEncoding="utf-8">
            <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          </textMessageEncoding>
          <httpTransport manualAddressing="false" maxBufferPoolSize="524288" maxReceivedMessageSize="965536" allowCookies="false" authenticationScheme="Anonymous" bypassProxyOnLocal="false" decompressionEnabled="true" hostNameComparisonMode="StrongWildcard" keepAliveEnabled="true" maxBufferSize="965536" proxyAuthenticationScheme="Anonymous" realm="" transferMode="Buffered" unsafeConnectionNtlmAuthentication="false" useDefaultWebProxy="true" />
        </binding>
      </customBinding>
      <wsHttpBinding>
        <binding name="WSHttpBinding_IWSAdmin" sendTimeout="00:03:00" maxBufferPoolSize="**********" maxReceivedMessageSize="**********" messageEncoding="Text" textEncoding="utf-8" allowCookies="false">
          <readerQuotas maxDepth="256" maxStringContentLength="**********" maxArrayLength="**********" maxBytesPerRead="**********" maxNameTableCharCount="**********" />
        </binding>
        <binding name="WSHttpBinding_IWSAdmin1" />
        <binding name="WSHttpBinding_IWSAdmin2" />
      </wsHttpBinding>
      <webHttpBinding>
        <binding name="MessageSizeWeb" closeTimeout="00:01:00" openTimeout="00:03:00" receiveTimeout="00:10:00" sendTimeout="00:03:00" maxBufferSize="**********" maxReceivedMessageSize="**********" />
      </webHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://localhost:55660/wcf-wsThemis.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Iwcf_wsThemis" contract="wcfThemis.Iwcf_wsThemis" name="BasicHttpBinding_Iwcf_wsThemis" />
      <endpoint address="http://localhost:56304/wcf_webPaiement.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Iwcf_webPaiement" contract="wcfPaiement.Iwcf_webPaiement" name="BasicHttpBinding_Iwcf_webPaiement" />
      <!--<endpoint address="http://back-themis-ws/wsdev/Adminv2/WSThemisAdmin2010.WSAdmin.svc" binding="wsHttpBinding" bindingConfiguration="WSHttpBinding_IWSAdmin" contract="wsThemisAdmin2010.IWSAdmin" name="WSHttpBinding_IWSAdmin">
        <identity>
          <dns value="localhost" />
        </identity>
      </endpoint>-->
      <endpoint address="http://localhost:8732/WSThemisAdmin2010/WSAdmin/" binding="wsHttpBinding" bindingConfiguration="WSHttpBinding_IWSAdmin" contract="wsThemisAdmin2010.IWSAdmin" name="WSHttpBinding_IWSAdmin">
        <identity>
          <dns value="localhost" />
        </identity>
      </endpoint>
    </client>
  </system.serviceModel>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="itextsharp" publicKeyToken="8354ae6d2174ddca" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="BouncyCastle.Crypto" publicKeyToken="0e99375e54769942" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="PdfSharp" publicKeyToken="f94615aa0424f9eb" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.50.5147.0" newVersion="1.50.5147.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.6" newVersion="9.0.0.6" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.2" newVersion="8.0.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.1" newVersion="8.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.2" newVersion="8.0.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.6" newVersion="9.0.0.6" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <applicationSettings>
    <login.Properties.Settings>
      <setting name="login_wsThemis_WSThemisAbo" serializeAs="String">
        <value>http://**************/wstest/themisrod/WSThemis.asmx</value>
      </setting>
      <setting name="login_WSThemisPaiement_WSThemisPaiement" serializeAs="String">
        <value>http://**************/WSTEST/Payment/WSThemispaiement.asmx</value>
      </setting>
      <setting name="login_WSThemisPaiement_WSThemisPaiementTest" serializeAs="String">
        <value>http://localhost:1011/WSThemisPaiement.asmx</value>
      </setting>
    </login.Properties.Settings>
  </applicationSettings>
</configuration>
<!--ProjectGuid: D4780C66-7317-43D6-87B8-AB2325DC6CEF-->