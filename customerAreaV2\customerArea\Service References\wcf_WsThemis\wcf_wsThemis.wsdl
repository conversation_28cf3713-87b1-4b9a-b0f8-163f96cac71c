<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="wcf_wsThemis" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://localhost:55660/wcf-wsThemis.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://localhost:55660/wcf-wsThemis.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://localhost:55660/wcf-wsThemis.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/ws_DTO" />
      <xsd:import schemaLocation="http://localhost:55660/wcf-wsThemis.svc?xsd=xsd3" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <xsd:import schemaLocation="http://localhost:55660/wcf-wsThemis.svc?xsd=xsd4" />
      <xsd:import schemaLocation="http://localhost:55660/wcf-wsThemis.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/ws_DTO.wt" />
      <xsd:import schemaLocation="http://localhost:55660/wcf-wsThemis.svc?xsd=xsd6" namespace="http://schemas.datacontract.org/2004/07/System.Collections.Specialized" />
      <xsd:import schemaLocation="http://localhost:55660/wcf-wsThemis.svc?xsd=xsd7" namespace="http://schemas.datacontract.org/2004/07/ws_DTO.objets_liaisons" />
    </xsd:schema>
  </wsdl:types>
 <wsdl:message name="Iwcf_wsThemis_FlagAutoAboMemePlace_InputMessage">
    <wsdl:part name="parameters" element="tns:FlagAutoAboMemePlace" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FlagAutoAboMemePlace_OutputMessage">
    <wsdl:part name="parameters" element="tns:FlagAutoAboMemePlaceResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FlagManuel_tempo_InputMessage">
    <wsdl:part name="parameters" element="tns:FlagManuel_tempo" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FlagManuel_tempo_OutputMessage">
    <wsdl:part name="parameters" element="tns:FlagManuel_tempoResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UnFlagManuel_tempo_InputMessage">
    <wsdl:part name="parameters" element="tns:UnFlagManuel_tempo" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UnFlagManuel_tempo_OutputMessage">
    <wsdl:part name="parameters" element="tns:UnFlagManuel_tempoResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UnFlagSeats_InputMessage">
    <wsdl:part name="parameters" element="tns:UnFlagSeats" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UnFlagSeats_OutputMessage">
    <wsdl:part name="parameters" element="tns:UnFlagSeatsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UnFlagSeatsManuel_tempo_InputMessage">
    <wsdl:part name="parameters" element="tns:UnFlagSeatsManuel_tempo" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UnFlagSeatsManuel_tempo_OutputMessage">
    <wsdl:part name="parameters" element="tns:UnFlagSeatsManuel_tempoResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UnFlagSeatsManuel_InputMessage">
    <wsdl:part name="parameters" element="tns:UnFlagSeatsManuel" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UnFlagSeatsManuel_OutputMessage">
    <wsdl:part name="parameters" element="tns:UnFlagSeatsManuelResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_ReFlagSeatsManuel_tempo_InputMessage">
    <wsdl:part name="parameters" element="tns:ReFlagSeatsManuel_tempo" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_ReFlagSeatsManuel_tempo_OutputMessage">
    <wsdl:part name="parameters" element="tns:ReFlagSeatsManuel_tempoResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadSeats_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadSeats" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadSeats_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadSeatsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadSeatsTexts_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadSeatsTexts" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadSeatsTexts_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadSeatsTextsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadLieusOfSessions_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadLieusOfSessions" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadLieusOfSessions_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadLieusOfSessionsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadInfoComp_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadInfoComp" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadInfoComp_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadInfoCompResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetInfoCompIDList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetInfoCompIDList" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetInfoCompIDList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetInfoCompIDListResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetListInfoCompOnIdentite_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListInfoCompOnIdentite" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetListInfoCompOnIdentite_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListInfoCompOnIdentiteResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetCustomerAllPurchaseHistoryDetails_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCustomerAllPurchaseHistoryDetails" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetCustomerAllPurchaseHistoryDetails_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCustomerAllPurchaseHistoryDetailsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetProfilAcheteurPurchaseHistory_InputMessage">
    <wsdl:part name="parameters" element="tns:GetProfilAcheteurPurchaseHistory" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetProfilAcheteurPurchaseHistory_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetProfilAcheteurPurchaseHistoryResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdateCustomerProfile_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCustomerProfile" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdateCustomerProfile_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCustomerProfileResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetCustomerProfileOfId_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCustomerProfileOfId" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetCustomerProfileOfId_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCustomerProfileOfIdResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadCivilityNaming_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadCivilityNaming" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadCivilityNaming_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadCivilityNamingResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadFunctions_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadFunctions" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadFunctions_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadFunctionsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdateListInfoCompOnIdentite_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateListInfoCompOnIdentite" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdateListInfoCompOnIdentite_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateListInfoCompOnIdentiteResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdateInfoComp_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateInfoComp" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdateInfoComp_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateInfoCompResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetCustomerProfileOfEmailOrIdentiteId_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCustomerProfileOfEmailOrIdentiteId" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetCustomerProfileOfEmailOrIdentiteId_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCustomerProfileOfEmailOrIdentiteIdResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_CreateCustomerProfileAndReturnID_InputMessage">
    <wsdl:part name="parameters" element="tns:CreateCustomerProfileAndReturnID" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_CreateCustomerProfileAndReturnID_OutputMessage">
    <wsdl:part name="parameters" element="tns:CreateCustomerProfileAndReturnIDResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadDevise_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadDevise" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadDevise_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadDeviseResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsListWaitList_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsListWaitList" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsListWaitList_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsListWaitListResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_InsertWaitList_InputMessage">
    <wsdl:part name="parameters" element="tns:InsertWaitList" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_InsertWaitList_OutputMessage">
    <wsdl:part name="parameters" element="tns:InsertWaitListResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetWaitListOfIdentite_InputMessage">
    <wsdl:part name="parameters" element="tns:GetWaitListOfIdentite" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetWaitListOfIdentite_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetWaitListOfIdentiteResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_DeleteIdentiteOfWaitList_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteIdentiteOfWaitList" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_DeleteIdentiteOfWaitList_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteIdentiteOfWaitListResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsListOfToday_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsListOfToday" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsListOfToday_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsListOfTodayResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetListCoupeFileCommandes_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListCoupeFileCommandes" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetListCoupeFileCommandes_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListCoupeFileCommandesResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdateDossierCommentaire_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateDossierCommentaire" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdateDossierCommentaire_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateDossierCommentaireResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_TAH_MakePDF_FromCmdCoupeFile_InputMessage">
    <wsdl:part name="parameters" element="tns:TAH_MakePDF_FromCmdCoupeFile" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_TAH_MakePDF_FromCmdCoupeFile_OutputMessage">
    <wsdl:part name="parameters" element="tns:TAH_MakePDF_FromCmdCoupeFileResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetCurrentBasketAbonnement_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCurrentBasketAbonnement" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetCurrentBasketAbonnement_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCurrentBasketAbonnementResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetCurrentCustomBasketAbonnement_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCurrentCustomBasketAbonnement" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetCurrentCustomBasketAbonnement_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCurrentCustomBasketAbonnementResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetListPropertiesOfEvent_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListPropertiesOfEvent" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetListPropertiesOfEvent_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListPropertiesOfEventResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadReservationList_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadReservationList" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadReservationList_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadReservationListResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadLanguages_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadLanguages" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadLanguages_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadLanguagesResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetLangueIdOfLangCode_InputMessage">
    <wsdl:part name="parameters" element="tns:GetLangueIdOfLangCode" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetLangueIdOfLangCode_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetLangueIdOfLangCodeResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_nextDelayRefresh_InputMessage">
    <wsdl:part name="parameters" element="tns:nextDelayRefresh" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_nextDelayRefresh_OutputMessage">
    <wsdl:part name="parameters" element="tns:nextDelayRefreshResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEvents_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadEvents" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEvents_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsInGp_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsInGp" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsInGp_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsInGpResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsOfFiliere_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsOfFiliere" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsOfFiliere_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsOfFiliereResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadDispoFuturesSessions_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadDispoFuturesSessions" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadDispoFuturesSessions_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadDispoFuturesSessionsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdatePasswordIdentite_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdatePasswordIdentite" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdatePasswordIdentite_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdatePasswordIdentiteResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadIdentite_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadIdentite" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadIdentite_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadIdentiteResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_CheckIdentite_InputMessage">
    <wsdl:part name="parameters" element="tns:CheckIdentite" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_CheckIdentite_OutputMessage">
    <wsdl:part name="parameters" element="tns:CheckIdentiteResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FillDroitsFacture_InputMessage">
    <wsdl:part name="parameters" element="tns:FillDroitsFacture" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FillDroitsFacture_OutputMessage">
    <wsdl:part name="parameters" element="tns:FillDroitsFactureResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FillAcompte_InputMessage">
    <wsdl:part name="parameters" element="tns:FillAcompte" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FillAcompte_OutputMessage">
    <wsdl:part name="parameters" element="tns:FillAcompteResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadCommandes_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadCommandes" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadCommandes_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadCommandesResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FanCard_LoadIdentiteChild_InputMessage">
    <wsdl:part name="parameters" element="tns:FanCard_LoadIdentiteChild" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FanCard_LoadIdentiteChild_OutputMessage">
    <wsdl:part name="parameters" element="tns:FanCard_LoadIdentiteChildResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FanCard_AddRelation_InputMessage">
    <wsdl:part name="parameters" element="tns:FanCard_AddRelation" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FanCard_AddRelation_OutputMessage">
    <wsdl:part name="parameters" element="tns:FanCard_AddRelationResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FanCard_UpdateRelation_InputMessage">
    <wsdl:part name="parameters" element="tns:FanCard_UpdateRelation" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FanCard_UpdateRelation_OutputMessage">
    <wsdl:part name="parameters" element="tns:FanCard_UpdateRelationResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FanCard_LoadAllIdentiteCommandesFans_InputMessage">
    <wsdl:part name="parameters" element="tns:FanCard_LoadAllIdentiteCommandesFans" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FanCard_LoadAllIdentiteCommandesFans_OutputMessage">
    <wsdl:part name="parameters" element="tns:FanCard_LoadAllIdentiteCommandesFansResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_IsGestionnaireReaboFans_InputMessage">
    <wsdl:part name="parameters" element="tns:IsGestionnaireReaboFans" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_IsGestionnaireReaboFans_OutputMessage">
    <wsdl:part name="parameters" element="tns:IsGestionnaireReaboFansResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FanCard_DeleteRelation_InputMessage">
    <wsdl:part name="parameters" element="tns:FanCard_DeleteRelation" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FanCard_DeleteRelation_OutputMessage">
    <wsdl:part name="parameters" element="tns:FanCard_DeleteRelationResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetDemandResetPasswordById_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDemandResetPasswordById" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetDemandResetPasswordById_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDemandResetPasswordByIdResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdateUseDateDemandResetPassword_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateUseDateDemandResetPassword" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UpdateUseDateDemandResetPassword_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateUseDateDemandResetPasswordResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_AddDemandResetPassword_InputMessage">
    <wsdl:part name="parameters" element="tns:AddDemandResetPassword" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_AddDemandResetPassword_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddDemandResetPasswordResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_DeleteMyIdentite_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteMyIdentite" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_DeleteMyIdentite_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteMyIdentiteResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_getConsommateursForSeances_InputMessage">
    <wsdl:part name="parameters" element="tns:getConsommateursForSeances" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_getConsommateursForSeances_OutputMessage">
    <wsdl:part name="parameters" element="tns:getConsommateursForSeancesResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_getConsommateurs_InputMessage">
    <wsdl:part name="parameters" element="tns:getConsommateurs" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_getConsommateurs_OutputMessage">
    <wsdl:part name="parameters" element="tns:getConsommateursResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_AddLinkConsumers_InputMessage">
    <wsdl:part name="parameters" element="tns:AddLinkConsumers" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_AddLinkConsumers_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddLinkConsumersResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UnLinkConsumers_InputMessage">
    <wsdl:part name="parameters" element="tns:UnLinkConsumers" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_UnLinkConsumers_OutputMessage">
    <wsdl:part name="parameters" element="tns:UnLinkConsumersResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_ProfilAcheteurLogin_InputMessage">
    <wsdl:part name="parameters" element="tns:ProfilAcheteurLogin" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_ProfilAcheteurLogin_OutputMessage">
    <wsdl:part name="parameters" element="tns:ProfilAcheteurLoginResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_ProfilAcheteurLoginById_InputMessage">
    <wsdl:part name="parameters" element="tns:ProfilAcheteurLoginById" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_ProfilAcheteurLoginById_OutputMessage">
    <wsdl:part name="parameters" element="tns:ProfilAcheteurLoginByIdResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadGrilleTarifs_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadGrilleTarifs" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadGrilleTarifs_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadGrilleTarifsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadGrilleTarifsPlacement_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadGrilleTarifsPlacement" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadGrilleTarifsPlacement_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadGrilleTarifsPlacementResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadAllInternetGrilleTarifs_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadAllInternetGrilleTarifs" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadAllInternetGrilleTarifs_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadAllInternetGrilleTarifsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadAmounts_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadAmounts" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadAmounts_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadAmountsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadGroupFormulas_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadGroupFormulas" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadGroupFormulas_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadGroupFormulasResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadFormulas_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadFormulas" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadFormulas_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadFormulasResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsOfFormulas_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsOfFormulas" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsOfFormulas_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsOfFormulasResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadContraintesOfFormulas_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadContraintesOfFormulas" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadContraintesOfFormulas_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadContraintesOfFormulasResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsHorsAbo_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsHorsAbo" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadEventsHorsAbo_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadEventsHorsAboResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadGrilleTarifsHorsAbo_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadGrilleTarifsHorsAbo" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadGrilleTarifsHorsAbo_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadGrilleTarifsHorsAboResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadGrilleTarifsPlacesSupplementaires_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadGrilleTarifsPlacesSupplementaires" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadGrilleTarifsPlacesSupplementaires_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadGrilleTarifsPlacesSupplementairesResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetLieuBySessionId_InputMessage">
    <wsdl:part name="parameters" element="tns:GetLieuBySessionId" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetLieuBySessionId_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetLieuBySessionIdResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadProductsOfFormulas_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadProductsOfFormulas" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadProductsOfFormulas_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadProductsOfFormulasResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadFraisProductsOfFormulas_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadFraisProductsOfFormulas" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadFraisProductsOfFormulas_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadFraisProductsOfFormulasResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetListeReserveForFormula_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListeReserveForFormula" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetListeReserveForFormula_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListeReserveForFormulaResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetReserveList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetReserveList" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetReserveList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetReserveListResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetTypePrisePlaceOfFormula_InputMessage">
    <wsdl:part name="parameters" element="tns:GetTypePrisePlaceOfFormula" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_GetTypePrisePlaceOfFormula_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetTypePrisePlaceOfFormulaResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadFraisEnvoisReabo_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadFraisEnvoisReabo" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadFraisEnvoisReabo_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadFraisEnvoisReaboResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadProducts_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadProducts" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadProducts_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadProductsResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadCommonsMO_ListFormulaTarifSeance_abo_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadCommonsMO_ListFormulaTarifSeance_abo" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadCommonsMO_ListFormulaTarifSeance_abo_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadCommonsMO_ListFormulaTarifSeance_aboResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadMaquettesOfMoGp_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadMaquettesOfMoGp" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadMaquettesOfMoGp_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadMaquettesOfMoGpResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadQuestionnairesProductsOfFormulas_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadQuestionnairesProductsOfFormulas" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadQuestionnairesProductsOfFormulas_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadQuestionnairesProductsOfFormulasResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_Basket_FillFromOpen_InputMessage">
    <wsdl:part name="parameters" element="tns:Basket_FillFromOpen" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_Basket_FillFromOpen_OutputMessage">
    <wsdl:part name="parameters" element="tns:Basket_FillFromOpenResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_BasketFillFromOpen_InputMessage">
    <wsdl:part name="parameters" element="tns:BasketFillFromOpen" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_BasketFillFromOpen_OutputMessage">
    <wsdl:part name="parameters" element="tns:BasketFillFromOpenResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_BasketTransformation_InputMessage">
    <wsdl:part name="parameters" element="tns:BasketTransformation" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_BasketTransformation_OutputMessage">
    <wsdl:part name="parameters" element="tns:BasketTransformationResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_TAH_MakePDF_FromCmd_InputMessage">
    <wsdl:part name="parameters" element="tns:TAH_MakePDF_FromCmd" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_TAH_MakePDF_FromCmd_OutputMessage">
    <wsdl:part name="parameters" element="tns:TAH_MakePDF_FromCmdResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_EditerCommande_InputMessage">
    <wsdl:part name="parameters" element="tns:EditerCommande" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_EditerCommande_OutputMessage">
    <wsdl:part name="parameters" element="tns:EditerCommandeResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_DepotVente_ispossible_InputMessage">
    <wsdl:part name="parameters" element="tns:DepotVente_ispossible" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_DepotVente_ispossible_OutputMessage">
    <wsdl:part name="parameters" element="tns:DepotVente_ispossibleResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_DepotVente_GetMontantReprise_InputMessage">
    <wsdl:part name="parameters" element="tns:DepotVente_GetMontantReprise" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_DepotVente_GetMontantReprise_OutputMessage">
    <wsdl:part name="parameters" element="tns:DepotVente_GetMontantRepriseResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_DepotVente_put_InputMessage">
    <wsdl:part name="parameters" element="tns:DepotVente_put" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_DepotVente_put_OutputMessage">
    <wsdl:part name="parameters" element="tns:DepotVente_putResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_MarquerConsommateur_InputMessage">
    <wsdl:part name="parameters" element="tns:MarquerConsommateur" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_MarquerConsommateur_OutputMessage">
    <wsdl:part name="parameters" element="tns:MarquerConsommateurResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadIdentityRIB_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadIdentityRIB" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_LoadIdentityRIB_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadIdentityRIBResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_AddIdentityRIB_InputMessage">
    <wsdl:part name="parameters" element="tns:AddIdentityRIB" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_AddIdentityRIB_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddIdentityRIBResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FlagAuto_InputMessage">
    <wsdl:part name="parameters" element="tns:FlagAuto" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FlagAuto_OutputMessage">
    <wsdl:part name="parameters" element="tns:FlagAutoResponse" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FlagAutoAboFermeMemePlace_InputMessage">
    <wsdl:part name="parameters" element="tns:FlagAutoAboFermeMemePlace" />
  </wsdl:message>
  <wsdl:message name="Iwcf_wsThemis_FlagAutoAboFermeMemePlace_OutputMessage">
    <wsdl:part name="parameters" element="tns:FlagAutoAboFermeMemePlaceResponse" />
  </wsdl:message>
  <wsdl:portType name="Iwcf_wsThemis">
    <wsdl:operation name="FlagAutoAboMemePlace">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FlagAutoAboMemePlace" message="tns:Iwcf_wsThemis_FlagAutoAboMemePlace_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FlagAutoAboMemePlaceResponse" message="tns:Iwcf_wsThemis_FlagAutoAboMemePlace_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FlagManuel_tempo">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FlagManuel_tempo" message="tns:Iwcf_wsThemis_FlagManuel_tempo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FlagManuel_tempoResponse" message="tns:Iwcf_wsThemis_FlagManuel_tempo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UnFlagManuel_tempo">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UnFlagManuel_tempo" message="tns:Iwcf_wsThemis_UnFlagManuel_tempo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UnFlagManuel_tempoResponse" message="tns:Iwcf_wsThemis_UnFlagManuel_tempo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UnFlagSeats">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeats" message="tns:Iwcf_wsThemis_UnFlagSeats_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsResponse" message="tns:Iwcf_wsThemis_UnFlagSeats_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UnFlagSeatsManuel_tempo">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel_tempo" message="tns:Iwcf_wsThemis_UnFlagSeatsManuel_tempo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel_tempoResponse" message="tns:Iwcf_wsThemis_UnFlagSeatsManuel_tempo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UnFlagSeatsManuel">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel" message="tns:Iwcf_wsThemis_UnFlagSeatsManuel_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuelResponse" message="tns:Iwcf_wsThemis_UnFlagSeatsManuel_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ReFlagSeatsManuel_tempo">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/ReFlagSeatsManuel_tempo" message="tns:Iwcf_wsThemis_ReFlagSeatsManuel_tempo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/ReFlagSeatsManuel_tempoResponse" message="tns:Iwcf_wsThemis_ReFlagSeatsManuel_tempo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadSeats">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadSeats" message="tns:Iwcf_wsThemis_LoadSeats_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadSeatsResponse" message="tns:Iwcf_wsThemis_LoadSeats_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadSeatsTexts">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadSeatsTexts" message="tns:Iwcf_wsThemis_LoadSeatsTexts_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadSeatsTextsResponse" message="tns:Iwcf_wsThemis_LoadSeatsTexts_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadLieusOfSessions">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadLieusOfSessions" message="tns:Iwcf_wsThemis_LoadLieusOfSessions_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadLieusOfSessionsResponse" message="tns:Iwcf_wsThemis_LoadLieusOfSessions_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadInfoComp">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadInfoComp" message="tns:Iwcf_wsThemis_LoadInfoComp_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadInfoCompResponse" message="tns:Iwcf_wsThemis_LoadInfoComp_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetInfoCompIDList">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetInfoCompIDList" message="tns:Iwcf_wsThemis_GetInfoCompIDList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetInfoCompIDListResponse" message="tns:Iwcf_wsThemis_GetInfoCompIDList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListInfoCompOnIdentite">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetListInfoCompOnIdentite" message="tns:Iwcf_wsThemis_GetListInfoCompOnIdentite_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetListInfoCompOnIdentiteResponse" message="tns:Iwcf_wsThemis_GetListInfoCompOnIdentite_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCustomerAllPurchaseHistoryDetails">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerAllPurchaseHistoryDetails" message="tns:Iwcf_wsThemis_GetCustomerAllPurchaseHistoryDetails_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerAllPurchaseHistoryDetailsResponse" message="tns:Iwcf_wsThemis_GetCustomerAllPurchaseHistoryDetails_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetProfilAcheteurPurchaseHistory">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetProfilAcheteurPurchaseHistory" message="tns:Iwcf_wsThemis_GetProfilAcheteurPurchaseHistory_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetProfilAcheteurPurchaseHistoryResponse" message="tns:Iwcf_wsThemis_GetProfilAcheteurPurchaseHistory_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateCustomerProfile">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdateCustomerProfile" message="tns:Iwcf_wsThemis_UpdateCustomerProfile_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdateCustomerProfileResponse" message="tns:Iwcf_wsThemis_UpdateCustomerProfile_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCustomerProfileOfId">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfId" message="tns:Iwcf_wsThemis_GetCustomerProfileOfId_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfIdResponse" message="tns:Iwcf_wsThemis_GetCustomerProfileOfId_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadCivilityNaming">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadCivilityNaming" message="tns:Iwcf_wsThemis_LoadCivilityNaming_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadCivilityNamingResponse" message="tns:Iwcf_wsThemis_LoadCivilityNaming_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadFunctions">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadFunctions" message="tns:Iwcf_wsThemis_LoadFunctions_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadFunctionsResponse" message="tns:Iwcf_wsThemis_LoadFunctions_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateListInfoCompOnIdentite">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdateListInfoCompOnIdentite" message="tns:Iwcf_wsThemis_UpdateListInfoCompOnIdentite_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdateListInfoCompOnIdentiteResponse" message="tns:Iwcf_wsThemis_UpdateListInfoCompOnIdentite_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateInfoComp">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdateInfoComp" message="tns:Iwcf_wsThemis_UpdateInfoComp_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdateInfoCompResponse" message="tns:Iwcf_wsThemis_UpdateInfoComp_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCustomerProfileOfEmailOrIdentiteId">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfEmailOrIdentiteId" message="tns:Iwcf_wsThemis_GetCustomerProfileOfEmailOrIdentiteId_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfEmailOrIdentiteIdResponse" message="tns:Iwcf_wsThemis_GetCustomerProfileOfEmailOrIdentiteId_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CreateCustomerProfileAndReturnID">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/CreateCustomerProfileAndReturnID" message="tns:Iwcf_wsThemis_CreateCustomerProfileAndReturnID_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/CreateCustomerProfileAndReturnIDResponse" message="tns:Iwcf_wsThemis_CreateCustomerProfileAndReturnID_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadDevise">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadDevise" message="tns:Iwcf_wsThemis_LoadDevise_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadDeviseResponse" message="tns:Iwcf_wsThemis_LoadDevise_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadEventsListWaitList">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsListWaitList" message="tns:Iwcf_wsThemis_LoadEventsListWaitList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsListWaitListResponse" message="tns:Iwcf_wsThemis_LoadEventsListWaitList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="InsertWaitList">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/InsertWaitList" message="tns:Iwcf_wsThemis_InsertWaitList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/InsertWaitListResponse" message="tns:Iwcf_wsThemis_InsertWaitList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetWaitListOfIdentite">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetWaitListOfIdentite" message="tns:Iwcf_wsThemis_GetWaitListOfIdentite_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetWaitListOfIdentiteResponse" message="tns:Iwcf_wsThemis_GetWaitListOfIdentite_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DeleteIdentiteOfWaitList">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/DeleteIdentiteOfWaitList" message="tns:Iwcf_wsThemis_DeleteIdentiteOfWaitList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/DeleteIdentiteOfWaitListResponse" message="tns:Iwcf_wsThemis_DeleteIdentiteOfWaitList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadEventsListOfToday">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsListOfToday" message="tns:Iwcf_wsThemis_LoadEventsListOfToday_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsListOfTodayResponse" message="tns:Iwcf_wsThemis_LoadEventsListOfToday_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListCoupeFileCommandes">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetListCoupeFileCommandes" message="tns:Iwcf_wsThemis_GetListCoupeFileCommandes_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetListCoupeFileCommandesResponse" message="tns:Iwcf_wsThemis_GetListCoupeFileCommandes_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateDossierCommentaire">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdateDossierCommentaire" message="tns:Iwcf_wsThemis_UpdateDossierCommentaire_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdateDossierCommentaireResponse" message="tns:Iwcf_wsThemis_UpdateDossierCommentaire_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="TAH_MakePDF_FromCmdCoupeFile">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmdCoupeFile" message="tns:Iwcf_wsThemis_TAH_MakePDF_FromCmdCoupeFile_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmdCoupeFileResponse" message="tns:Iwcf_wsThemis_TAH_MakePDF_FromCmdCoupeFile_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCurrentBasketAbonnement">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetCurrentBasketAbonnement" message="tns:Iwcf_wsThemis_GetCurrentBasketAbonnement_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetCurrentBasketAbonnementResponse" message="tns:Iwcf_wsThemis_GetCurrentBasketAbonnement_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCurrentCustomBasketAbonnement">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetCurrentCustomBasketAbonnement" message="tns:Iwcf_wsThemis_GetCurrentCustomBasketAbonnement_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetCurrentCustomBasketAbonnementResponse" message="tns:Iwcf_wsThemis_GetCurrentCustomBasketAbonnement_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListPropertiesOfEvent">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetListPropertiesOfEvent" message="tns:Iwcf_wsThemis_GetListPropertiesOfEvent_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetListPropertiesOfEventResponse" message="tns:Iwcf_wsThemis_GetListPropertiesOfEvent_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadReservationList">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadReservationList" message="tns:Iwcf_wsThemis_LoadReservationList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadReservationListResponse" message="tns:Iwcf_wsThemis_LoadReservationList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadLanguages">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadLanguages" message="tns:Iwcf_wsThemis_LoadLanguages_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadLanguagesResponse" message="tns:Iwcf_wsThemis_LoadLanguages_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetLangueIdOfLangCode">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetLangueIdOfLangCode" message="tns:Iwcf_wsThemis_GetLangueIdOfLangCode_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetLangueIdOfLangCodeResponse" message="tns:Iwcf_wsThemis_GetLangueIdOfLangCode_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="nextDelayRefresh">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/nextDelayRefresh" message="tns:Iwcf_wsThemis_nextDelayRefresh_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/nextDelayRefreshResponse" message="tns:Iwcf_wsThemis_nextDelayRefresh_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadEvents">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEvents" message="tns:Iwcf_wsThemis_LoadEvents_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsResponse" message="tns:Iwcf_wsThemis_LoadEvents_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadEventsInGp">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsInGp" message="tns:Iwcf_wsThemis_LoadEventsInGp_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsInGpResponse" message="tns:Iwcf_wsThemis_LoadEventsInGp_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadEventsOfFiliere">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFiliere" message="tns:Iwcf_wsThemis_LoadEventsOfFiliere_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFiliereResponse" message="tns:Iwcf_wsThemis_LoadEventsOfFiliere_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadDispoFuturesSessions">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadDispoFuturesSessions" message="tns:Iwcf_wsThemis_LoadDispoFuturesSessions_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadDispoFuturesSessionsResponse" message="tns:Iwcf_wsThemis_LoadDispoFuturesSessions_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdatePasswordIdentite">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdatePasswordIdentite" message="tns:Iwcf_wsThemis_UpdatePasswordIdentite_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdatePasswordIdentiteResponse" message="tns:Iwcf_wsThemis_UpdatePasswordIdentite_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadIdentite">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadIdentite" message="tns:Iwcf_wsThemis_LoadIdentite_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadIdentiteResponse" message="tns:Iwcf_wsThemis_LoadIdentite_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CheckIdentite">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/CheckIdentite" message="tns:Iwcf_wsThemis_CheckIdentite_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/CheckIdentiteResponse" message="tns:Iwcf_wsThemis_CheckIdentite_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FillDroitsFacture">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FillDroitsFacture" message="tns:Iwcf_wsThemis_FillDroitsFacture_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FillDroitsFactureResponse" message="tns:Iwcf_wsThemis_FillDroitsFacture_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FillAcompte">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FillAcompte" message="tns:Iwcf_wsThemis_FillAcompte_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FillAcompteResponse" message="tns:Iwcf_wsThemis_FillAcompte_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadCommandes">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadCommandes" message="tns:Iwcf_wsThemis_LoadCommandes_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadCommandesResponse" message="tns:Iwcf_wsThemis_LoadCommandes_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FanCard_LoadIdentiteChild">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadIdentiteChild" message="tns:Iwcf_wsThemis_FanCard_LoadIdentiteChild_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadIdentiteChildResponse" message="tns:Iwcf_wsThemis_FanCard_LoadIdentiteChild_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FanCard_AddRelation">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FanCard_AddRelation" message="tns:Iwcf_wsThemis_FanCard_AddRelation_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FanCard_AddRelationResponse" message="tns:Iwcf_wsThemis_FanCard_AddRelation_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FanCard_UpdateRelation">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FanCard_UpdateRelation" message="tns:Iwcf_wsThemis_FanCard_UpdateRelation_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FanCard_UpdateRelationResponse" message="tns:Iwcf_wsThemis_FanCard_UpdateRelation_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FanCard_LoadAllIdentiteCommandesFans">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadAllIdentiteCommandesFans" message="tns:Iwcf_wsThemis_FanCard_LoadAllIdentiteCommandesFans_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadAllIdentiteCommandesFansResponse" message="tns:Iwcf_wsThemis_FanCard_LoadAllIdentiteCommandesFans_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="IsGestionnaireReaboFans">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/IsGestionnaireReaboFans" message="tns:Iwcf_wsThemis_IsGestionnaireReaboFans_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/IsGestionnaireReaboFansResponse" message="tns:Iwcf_wsThemis_IsGestionnaireReaboFans_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FanCard_DeleteRelation">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FanCard_DeleteRelation" message="tns:Iwcf_wsThemis_FanCard_DeleteRelation_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FanCard_DeleteRelationResponse" message="tns:Iwcf_wsThemis_FanCard_DeleteRelation_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDemandResetPasswordById">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetDemandResetPasswordById" message="tns:Iwcf_wsThemis_GetDemandResetPasswordById_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetDemandResetPasswordByIdResponse" message="tns:Iwcf_wsThemis_GetDemandResetPasswordById_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateUseDateDemandResetPassword">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdateUseDateDemandResetPassword" message="tns:Iwcf_wsThemis_UpdateUseDateDemandResetPassword_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UpdateUseDateDemandResetPasswordResponse" message="tns:Iwcf_wsThemis_UpdateUseDateDemandResetPassword_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddDemandResetPassword">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/AddDemandResetPassword" message="tns:Iwcf_wsThemis_AddDemandResetPassword_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/AddDemandResetPasswordResponse" message="tns:Iwcf_wsThemis_AddDemandResetPassword_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DeleteMyIdentite">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/DeleteMyIdentite" message="tns:Iwcf_wsThemis_DeleteMyIdentite_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/DeleteMyIdentiteResponse" message="tns:Iwcf_wsThemis_DeleteMyIdentite_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="getConsommateursForSeances">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/getConsommateursForSeances" message="tns:Iwcf_wsThemis_getConsommateursForSeances_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/getConsommateursForSeancesResponse" message="tns:Iwcf_wsThemis_getConsommateursForSeances_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="getConsommateurs">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/getConsommateurs" message="tns:Iwcf_wsThemis_getConsommateurs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/getConsommateursResponse" message="tns:Iwcf_wsThemis_getConsommateurs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddLinkConsumers">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/AddLinkConsumers" message="tns:Iwcf_wsThemis_AddLinkConsumers_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/AddLinkConsumersResponse" message="tns:Iwcf_wsThemis_AddLinkConsumers_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UnLinkConsumers">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UnLinkConsumers" message="tns:Iwcf_wsThemis_UnLinkConsumers_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/UnLinkConsumersResponse" message="tns:Iwcf_wsThemis_UnLinkConsumers_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ProfilAcheteurLogin">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/ProfilAcheteurLogin" message="tns:Iwcf_wsThemis_ProfilAcheteurLogin_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/ProfilAcheteurLoginResponse" message="tns:Iwcf_wsThemis_ProfilAcheteurLogin_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ProfilAcheteurLoginById">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/ProfilAcheteurLoginById" message="tns:Iwcf_wsThemis_ProfilAcheteurLoginById_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/ProfilAcheteurLoginByIdResponse" message="tns:Iwcf_wsThemis_ProfilAcheteurLoginById_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadGrilleTarifs">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifs" message="tns:Iwcf_wsThemis_LoadGrilleTarifs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsResponse" message="tns:Iwcf_wsThemis_LoadGrilleTarifs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadGrilleTarifsPlacement">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsPlacement" message="tns:Iwcf_wsThemis_LoadGrilleTarifsPlacement_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsPlacementResponse" message="tns:Iwcf_wsThemis_LoadGrilleTarifsPlacement_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadAllInternetGrilleTarifs">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadAllInternetGrilleTarifs" message="tns:Iwcf_wsThemis_LoadAllInternetGrilleTarifs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadAllInternetGrilleTarifsResponse" message="tns:Iwcf_wsThemis_LoadAllInternetGrilleTarifs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadAmounts">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadAmounts" message="tns:Iwcf_wsThemis_LoadAmounts_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadAmountsResponse" message="tns:Iwcf_wsThemis_LoadAmounts_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadGroupFormulas">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadGroupFormulas" message="tns:Iwcf_wsThemis_LoadGroupFormulas_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadGroupFormulasResponse" message="tns:Iwcf_wsThemis_LoadGroupFormulas_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadFormulas">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadFormulas" message="tns:Iwcf_wsThemis_LoadFormulas_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadFormulasResponse" message="tns:Iwcf_wsThemis_LoadFormulas_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadEventsOfFormulas">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFormulas" message="tns:Iwcf_wsThemis_LoadEventsOfFormulas_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFormulasResponse" message="tns:Iwcf_wsThemis_LoadEventsOfFormulas_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadContraintesOfFormulas">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadContraintesOfFormulas" message="tns:Iwcf_wsThemis_LoadContraintesOfFormulas_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadContraintesOfFormulasResponse" message="tns:Iwcf_wsThemis_LoadContraintesOfFormulas_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadEventsHorsAbo">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsHorsAbo" message="tns:Iwcf_wsThemis_LoadEventsHorsAbo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadEventsHorsAboResponse" message="tns:Iwcf_wsThemis_LoadEventsHorsAbo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadGrilleTarifsHorsAbo">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsHorsAbo" message="tns:Iwcf_wsThemis_LoadGrilleTarifsHorsAbo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsHorsAboResponse" message="tns:Iwcf_wsThemis_LoadGrilleTarifsHorsAbo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadGrilleTarifsPlacesSupplementaires">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsPlacesSupplementaires" message="tns:Iwcf_wsThemis_LoadGrilleTarifsPlacesSupplementaires_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsPlacesSupplementairesResponse" message="tns:Iwcf_wsThemis_LoadGrilleTarifsPlacesSupplementaires_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetLieuBySessionId">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetLieuBySessionId" message="tns:Iwcf_wsThemis_GetLieuBySessionId_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetLieuBySessionIdResponse" message="tns:Iwcf_wsThemis_GetLieuBySessionId_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadProductsOfFormulas">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadProductsOfFormulas" message="tns:Iwcf_wsThemis_LoadProductsOfFormulas_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadProductsOfFormulasResponse" message="tns:Iwcf_wsThemis_LoadProductsOfFormulas_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadFraisProductsOfFormulas">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadFraisProductsOfFormulas" message="tns:Iwcf_wsThemis_LoadFraisProductsOfFormulas_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadFraisProductsOfFormulasResponse" message="tns:Iwcf_wsThemis_LoadFraisProductsOfFormulas_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListeReserveForFormula">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetListeReserveForFormula" message="tns:Iwcf_wsThemis_GetListeReserveForFormula_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetListeReserveForFormulaResponse" message="tns:Iwcf_wsThemis_GetListeReserveForFormula_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetReserveList">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetReserveList" message="tns:Iwcf_wsThemis_GetReserveList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetReserveListResponse" message="tns:Iwcf_wsThemis_GetReserveList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetTypePrisePlaceOfFormula">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetTypePrisePlaceOfFormula" message="tns:Iwcf_wsThemis_GetTypePrisePlaceOfFormula_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/GetTypePrisePlaceOfFormulaResponse" message="tns:Iwcf_wsThemis_GetTypePrisePlaceOfFormula_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadFraisEnvoisReabo">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadFraisEnvoisReabo" message="tns:Iwcf_wsThemis_LoadFraisEnvoisReabo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadFraisEnvoisReaboResponse" message="tns:Iwcf_wsThemis_LoadFraisEnvoisReabo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadProducts">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadProducts" message="tns:Iwcf_wsThemis_LoadProducts_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadProductsResponse" message="tns:Iwcf_wsThemis_LoadProducts_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadCommonsMO_ListFormulaTarifSeance_abo">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadCommonsMO_ListFormulaTarifSeance_abo" message="tns:Iwcf_wsThemis_LoadCommonsMO_ListFormulaTarifSeance_abo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadCommonsMO_ListFormulaTarifSeance_aboResponse" message="tns:Iwcf_wsThemis_LoadCommonsMO_ListFormulaTarifSeance_abo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadMaquettesOfMoGp">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadMaquettesOfMoGp" message="tns:Iwcf_wsThemis_LoadMaquettesOfMoGp_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadMaquettesOfMoGpResponse" message="tns:Iwcf_wsThemis_LoadMaquettesOfMoGp_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadQuestionnairesProductsOfFormulas">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadQuestionnairesProductsOfFormulas" message="tns:Iwcf_wsThemis_LoadQuestionnairesProductsOfFormulas_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadQuestionnairesProductsOfFormulasResponse" message="tns:Iwcf_wsThemis_LoadQuestionnairesProductsOfFormulas_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="Basket_FillFromOpen">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/Basket_FillFromOpen" message="tns:Iwcf_wsThemis_Basket_FillFromOpen_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/Basket_FillFromOpenResponse" message="tns:Iwcf_wsThemis_Basket_FillFromOpen_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="BasketFillFromOpen">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/BasketFillFromOpen" message="tns:Iwcf_wsThemis_BasketFillFromOpen_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/BasketFillFromOpenResponse" message="tns:Iwcf_wsThemis_BasketFillFromOpen_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="BasketTransformation">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/BasketTransformation" message="tns:Iwcf_wsThemis_BasketTransformation_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/BasketTransformationResponse" message="tns:Iwcf_wsThemis_BasketTransformation_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="TAH_MakePDF_FromCmd">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmd" message="tns:Iwcf_wsThemis_TAH_MakePDF_FromCmd_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmdResponse" message="tns:Iwcf_wsThemis_TAH_MakePDF_FromCmd_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="EditerCommande">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/EditerCommande" message="tns:Iwcf_wsThemis_EditerCommande_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/EditerCommandeResponse" message="tns:Iwcf_wsThemis_EditerCommande_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DepotVente_ispossible">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_ispossible" message="tns:Iwcf_wsThemis_DepotVente_ispossible_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_ispossibleResponse" message="tns:Iwcf_wsThemis_DepotVente_ispossible_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DepotVente_GetMontantReprise">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_GetMontantReprise" message="tns:Iwcf_wsThemis_DepotVente_GetMontantReprise_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_GetMontantRepriseResponse" message="tns:Iwcf_wsThemis_DepotVente_GetMontantReprise_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DepotVente_put">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_put" message="tns:Iwcf_wsThemis_DepotVente_put_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/DepotVente_putResponse" message="tns:Iwcf_wsThemis_DepotVente_put_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="MarquerConsommateur">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/MarquerConsommateur" message="tns:Iwcf_wsThemis_MarquerConsommateur_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/MarquerConsommateurResponse" message="tns:Iwcf_wsThemis_MarquerConsommateur_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadIdentityRIB">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadIdentityRIB" message="tns:Iwcf_wsThemis_LoadIdentityRIB_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/LoadIdentityRIBResponse" message="tns:Iwcf_wsThemis_LoadIdentityRIB_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddIdentityRIB">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/AddIdentityRIB" message="tns:Iwcf_wsThemis_AddIdentityRIB_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/AddIdentityRIBResponse" message="tns:Iwcf_wsThemis_AddIdentityRIB_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FlagAuto">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FlagAuto" message="tns:Iwcf_wsThemis_FlagAuto_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FlagAutoResponse" message="tns:Iwcf_wsThemis_FlagAuto_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FlagAutoAboFermeMemePlace">
      <wsdl:input wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FlagAutoAboFermeMemePlace" message="tns:Iwcf_wsThemis_FlagAutoAboFermeMemePlace_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Iwcf_wsThemis/FlagAutoAboFermeMemePlaceResponse" message="tns:Iwcf_wsThemis_FlagAutoAboFermeMemePlace_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_Iwcf_wsThemis" type="tns:Iwcf_wsThemis">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="FlagAutoAboMemePlace">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FlagAutoAboMemePlace" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FlagManuel_tempo">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FlagManuel_tempo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnFlagManuel_tempo">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UnFlagManuel_tempo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnFlagSeats">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UnFlagSeats" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnFlagSeatsManuel_tempo">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel_tempo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnFlagSeatsManuel">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UnFlagSeatsManuel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReFlagSeatsManuel_tempo">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/ReFlagSeatsManuel_tempo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadSeats">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadSeats" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadSeatsTexts">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadSeatsTexts" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadLieusOfSessions">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadLieusOfSessions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadInfoComp">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadInfoComp" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInfoCompIDList">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetInfoCompIDList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListInfoCompOnIdentite">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetListInfoCompOnIdentite" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCustomerAllPurchaseHistoryDetails">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetCustomerAllPurchaseHistoryDetails" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetProfilAcheteurPurchaseHistory">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetProfilAcheteurPurchaseHistory" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateCustomerProfile">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UpdateCustomerProfile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCustomerProfileOfId">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadCivilityNaming">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadCivilityNaming" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadFunctions">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadFunctions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateListInfoCompOnIdentite">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UpdateListInfoCompOnIdentite" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateInfoComp">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UpdateInfoComp" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCustomerProfileOfEmailOrIdentiteId">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetCustomerProfileOfEmailOrIdentiteId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateCustomerProfileAndReturnID">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/CreateCustomerProfileAndReturnID" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadDevise">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadDevise" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadEventsListWaitList">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsListWaitList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InsertWaitList">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/InsertWaitList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetWaitListOfIdentite">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetWaitListOfIdentite" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteIdentiteOfWaitList">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/DeleteIdentiteOfWaitList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadEventsListOfToday">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsListOfToday" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListCoupeFileCommandes">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetListCoupeFileCommandes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateDossierCommentaire">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UpdateDossierCommentaire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TAH_MakePDF_FromCmdCoupeFile">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmdCoupeFile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCurrentBasketAbonnement">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetCurrentBasketAbonnement" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCurrentCustomBasketAbonnement">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetCurrentCustomBasketAbonnement" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListPropertiesOfEvent">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetListPropertiesOfEvent" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadReservationList">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadReservationList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadLanguages">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadLanguages" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLangueIdOfLangCode">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetLangueIdOfLangCode" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="nextDelayRefresh">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/nextDelayRefresh" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadEvents">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadEvents" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadEventsInGp">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsInGp" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadEventsOfFiliere">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFiliere" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadDispoFuturesSessions">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadDispoFuturesSessions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdatePasswordIdentite">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UpdatePasswordIdentite" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadIdentite">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadIdentite" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckIdentite">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/CheckIdentite" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FillDroitsFacture">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FillDroitsFacture" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FillAcompte">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FillAcompte" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadCommandes">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadCommandes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FanCard_LoadIdentiteChild">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadIdentiteChild" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FanCard_AddRelation">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FanCard_AddRelation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FanCard_UpdateRelation">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FanCard_UpdateRelation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FanCard_LoadAllIdentiteCommandesFans">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FanCard_LoadAllIdentiteCommandesFans" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsGestionnaireReaboFans">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/IsGestionnaireReaboFans" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FanCard_DeleteRelation">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FanCard_DeleteRelation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDemandResetPasswordById">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetDemandResetPasswordById" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateUseDateDemandResetPassword">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UpdateUseDateDemandResetPassword" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddDemandResetPassword">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/AddDemandResetPassword" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteMyIdentite">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/DeleteMyIdentite" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getConsommateursForSeances">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/getConsommateursForSeances" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getConsommateurs">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/getConsommateurs" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddLinkConsumers">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/AddLinkConsumers" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnLinkConsumers">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/UnLinkConsumers" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ProfilAcheteurLogin">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/ProfilAcheteurLogin" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ProfilAcheteurLoginById">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/ProfilAcheteurLoginById" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadGrilleTarifs">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifs" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadGrilleTarifsPlacement">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsPlacement" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadAllInternetGrilleTarifs">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadAllInternetGrilleTarifs" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadAmounts">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadAmounts" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadGroupFormulas">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadGroupFormulas" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadFormulas">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadFormulas" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadEventsOfFormulas">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsOfFormulas" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadContraintesOfFormulas">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadContraintesOfFormulas" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadEventsHorsAbo">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadEventsHorsAbo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadGrilleTarifsHorsAbo">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsHorsAbo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadGrilleTarifsPlacesSupplementaires">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadGrilleTarifsPlacesSupplementaires" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLieuBySessionId">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetLieuBySessionId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadProductsOfFormulas">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadProductsOfFormulas" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadFraisProductsOfFormulas">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadFraisProductsOfFormulas" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListeReserveForFormula">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetListeReserveForFormula" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetReserveList">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetReserveList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTypePrisePlaceOfFormula">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/GetTypePrisePlaceOfFormula" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadFraisEnvoisReabo">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadFraisEnvoisReabo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadProducts">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadProducts" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadCommonsMO_ListFormulaTarifSeance_abo">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadCommonsMO_ListFormulaTarifSeance_abo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadMaquettesOfMoGp">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadMaquettesOfMoGp" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadQuestionnairesProductsOfFormulas">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadQuestionnairesProductsOfFormulas" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Basket_FillFromOpen">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/Basket_FillFromOpen" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BasketFillFromOpen">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/BasketFillFromOpen" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BasketTransformation">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/BasketTransformation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TAH_MakePDF_FromCmd">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/TAH_MakePDF_FromCmd" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EditerCommande">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/EditerCommande" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DepotVente_ispossible">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/DepotVente_ispossible" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DepotVente_GetMontantReprise">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/DepotVente_GetMontantReprise" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DepotVente_put">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/DepotVente_put" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MarquerConsommateur">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/MarquerConsommateur" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadIdentityRIB">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/LoadIdentityRIB" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddIdentityRIB">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/AddIdentityRIB" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FlagAuto">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FlagAuto" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FlagAutoAboFermeMemePlace">
      <soap:operation soapAction="http://tempuri.org/Iwcf_wsThemis/FlagAutoAboFermeMemePlace" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="wcf_wsThemis">
    <wsdl:port name="BasicHttpBinding_Iwcf_wsThemis" binding="tns:BasicHttpBinding_Iwcf_wsThemis">
      <soap:address location="http://localhost:55660/wcf-wsThemis.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>