﻿select pft.panier_formule_tarif_id, p.panier_id, p.identite_id, formule_id, type_tarif_id, formule_nom, type_tarif_nom,
	nb_place, panier_formule_tarif_entree_id, manif_id, entree_id, vts_id, rang, siege, seance_id, manif_nom, seance_description,categ_id, categ_nom, montant, frais, contrainte, type_envoi,
	maquette_id, type_envoi_id, type_envoi_id, section_id, etage_id, zone_id, gestion_place_id, date_prise_place, etage_nom, section_nom, zone_nom
 from panier p 
 inner join panier_formule_tarif  pft
 on pft.panier_id = p.panier_id
inner join panier_formule_tarif_entree pfte
on pft.panier_formule_tarif_id = pfte.panier_formule_tarif_id
where  p.panier_id =[PANIER_ID] AND p.identite_id=[IDENTITE_ID] 