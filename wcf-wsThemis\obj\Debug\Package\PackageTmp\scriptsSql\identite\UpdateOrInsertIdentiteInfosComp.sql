﻿DECLARE @ICEXIST[CNT] INT;

SELECT @ICEXIST[CNT]=count(*) 
FROM identite_infos_comp 
WHERE identite_id=[IDENTITE_ID] AND info_comp_id=[INFO_COMP_TO_ADD]


IF (@ICEXIST[CNT]=0)
	INSERT INTO identite_infos_comp (identite_id,info_comp_id,valeur1,valeur2,valeur3,valeur4,supprimer,datecreation,datemodification) 
	VALUES ([IDENTITE_ID], [INFO_COMP_TO_ADD],'','','','','N',getdate(),getdate());
    
ELSE
 UPDATE identite_infos_comp SET supprimer='N' , datemodification=getdate()
	WHERE identite_id=[IDENTITE_ID] AND info_comp_id=[INFO_COMP_TO_ADD]