﻿---------------- seances :
SELECT  distinct lieu_nom as placeName, lieu.lieu_id as placeId, manifestation_groupe.manif_groupe_nom as eventGroupName ,manifestation_groupe.manif_groupe_id as eventGroupId,
manifestation.manifestation_nom as eventName ,manifestation.manifestation_id as eventId, seance.seance_date_deb as sessionStartDate,seance.seance_id as sessionId
,'GP' = CASE WHEN gp.manif_id is NULL THEN 'NON' ELSE 'OUI' END,  
'manifsislock' = CASE 
				WHEN gpm.islock is NULL THEN 'false' 
				WHEN gpm.islock = 0 THEN 'false' 
				ELSE 'true' END, 
'seancesislock' = CASE 
				WHEN gps.islock is NULL THEN 'false' 
				WHEN gps.islock =0 THEN 'false' 
				ELSE 'true' END,
	c.categ_nom as categNom, c.categ_id as categId, c.pref_affichage as categPrefAffichage
FROM         manifestation 
INNER JOIN manifestation_groupe ON manifestation.manifestation_groupe_id = manifestation_groupe.manif_groupe_id 
INNER JOIN seance ON manifestation.manifestation_id = seance.manifestation_id
INNER JOIN lieu ON lieu.lieu_id = seance.lieu_id
INNER JOIN categorie c on c.lieu_id = lieu.lieu_id
LEFT OUTER JOIN GP_seance gps on  gps.seance_id = seance.seance_Id
LEFT OUTER JOIN GP_manifestation gpm on gpm.manifestation_id = manifestation.manifestation_id
LEFT OUTER JOIN gestion_place gp on gp.seance_id =seance.seance_id  and gp.formule_id is null and gp.categ_id is null and iscontrainteidentite=0
WHERE seance_date_deb > getdate() and seance_cloturer<>'O' and seance_masquer<>'O' and seance_verrouiller<>'O'  and OPTIONS <>'O'
and gp.dispo = 0
and manif_groupe_nom not like 'ANIM-GROUPE %'
--AND  seance.seance_id BETWEEN 1 and 20
ORDER BY GP desc,placeName,eventGroupName,eventName,sessionStartDate;