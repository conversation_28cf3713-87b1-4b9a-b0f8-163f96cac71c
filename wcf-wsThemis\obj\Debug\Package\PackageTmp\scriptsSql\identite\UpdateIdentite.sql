﻿declare @operateurOfThisIdentite int
set @operateurOfThisIdentite = (select operateurmodif_id from identite where identite_id =[IDENTITE_ID])


UPDATE identite SET identite_date_modification=getdate(), identite_nom='[NOM]', identite_complement='[IDENTITE_COMPLEMENT]',
identite_prenom='[PRENOM]', identite_date_naissance=CONVERT(DATETIME,'[DATEOFBIRTHDAY]',103), postal_rue1='[POSTAL_RUE1]', postal_rue2='[POSTAL_RUE2]',
postal_rue3='[POSTAL_RUE3]',postal_rue4='[POSTAL_RUE4]',postal_cp='[POSTAL_CP]', postal_ville='[POSTAL_VILLE]',postal_pays='[POSTAL_PAYS]',
postal_tel1='[POSTAL_TEL1]',postal_tel2='[POSTAL_TEL2]',postal_tel3='[POSTAL_TEL3]',postal_tel4='[POSTAL_TEL4]',postal_tel5='[POSTAL_TEL5]',
postal_tel6='[POSTAL_TEL6]',postal_tel7='[POSTAL_TEL7]',appellation_id=[APPELATION_ID], identite_password='[IDENTITE_PASSWORD]',
operateurmodif_id=  ISNULL((select preference_valeur from structure_prefs where preference_cle='WEB_OPERATOR_ID'), (select @operateurOfThisIdentite))
 WHERE identite_id=[IDENTITE_ID]


