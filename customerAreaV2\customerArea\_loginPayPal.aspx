﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="_loginPayPal.aspx.cs" Inherits="customerArea._loginPayPal" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">


    <script>
        // Fonction pour extraire les paramètres de l'URL
        function getUrlVars() {
            var vars = {};
            var parts = window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi, function(m,key,value) {
                vars[key] = value;
            });
            return vars;
        }

        if (getUrlVars()["code"] != undefined) {
            var codePP = getUrlVars()["code"];

            console.log('🧪 _loginPayPal.aspx - Code PayPal reçu:', codePP);

            // 🧪 TEST : Récupérer idstructure depuis sessionStorage
            var idstructure = sessionStorage.getItem('paypal_idstructure');
            console.log('🧪 _loginPayPal.aspx - idstructure récupéré:', idstructure);

            /* reload la page login en passant le code PayPal */
            var openerUrl = window.opener.location.href;

            // Vérifier si l'URL contient déjà idstructure
            if (openerUrl.indexOf('idstructure=') === -1 && idstructure) {
                // Ajouter idstructure si absent
                openerUrl += (openerUrl.indexOf('?') === -1 ? '?' : '&') + 'idstructure=' + idstructure;
            }

            // Ajouter le code PayPal
            openerUrl += '&codePP=' + codePP;

            console.log('🧪 _loginPayPal.aspx - Redirection vers:', openerUrl);

            window.opener.location.href = openerUrl;
            window.close();
        } else {
            console.error('❌ _loginPayPal.aspx - Aucun code PayPal reçu !');
            console.log('URL actuelle:', window.location.href);
        }
    </script>


</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ExtraScripts" runat="server">
</asp:Content>
