/**
 * Vérifie si une cellule est vraiment un en-tête et pas des données
 */
function isValidHeaderCell($cell, text) {
    // Ignorer les cellules vides
    if (!text || text.length === 0) {
        return false;
    }

    // RÈGLE PRINCIPALE : Accepter SEULEMENT les cellules avec les classes d'en-tête spécifiques
    var hasHeaderClass = $cell.hasClass('bgGrey') || $cell.hasClass('header') || $cell.hasClass('th');
    var isInThead = $cell.closest('thead').length > 0;

    // Si ce n'est pas dans une zone d'en-tête, ignorer
    if (!hasHeaderClass && !isInThead) {
        return false;
    }

    // Ignorer les nombres purs (comme les téléphones, montants, etc.)
    if (/^\d+$/.test(text) || /^\d+[\.,]\d+$/.test(text)) {
        return false;
    }

    // Ignorer les numéros de téléphone
    if (/^\+?\d{8,15}$/.test(text.replace(/[\s\-\(\)]/g, ''))) {
        return false;
    }

    // Ignorer les adresses (contiennent des numéros suivis de mots)
    if (/^\d+\s+[A-Z\s]+\d*$/i.test(text)) {
        return false;
    }

    // Ignorer les emails
    if (/@/.test(text)) {
        return false;
    }

    // Ignorer les codes postaux
    if (/^\d{5}$/.test(text)) {
        return false;
    }

    // Ignorer les valeurs qui ressemblent à des données plutôt qu'à des en-têtes
    if (text.length > 100) { // En-têtes peuvent être longs mais pas trop
        return false;
    }

    // Ignorer les valeurs qui sont clairement des données
    if (/^\d+_\d+_\d+$/.test(text)) { // Format comme "123_456_789"
        return false;
    }


    return true;
}

/**
 * Fonction de traduction des en-têtes de colonnes pour les tableaux générés dynamiquement
 * Traduit directement les clés SQL qui viennent de la requête
 */
function TranslateColumnHeaders(tableId) {
    if (typeof ReadXmlTranslate !== 'function') {
        return;
    }

    var table = $('#' + tableId);
    if (table.length === 0) {
        // Essayons de trouver tous les tableaux dans la modal
        $('#modalGeneric table').each(function(index) {
            // Table trouvée dans la modal
        });
        return;
    }

    // Traduit TOUS les en-têtes de colonnes directement par leur texte (clé SQL)
    // Cherche dans tous les éléments qui peuvent être des en-têtes
    var headerSelectors = [
        'thead td',
        'thead th',
        'tr:first-child td',
        'tr.entetecols td',
        'td.bgGrey'
    ];

    var totalTranslated = 0;
    var totalAttempted = 0;

    headerSelectors.forEach(function(selector) {
        var headers = table.find(selector);

        headers.each(function() {
            var $cell = $(this);
            var currentText = $cell.text().trim();
            var dataTrad = $cell.attr('data-trad');

            // Vérifier si c'est vraiment un en-tête et pas des données
            if (!isValidHeaderCell($cell, currentText)) {
                return; // Continue to next cell
            }

            totalAttempted++;

            // Utilise d'abord data-trad si disponible, sinon le texte
            var keyToTranslate = dataTrad || currentText;

            // Nettoie la clé (enlève les apostrophes si présentes)
            var cleanKey = keyToTranslate.replace(/^['"]|['"]$/g, '');

            if (cleanKey && cleanKey.length > 0) {
                var translatedText = ReadXmlTranslate(cleanKey);

                if (translatedText && translatedText !== cleanKey && translatedText !== currentText) {
                    $cell.text(translatedText);
                    totalTranslated++;
                }
            }
        });
    });
}

/**
 * Fonction principale à appeler après le chargement des données
 * Utilise le système de traduction XML existant
 */
function TranslateTableHeaders(tableId) {
    var maxRetries = 10;
    var retryCount = 0;

    function attemptTranslation() {
        // Vérifier que la fonction de traduction est disponible
        if (typeof ReadXmlTranslate !== 'function') {
            retryCount++;
            if (retryCount < maxRetries) {
                setTimeout(attemptTranslation, 200);
                return;
            } else {
                return;
            }
        }

        TranslateColumnHeaders(tableId);

        // Force une nouvelle traduction après LaunchTraduction()
        setTimeout(function() {
            TranslateColumnHeaders(tableId);
        }, 500);
    }

    attemptTranslation();
}

/**
 * Traduit tous les tableaux présents dans un conteneur
 */
function TranslateAllTablesInContainer(containerSelector) {
    $(containerSelector + ' table').each(function() {
        var tableId = $(this).attr('id');
        if (!tableId) {
            tableId = 'auto_table_' + Math.random().toString(36).substring(2, 11);
            $(this).attr('id', tableId);
        }
        TranslateTableHeaders(tableId);
    });
}

/**
 * Traduit les options d'un dropdown de sélection de colonnes
 */
function TranslateSelectOptions(selectId) {
    console.log('🔄 TranslateSelectOptions appelée pour:', selectId);

    if (typeof ReadXmlTranslate !== 'function') {
        console.log('❌ ReadXmlTranslate non disponible');
        return;
    }

    var $select = $('#' + selectId);
    if ($select.length === 0) {
        console.log('❌ Select non trouvé:', selectId);
        return;
    }

    console.log('✅ Select trouvé, nombre d\'options:', $select.find('option').length);

    var translatedCount = 0;
    $select.find('option').each(function() {
        var $option = $(this);
        var originalText = $option.text().trim();
        var dataTrad = $option.attr('data-trad');

        // Utilise d'abord data-trad si disponible, sinon le texte
        var keyToTranslate = dataTrad || originalText;

        if (keyToTranslate && keyToTranslate.length > 0) {
            var translatedText = ReadXmlTranslate(keyToTranslate);
            if (translatedText && translatedText !== keyToTranslate && translatedText !== originalText) {
                console.log('🔄 Traduction:', keyToTranslate, '→', translatedText);
                $option.text(translatedText);
                translatedCount++;
            }
        }
    });

    console.log('✅ Traductions appliquées:', translatedCount);
}
