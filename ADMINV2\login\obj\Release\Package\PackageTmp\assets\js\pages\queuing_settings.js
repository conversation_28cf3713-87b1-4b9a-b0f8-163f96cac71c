﻿var sAjaxSourceUrl = "queuingsettings.aspx/";

$(document).ready(function () {
    $('#Maintenance_FileName').on('change, input', function () {
        $('#maintenanceFileNameCompleteUrlLink').attr('href', $(this).attr('data-baseurl') + "" + $(this).val() + QueuingFileExtension)
        $('#maintenanceFileNameCompleteUrlText').html($(this).attr('data-baseurl') + "" + $(this).val() + QueuingFileExtension)
    })

    $('#Maintenance_FileName').trigger('input')



    $('#QueuingV1_FileNameIndiv').on('change, input', function () {
        $('#queuingV1FileNameIndivCompleteUrlLink').attr('href', $(this).attr('data-baseurl') + "" + $(this).val() + QueuingFileExtension)
        $('#queuingV1FileNameIndivCompleteUrlText').html($(this).attr('data-baseurl') + "" + $(this).val() + QueuingFileExtension)
    })

    $('#QueuingV1_FileNameIndiv').trigger('input')

    $('#QueuingV1_FileNameAbo').on('change, input', function () {
        $('#queuingV1FileNameAboCompleteUrlLink').attr('href', $(this).attr('data-baseurl') + "" + $(this).val() + QueuingFileExtension)
        $('#queuingV1FileNameAboCompleteUrlText').html($(this).attr('data-baseurl') + "" + $(this).val() + QueuingFileExtension)
    })

    $('#QueuingV1_FileNameAbo').trigger('input')


    $('#QueuingV2_UrlToGo').on('change, input', function () {
        $('#queuingV2UrlToGoLink').attr('href', $(this).val())
    })

    $('#QueuingV2_UrlToGo').trigger('input')


    //Url queuing generique
    $('#queuingV2_Url').on('change, input', function () {
        $('#queuingV2Url').attr('href', $(this).val())
    })

    $('#queuingV2_Url').trigger('input')

    //Url queuing indiv
    $('#queuingV2_UrlIndiv').on('change, input', function () {
        $('#queuingV2UrlIndiv').attr('href', $(this).val())
    })

    $('#queuingV2_UrlIndiv').trigger('input')

    //url queuing abo
    $('#queuingV2_UrlAbo').on('change, input', function () {
        $('#queuingV2UrlAbo').attr('href', $(this).val())
    })

    $('#queuingV2_UrlAbo').trigger('input')


    //reset url queuing generique
    $('#resetQueuingV2Url').on('click', function (e) {
        e.preventDefault();
        $('#queuingV2_Url').val(DefaultQueuingV2GotoUrlIndiv);
    })
    //reset url queuing indiv
    $('#resetQueuingV2UrlIndiv').on('click', function (e) {
        e.preventDefault();
        $('#queuingV2_UrlIndiv').val(DefaultQueuingV2GotoUrlIndiv);
    })
    //reset url queuing abo
    $('#resetQueuingV2UrlAbo').on('click', function (e) {
        e.preventDefault();
        $('#queuingV2_UrlAbo').val(DefaultQueuingV2GotoUrlIndiv);
    })


    $('#btnSaveQueuing').on('click', function () {
        saveQueuingClick()
    })

    // Nouveau bouton pour créer une file d'attente (même structure que btnSaveQueuing)
    $('#btnCreateQueue').on('click', function () {
        createQueueClick()
    })
    $("input[max]").on('change, input', function () {
        var max = parseInt($(this).attr('max'));
        var min = parseInt($(this).attr('min'));
        if ($(this).val() > max) {
            $(this).val(max);
        } else if ($(this).val() < min) {
            $(this).val(min);
        }
    });
    $('input[max]').trigger('input')


    waitForRadioAndAttachEvent();
});



function waitForRadioAndAttachEvent() {
    const radioQueuingV2 = document.querySelector('input[value="QueuingV2"]');
    const checkboxIsActive = document.getElementById('QueuingV2_IsActiveFileAttente');

    if (!radioQueuingV2._lcs_event_attached) {
        radioQueuingV2.addEventListener('lcs-statuschange', function () {
            if (radioQueuingV2.checked) {

                if (typeof lcs_on === 'function') {
                    lcs_on(checkboxIsActive);
                }
                checkboxIsActive.checked = true;
            } else {

                if (typeof lcs_off === 'function') {
                    lcs_off(checkboxIsActive);
                }
                checkboxIsActive.checked = false;
            }
        });

        radioQueuingV2._lcs_event_attached = true;
    }

    if (radioQueuingV2.checked && !checkboxIsActive.checked) {
        if (typeof lcs_on === 'function') {
            lcs_on(checkboxIsActive);
        }
        checkboxIsActive.checked = true;
    } else if (!radioQueuingV2.checked && checkboxIsActive.checked) {
        if (typeof lcs_off === 'function') {
            lcs_off(checkboxIsActive);
        }
        checkboxIsActive.checked = false;
    }

}

//sauvegarde de la queuing
function saveQueuingClick() {
    var isError = false
    var QueuingJsonNew = JSON.parse(JSON.stringify(QueuingJson));
    $.each(QueuingJsonNew, function (ijson, kjson) {
        kjson.IsActive = false;
    })
    $.each($('#queuingForm input'), function (i, k) {
        if ($(k).attr('name') != 'radio-queuing_active') {
            var kname = $(k).attr('name').split('_')
            var parent = kname[0]
            var key = kname[1]
        }

        switch ($(k).attr('type')) {
            case 'number':
                if ($(k).val() != '') {
                    QueuingJsonNew[parent][key] = parseInt($(k).val())
                } else {
                    isError = true
                }
                break;

            case 'text':
                if ($(k).val() != '') {
                    QueuingJsonNew[parent][key] = $(k).val()
                } else {
                    isError = true
                }
                break;

            case 'radio':
                if ($(k).attr('name') == 'radio-queuing_active' && $(k).is(':checked')) {
                    QueuingJsonNew[$(k).val()].IsActive = true
                }
                break;

            case 'checkbox':
                QueuingJsonNew[parent][key] = $(k).is(':checked')
                break;
        }
    });

    $.each($('#queuingForm select'), function (i, k) {
        var kname = $(k).attr('name').split('_')
        var parent = kname[0]
        var key = kname[1]

        if ($(k).val() != '') {
            QueuingJsonNew[parent][key] = $(k).val()
        } else {
            isError = true
        }

    });
    
    //console.log(QueuingJsonNew)
    if (isError) {
        ShowError("error", ReadXmlTranslate('msg_error_empty_fields'), "alert alert-danger alert-dismissable alert-fixed");
    } else {
        SaveQueuingAjax(QueuingJsonNew)
    }
}

function SaveQueuingAjax(QueuingJsonNew) {
    var sData = JSON.stringify({
        _structureId: StructureId,
        _langCode: LangCode,
        _queuing: QueuingJsonNew
    });
    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'SaveQueuing',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (response) {
            console.log(response.d);
            ShowError("error", ReadXmlTranslate('msg_queuing_update_success'), "alert alert-success alert-dismissable alert-fixed");
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });
}

//création de file d'attente (copie exacte de saveQueuingClick mais appelle CreateQueueTables)
function createQueueClick() {
    var isError = false
    var QueuingJsonNew = JSON.parse(JSON.stringify(QueuingJson));
    $.each(QueuingJsonNew, function (ijson, kjson) {
        kjson.IsActive = false;
    })

    $.each($('input[name="radio-queuing_active"]:checked'), function (i, k) {
        QueuingJsonNew[$(k).val()].IsActive = true;
    })

    $.each($('#queuingForm input'), function (i, k) {
        if ($(k).attr('name') != 'radio-queuing_active') {
            var kname = $(k).attr('name').split('_')
            var parent = kname[0]
            var key = kname[1]

            if ($(k).attr('type') == 'checkbox') {
                QueuingJsonNew[parent][key] = $(k).is(':checked')
            } else {
                if ($(k).val() != '') {
                    QueuingJsonNew[parent][key] = $(k).val()
                } else {
                    isError = true
                }
            }
        }
    });

    $.each($('#queuingForm select'), function (i, k) {
        var kname = $(k).attr('name').split('_')
        var parent = kname[0]
        var key = kname[1]

        if ($(k).val() != '') {
            QueuingJsonNew[parent][key] = $(k).val()
        } else {
            isError = true
        }

    });

    //console.log(QueuingJsonNew)
    if (isError) {
        ShowError("error", ReadXmlTranslate('msg_error_empty_fields'), "alert alert-danger alert-dismissable alert-fixed");
    } else {
        CreateQueueTablesAjax(QueuingJsonNew)
    }
}

function CreateQueueTablesAjax(QueuingJsonNew) {
    var sData = JSON.stringify({
        _structureId: StructureId,
        _langCode: LangCode,
        _queuing: QueuingJsonNew
    });
    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'CreateQueueTables',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (response) {
            console.log(response.d);

            // Vérifier si le message contient "succès" pour déterminer si c'est un succès ou une erreur
            var message = response.d;
            var isSuccess = message.toLowerCase().includes("succès") || message.toLowerCase().includes("créées");

            if (isSuccess) {
                // Message de succès (vert)
                ShowError("error", message, "alert alert-success alert-dismissable alert-fixed");
            } else {
                // Message d'erreur ou d'avertissement (rouge/orange)
                ShowError("error", message, "alert alert-warning alert-dismissable alert-fixed");
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });
}