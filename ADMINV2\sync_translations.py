#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour synchroniser les fichiers de traduction
Basé sur le fichier français (translate.fr.xml) comme référence
"""

import xml.etree.ElementTree as ET
import os
from collections import OrderedDict

def parse_xml_file(file_path):
    """Parse un fichier XML et retourne un dictionnaire des traductions"""
    if not os.path.exists(file_path):
        print(f"Fichier non trouvé: {file_path}")
        return {}
    
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        translations = OrderedDict()
        
        # Parcourir tous les éléments avec attribut 'trad'
        for elem in root.iter():
            if elem.get('trad'):
                key = elem.get('trad')
                text = elem.text if elem.text else ""
                translations[key] = {
                    'text': text,
                    'tag': elem.tag,
                    'parent_path': get_parent_path(elem)
                }
        
        return translations
    except Exception as e:
        print(f"Erreur lors du parsing de {file_path}: {e}")
        return {}

def get_parent_path(elem):
    """Obtient le chemin parent d'un élément"""
    path = []
    parent = elem.getparent()
    while parent is not None:
        if parent.tag != 'Root':
            path.insert(0, parent.tag)
        parent = parent.getparent()
    return '/'.join(path)

def create_translation_mapping():
    """Crée un mapping des traductions français -> allemand/anglais"""
    
    # Traductions de base français -> allemand
    fr_to_de = {
        # Traductions générales
        'Aide': 'Hilfe',
        'Changer de langue': 'Sprache wechseln',
        'se déconnecter': 'abmelden',
        'Exporter en PDF': 'Als PDF exportieren',
        'Exporter sous Excel': 'Als Excel exportieren',
        'Imprimer': 'Drucken',
        'Détail': 'Detail',
        'Détail complet': 'Vollständige Details',
        'Montant 1': 'Betrag 1',
        'Montant': 'Betrag',
        'Date': 'Datum',
        'Nom': 'Name',
        'Prénom': 'Vorname',
        'Email': 'E-Mail',
        'Téléphone': 'Telefon',
        'Adresse': 'Adresse',
        'Ville': 'Stadt',
        'Code postal': 'Postleitzahl',
        'Pays': 'Land',
        'Valider': 'Bestätigen',
        'Annuler': 'Abbrechen',
        'Modifier': 'Bearbeiten',
        'Ajouter': 'Hinzufügen',
        'Supprimer': 'Löschen',
        'Enregistrer': 'Speichern',
        'Rechercher': 'Suchen',
        'Nouveau': 'Neu',
        'Éditer': 'Bearbeiten',
        'Copier': 'Kopieren',
        'Oui': 'Ja',
        'Non': 'Nein',
        'Erreur': 'Fehler',
        'Succès': 'Erfolg',
        'Attention': 'Achtung',
        'Information': 'Information',
        'Confirmation': 'Bestätigung',
        'Chargement': 'Laden',
        'Veuillez patienter': 'Bitte warten',
        'Aucun': 'Keine',
        'Tous': 'Alle',
        'Sélectionner': 'Auswählen',
        'Choisir': 'Wählen',
        'Options': 'Optionen',
        'Paramètres': 'Einstellungen',
        'Configuration': 'Konfiguration',
        'Administration': 'Verwaltung',
        'Utilisateur': 'Benutzer',
        'Mot de passe': 'Passwort',
        'Connexion': 'Anmeldung',
        'Déconnexion': 'Abmeldung',
        'Session': 'Sitzung',
        'Profil': 'Profil',
        'Droits': 'Rechte',
        'Permissions': 'Berechtigungen',
        'Groupe': 'Gruppe',
        'Structure': 'Struktur',
        'Manifestation': 'Veranstaltung',
        'Séance': 'Vorstellung',
        'Tarif': 'Tarif',
        'Catégorie': 'Kategorie',
        'Réserve': 'Reserve',
        'Place': 'Platz',
        'Billet': 'Ticket',
        'Commande': 'Bestellung',
        'Panier': 'Warenkorb',
        'Paiement': 'Zahlung',
        'Facture': 'Rechnung',
        'Client': 'Kunde',
        'Produit': 'Produkt',
        'Stock': 'Lager',
        'Quantité': 'Menge',
        'Prix': 'Preis',
        'Total': 'Gesamt',
        'Sous-total': 'Zwischensumme',
        'TVA': 'MwSt',
        'Remise': 'Rabatt',
        'Promotion': 'Aktion',
        'Offre': 'Angebot',
        'Formule': 'Formel',
        'Abonnement': 'Abonnement',
        'Statistiques': 'Statistiken',
        'Rapport': 'Bericht',
        'Export': 'Export',
        'Import': 'Import',
        'Fichier': 'Datei',
        'Dossier': 'Ordner',
        'Image': 'Bild',
        'Logo': 'Logo',
        'Maquette': 'Vorlage',
        'Template': 'Vorlage',
        'CSS': 'CSS',
        'HTML': 'HTML',
        'JavaScript': 'JavaScript',
        'Base de données': 'Datenbank',
        'Serveur': 'Server',
        'Connexion': 'Verbindung',
        'Test': 'Test',
        'Production': 'Produktion',
        'Développement': 'Entwicklung',
        'Version': 'Version',
        'Mise à jour': 'Aktualisierung',
        'Installation': 'Installation',
        'Configuration': 'Konfiguration',
        'Sauvegarde': 'Sicherung',
        'Restauration': 'Wiederherstellung',
        'Maintenance': 'Wartung',
        'Support': 'Support',
        'Documentation': 'Dokumentation',
        'Manuel': 'Handbuch',
        'Guide': 'Leitfaden',
        'Tutoriel': 'Tutorial',
        'Formation': 'Schulung',
        'Assistance': 'Hilfe',
        'Contact': 'Kontakt',
        'À propos': 'Über uns',
        'Mentions légales': 'Rechtliche Hinweise',
        'Conditions': 'Bedingungen',
        'Politique': 'Richtlinie',
        'Confidentialité': 'Datenschutz',
        'Cookies': 'Cookies',
        'Langue': 'Sprache',
        'Français': 'Französisch',
        'Allemand': 'Deutsch',
        'Anglais': 'Englisch',
        'Traduction': 'Übersetzung'
    }
    
    # Traductions de base français -> anglais
    fr_to_en = {
        # Traductions générales
        'Aide': 'Help',
        'Changer de langue': 'Change language',
        'se déconnecter': 'logout',
        'Exporter en PDF': 'Export to PDF',
        'Exporter sous Excel': 'Export to Excel',
        'Imprimer': 'Print',
        'Détail': 'Detail',
        'Détail complet': 'Full Detail',
        'Montant 1': 'Amount 1',
        'Montant': 'Amount',
        'Date': 'Date',
        'Nom': 'Name',
        'Prénom': 'First name',
        'Email': 'Email',
        'Téléphone': 'Phone',
        'Adresse': 'Address',
        'Ville': 'City',
        'Code postal': 'Postal code',
        'Pays': 'Country',
        'Valider': 'Validate',
        'Annuler': 'Cancel',
        'Modifier': 'Edit',
        'Ajouter': 'Add',
        'Supprimer': 'Delete',
        'Enregistrer': 'Save',
        'Rechercher': 'Search',
        'Nouveau': 'New',
        'Éditer': 'Edit',
        'Copier': 'Copy',
        'Oui': 'Yes',
        'Non': 'No',
        'Erreur': 'Error',
        'Succès': 'Success',
        'Attention': 'Warning',
        'Information': 'Information',
        'Confirmation': 'Confirmation',
        'Chargement': 'Loading',
        'Veuillez patienter': 'Please wait',
        'Aucun': 'None',
        'Tous': 'All',
        'Sélectionner': 'Select',
        'Choisir': 'Choose',
        'Options': 'Options',
        'Paramètres': 'Settings',
        'Configuration': 'Configuration',
        'Administration': 'Administration',
        'Utilisateur': 'User',
        'Mot de passe': 'Password',
        'Connexion': 'Login',
        'Déconnexion': 'Logout',
        'Session': 'Session',
        'Profil': 'Profile',
        'Droits': 'Rights',
        'Permissions': 'Permissions',
        'Groupe': 'Group',
        'Structure': 'Structure',
        'Manifestation': 'Event',
        'Séance': 'Session',
        'Tarif': 'Rate',
        'Catégorie': 'Category',
        'Réserve': 'Reserve',
        'Place': 'Seat',
        'Billet': 'Ticket',
        'Commande': 'Order',
        'Panier': 'Cart',
        'Paiement': 'Payment',
        'Facture': 'Invoice',
        'Client': 'Customer',
        'Produit': 'Product',
        'Stock': 'Stock',
        'Quantité': 'Quantity',
        'Prix': 'Price',
        'Total': 'Total',
        'Sous-total': 'Subtotal',
        'TVA': 'VAT',
        'Remise': 'Discount',
        'Promotion': 'Promotion',
        'Offre': 'Offer',
        'Formule': 'Formula',
        'Abonnement': 'Subscription',
        'Statistiques': 'Statistics',
        'Rapport': 'Report',
        'Export': 'Export',
        'Import': 'Import',
        'Fichier': 'File',
        'Dossier': 'Folder',
        'Image': 'Image',
        'Logo': 'Logo',
        'Maquette': 'Template',
        'Template': 'Template',
        'CSS': 'CSS',
        'HTML': 'HTML',
        'JavaScript': 'JavaScript',
        'Base de données': 'Database',
        'Serveur': 'Server',
        'Connexion': 'Connection',
        'Test': 'Test',
        'Production': 'Production',
        'Développement': 'Development',
        'Version': 'Version',
        'Mise à jour': 'Update',
        'Installation': 'Installation',
        'Configuration': 'Configuration',
        'Sauvegarde': 'Backup',
        'Restauration': 'Restore',
        'Maintenance': 'Maintenance',
        'Support': 'Support',
        'Documentation': 'Documentation',
        'Manuel': 'Manual',
        'Guide': 'Guide',
        'Tutoriel': 'Tutorial',
        'Formation': 'Training',
        'Assistance': 'Help',
        'Contact': 'Contact',
        'À propos': 'About',
        'Mentions légales': 'Legal notices',
        'Conditions': 'Terms',
        'Politique': 'Policy',
        'Confidentialité': 'Privacy',
        'Cookies': 'Cookies',
        'Langue': 'Language',
        'Français': 'French',
        'Allemand': 'German',
        'Anglais': 'English',
        'Traduction': 'Translation'
    }
    
    return fr_to_de, fr_to_en

def translate_text(french_text, fr_to_target):
    """Traduit un texte français vers la langue cible"""
    if not french_text:
        return french_text
    
    # Recherche exacte d'abord
    if french_text in fr_to_target:
        return fr_to_target[french_text]
    
    # Recherche partielle pour les textes plus complexes
    for fr_key, target_value in fr_to_target.items():
        if fr_key.lower() in french_text.lower():
            return french_text.replace(fr_key, target_value)
    
    # Si aucune traduction trouvée, retourner le texte original avec un marqueur
    return f"{french_text} (TODO: translate)"

def main():
    # Chemins des fichiers
    fr_file = "login/pages/Resources/translate.fr.xml"
    de_file = "login/pages/Resources/translate.de.xml"
    en_file = "login/pages/Resources/translate.en.xml"
    
    # Vérifier que le fichier français existe
    if not os.path.exists(fr_file):
        print(f"Fichier français non trouvé: {fr_file}")
        return
    
    # Parser le fichier français (référence)
    print("Parsing du fichier français...")
    fr_translations = parse_xml_file(fr_file)
    print(f"Trouvé {len(fr_translations)} traductions en français")
    
    # Parser les fichiers existants
    print("Parsing des fichiers existants...")
    de_translations = parse_xml_file(de_file) if os.path.exists(de_file) else {}
    en_translations = parse_xml_file(en_file) if os.path.exists(en_file) else {}
    
    print(f"Trouvé {len(de_translations)} traductions en allemand")
    print(f"Trouvé {len(en_translations)} traductions en anglais")
    
    # Obtenir les mappings de traduction
    fr_to_de, fr_to_en = create_translation_mapping()
    
    # Identifier les clés manquantes
    missing_de = set(fr_translations.keys()) - set(de_translations.keys())
    missing_en = set(fr_translations.keys()) - set(en_translations.keys())
    
    print(f"\nClés manquantes en allemand: {len(missing_de)}")
    print(f"Clés manquantes en anglais: {len(missing_en)}")
    
    # Afficher quelques exemples de clés manquantes
    if missing_de:
        print(f"Exemples DE manquants: {list(missing_de)[:10]}")
    if missing_en:
        print(f"Exemples EN manquants: {list(missing_en)[:10]}")
    
    # Créer les traductions manquantes
    print("\nCréation des traductions manquantes...")
    
    # Pour l'allemand
    for key in missing_de:
        fr_text = fr_translations[key]['text']
        de_text = translate_text(fr_text, fr_to_de)
        de_translations[key] = {
            'text': de_text,
            'tag': fr_translations[key]['tag'],
            'parent_path': fr_translations[key]['parent_path']
        }
    
    # Pour l'anglais
    for key in missing_en:
        fr_text = fr_translations[key]['text']
        en_text = translate_text(fr_text, fr_to_en)
        en_translations[key] = {
            'text': en_text,
            'tag': fr_translations[key]['tag'],
            'parent_path': fr_translations[key]['parent_path']
        }
    
    print(f"Traductions allemandes totales: {len(de_translations)}")
    print(f"Traductions anglaises totales: {len(en_translations)}")
    
    # Sauvegarder les résultats
    print("\nSauvegarde des fichiers synchronisés...")
    save_translations_to_file(de_translations, "translate_de_synchronized.xml", "de")
    save_translations_to_file(en_translations, "translate_en_synchronized.xml", "en")
    
    print("Synchronisation terminée!")
    print(f"Fichiers créés:")
    print(f"- translate_de_synchronized.xml ({len(de_translations)} traductions)")
    print(f"- translate_en_synchronized.xml ({len(en_translations)} traductions)")

def save_translations_to_file(translations, filename, lang_code):
    """Sauvegarde les traductions dans un fichier XML"""
    
    # Créer la structure XML
    root = ET.Element("Root")
    generals = ET.SubElement(root, "Generals")
    
    # Grouper par parent_path
    grouped = {}
    for key, data in translations.items():
        parent_path = data['parent_path']
        if parent_path not in grouped:
            grouped[parent_path] = []
        grouped[parent_path].append((key, data))
    
    # Ajouter les éléments généraux d'abord
    if 'Generals' in grouped:
        for key, data in grouped['Generals']:
            elem = ET.SubElement(generals, data['tag'])
            elem.set('trad', key)
            elem.text = data['text']
    
    # Ajouter les pages
    pages_added = set()
    for parent_path, items in grouped.items():
        if parent_path != 'Generals' and parent_path.startswith('Page'):
            page_name = parent_path.split('/')[-1] if '/' in parent_path else parent_path
            if page_name not in pages_added:
                page_elem = ET.SubElement(root, "Page")
                if page_name != 'Page':
                    page_elem.set('name', page_name.replace('Page_', ''))
                pages_added.add(page_name)
                
                for key, data in items:
                    elem = ET.SubElement(page_elem, data['tag'])
                    elem.set('trad', key)
                    elem.text = data['text']
    
    # Formater et sauvegarder
    xml_str = ET.tostring(root, encoding='unicode')
    
    # Ajouter l'en-tête XML
    xml_content = '<?xml version="1.0" encoding="utf-8"?>\n' + xml_str
    
    # Formater le XML (basique)
    xml_content = xml_content.replace('><', '>\n<')
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(xml_content)

if __name__ == "__main__":
    main()
