﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

using RodWebShop.App_Code;
using System.Globalization;

using App_Code;
using customerArea.App_Code;
using utilitaires2010;
using customerArea.classes;
using utilitaires2010.sql.sqlserver;
using ws_DTO;
using ws_bll.WT;
using System.Linq;
using System.Collections.Generic;

namespace customerArea.App_Code
{

//    alter FUNCTION [dbo].[sv98_encrypt] 
//( 
//      @toencode     as nvarchar(max)
//) 
//returns varchar(max)
//as
//begin
//declare @varxml xml

//--'i' + CONVERT(varchar,identite_id) + '|224k3y193t1t3'
//select @varxml =
//(select CAST(hashbytes('sha2_256', 'i' + @toencode + '|224k3y193t1t3') as varbinary(max)) FOR XML PATH(''))

//return @varxml.value('/','varchar(max)')
//end

//go
//--select identite_id,postal_tel6, dbo.sv98_encrypt( 'i' + CONVERT(varchar,identite_id) + '|224k3y193t1t3'), identite_password from identite order by identite_id desc
//select identite_id,postal_tel6, dbo.sv98_encrypt( CONVERT(varchar,identite_id) ), identite_password from identite order by identite_id desc



    public  class LoginDarmstadtSoftwAG : LoginDefault
    {

        public override string GetMyTypeLogin()
        {
            return "SofwareAG";
        }

        /// <summary>
        /// encrypte SHA1 normal
        /// </summary>
        /// <param name="_newpass"></param>
        /// <returns></returns>
        public override string EncryptePassW(int idStructure, string _newpass)
        {

            MyDictionary mySSC = new MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(idStructure);
            //string _newpasscrypte = MyConfigurationManager.CalculateHashedPassword(_newpass);

            string mSalt = "notSet";
            //string mSalt = "$2a$10$rBV2JDeWW3.vKyeQcM8fFO";

            if (mySSC.Contains("CRMEXTERNESALT"))
            {
                mSalt = mySSC["CRMEXTERNESALT"];
            }
            else
            {
                GestionTraceManager.WriteLogError(idStructure, "EncryptePassW: SALT not found!");
                Exception e = new Exception("LoginDarmstadtSoftwAG: SALT not found");
                throw e;
            }

            string passBCrypte = BCrypt.HashPassword(_newpass, mSalt);


            return passBCrypte;
        }


        /// <summary>
        /// connection de l'internaute
        /// </summary>
        /// <param name="IdStructureString"></param>
        /// <param name="Email"></param>
        /// <param name="IdIdentity"></param>
        /// <param name="PassWord"></param>
        /// <returns>identite_id si connection ok, 0 si connection echouée</returns>
        public override int GetLogin(string IdStructureString, string Email, string IdIdentity, string passwordInForm, bool isAutoLogin)
        {
            MyDictionary mySSC = new MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(int.Parse(IdStructureString));

            GestionTraceManager.WriteLog(int.Parse(IdStructureString), "GetLogin LoginDarmadtSoftAG...");

            //customerArea.WsThemis.WSThemisAbo WsThemis = new customerArea.WsThemis.WSThemisAbo();
            customerArea.wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new customerArea.wcf_WsThemis.Iwcf_wsThemisClient();
            IdentiteEntity user = new IdentiteEntity();
            try
            {
                var logsLevel = Initialisations.GetKeyAppSettings("logsLevel");

                if (logsLevel != LogLevel.NORMAL.ToString())
                {
                    GestionTraceManager.WriteLog(int.Parse(IdStructureString), "GetCustomerProfileOfEmailOrLogin " + IdStructureString + " " + Email + " " + IdIdentity, "LOGGENERIQUE");
                }

                //  user = wcfThemis.GetCustomerProfileOfEmailOrIdentiteId(IdStructureString, Email, MyConfigurationManager.CalculateHashedPassword(passwordInForm), IdIdentity, GetEmailPhoneNumber(int.Parse(IdStructureString)), "N", false, true);

                string passwordHash = EncryptePassW(int.Parse(IdStructureString), passwordInForm);


                user = wcfThemis.GetCustomerProfileOfEmailOrIdentiteId(IdStructureString, Email, passwordHash, IdIdentity, GetEmailPhoneNumber(int.Parse(IdStructureString)), "N");

                //dsUser = WsThemis.GetCustomerProfileOfEmailOrLogin(IdStructureString, Email, IdIdentity);
                if (passwordInForm.Length < 30)
                {
                    if (user.PasswordCrypt  != passwordHash)
                    {
                        Exception e = new Exception("dans ws.GetCustomerProfileOfEmailOrIdentiteId LoginDarmstadtSoftwAG:password is false");
                        throw e;
                    }
                }

            }
            catch (Exception e1)
            {
                Exception e = new Exception("dans ws.GetCustomerProfileOfEmailOrIdentiteId LoginDarmstadtSoftwAG:" + e1.Message);
                throw e;
            }


            //if (dsUser != null && dsUser.Tables.Count == 1 && dsUser.Tables[0].Rows.Count == 1)
            if (user != null)
            {
                var logsLevel = Initialisations.GetKeyAppSettings("logsLevel");

                if (logsLevel != LogLevel.NORMAL.ToString())
                {
                    GestionTraceManager.WriteLog(int.Parse(IdStructureString), user.Identite_id.ToString());
                }


                string passCrypte = "";
                if (!isAutoLogin)
                {
                    passCrypte = MyConfigurationManager.CalculateHashedPassword(passwordInForm);
                }
                string mSalt = "notSet";
                //string mSalt = "$2a$10$rBV2JDeWW3.vKyeQcM8fFO";

                if (mySSC.Contains("CRMEXTERNESALT"))
                {
                    mSalt = mySSC["CRMEXTERNESALT"];
                }
                else
                {
                    GestionTraceManager.WriteLogError(int.Parse(IdStructureString), "LoginDarmstadtSoftwAG: SALT not found!");
                    Exception e = new Exception("LoginDarmstadtSoftwAG: SALT not found");
                    throw e;
                }


                //crypt le mot de passe entrer dans le formulaire
                string passBCrypte = BCrypt.HashPassword(passwordInForm, mSalt);
                                    
                passCrypte = user.Password;

                if (logsLevel != LogLevel.NORMAL.ToString())
                {
                    GestionTraceManager.WriteLog(int.Parse(IdStructureString), "passCrypte SAG " + passCrypte);
                }


                //if (dsUser != null && dsUser.Tables.Count == 1 && dsUser.Tables[0].Rows.Count == 1)
                if ((user.Password == passwordInForm || user.Password == passCrypte) || user.Password == passBCrypte ||  isAutoLogin)
                {
                    bool wanted = false;
                    // TODO, verifier l'info comp WANTED 

                    if (!wanted)
                    {

                        //string passCrypte = MyConfigurationManager.CalculateHashedPassword(PassWord);
                        //si le mot de passe n'est pas crypté on le crypt
                        if ((passCrypte != user.Password && user.Password != passBCrypte) && !isAutoLogin)
                        {

                            //customerArea.wcf_WebOpen.Iwcf_wsOpenClient wsoc = new customerArea.wcf_WebOpen.Iwcf_wsOpenClient();
                            customerArea.wcf_WsThemis.Iwcf_wsThemisClient wsThemis = new customerArea.wcf_WsThemis.Iwcf_wsThemisClient();

                            // met le mdp passBCrypt
                            bool isPswdCrypte = wsThemis.UpdatePasswordIdentite(IdStructureString, user.Identite_id, passBCrypte);


                            if (logsLevel != LogLevel.NORMAL.ToString())
                            {
                                if (isPswdCrypte)
                                    GestionTraceManager.WriteLog(int.Parse(IdStructureString), "dans GetLogin : mot de passe crypte ", "LOGGENERIQUE");
                                else
                                    GestionTraceManager.WriteLog(int.Parse(IdStructureString), "dans GetLogin : erreur update mot de passe crypte ", "LOGGENERIQUE");

                            }


                        }

                        //Intialize Session variables
                        System.Web.HttpContext.Current.Session["SVarMyLogin"] = "true";
                        System.Web.HttpContext.Current.Session["SVarUserIdentity"] = "true";
                        System.Web.HttpContext.Current.Session["SVarMyResponse"] = user;
                        System.Web.HttpContext.Current.Session["SVarUserIdentityNom"] = user.SurName;
                        System.Web.HttpContext.Current.Session["SVarUserIdentityPrenom"] = user.FirstName;
                        System.Web.HttpContext.Current.Session["SVarUserIdentityID"] = user.Identite_id.ToString();
                        System.Web.HttpContext.Current.Session["SVarUserEmail"] = user.Email;
                        //recup WebUser..
                        string idIdentiteRod = user.Identite_id.ToString();
                        string typeRun = MyConfigurationManager.AppSettings("TypeRun");
                        if (idIdentiteRod != "")
                        {
                            // creer le web user dans log
                            if (System.Web.HttpContext.Current.Session["currIdUser"] == null)
                            {   // pas encore de WebUserID -> le creer dans la table Users, 
                                try
                                {
                                    SqlServerConnexion sqlConn;
                                    sqlConn = DBFunctions.ConnectWebTracing(typeRun);

                                    if (sqlConn == null)
                                    {
                                        GestionTraceManager.WriteLogError(int.Parse(IdStructureString), "Error !! ne peut se connecter au webtracing");
                                        throw new Exception("Error !! ne peut se connecter au webtracing");
                                    }


                                    GestionTraceManager.WriteWebUserIfNotExist(sqlConn, int.Parse(idIdentiteRod));
                                }
                                catch (Exception ex)
                                {
                                    throw new Exception(ex.Message + " " + ex.StackTrace);
                                }

                            }
                            else
                            {   // WebUser existe déjà, maj son identite_id, 
                                SqlServerConnexion sqlConn;
                                sqlConn = DBFunctions.ConnectWebTracing(typeRun);

                                GestionTraceManager.WriteIdentiteId(sqlConn, int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString()), int.Parse(idIdentiteRod));
                            }
                            // relier le panier (éventuel) à l'id identité
                            int webUserId = int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString());


                            //BasketEntity pan = new BasketEntity();
                            //WebTracing2010.BasketsManager.getBaskets()
                            //if (pan.BasketId != 0)
                            //{
                            //    pan.Identite_id = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                            //}
                        }
                        else
                        {
                            Exception e = new Exception("dans GetLogin:identite='' ?!?");
                            throw e;
                        }
                        return int.Parse(idIdentiteRod);
                    }
                    else
                        throw new exceptionWanted();
                }
                else
                    throw new exceptionIncorrectPassword();
            }
            else
                throw new exceptionIncorrectEmailOrId();
        }
    
    }
}