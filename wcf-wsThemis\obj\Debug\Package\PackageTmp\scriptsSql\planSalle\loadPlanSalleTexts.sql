﻿	 declare @typePlan int
	 select @typePlan = lieu_physique_type_plan from seance s
	 inner join lieu_configuration lc on lc.lieu_config_id = s.lieu_config_id
	 inner join lieu_physique lp on lp.lieu_physique_id = lc.lieu_physique_id
	 where seance_id = [sessionID]



SELECT type_siege, 
/* plan type 2 : on recalcule x y des traits comme si on etait toujours en grille = 19px (et non 18) */
case when @typePlan=2 and type_siege='L£' then convert(int, pos_x * 19.0/18.0) else pos_x end as pos_x,
case when @typePlan=2 and type_siege='L£' then convert(int,pos_y * 19.0/18.0) else pos_y end as pos_y,
case when @typePlan=2 and type_siege='L£' then convert(int,decal_x * 19.0/18.0) else decal_x end as decal_x,
case when @typePlan=2 and type_siege='L£' then convert(int,decal_y * 19.0/18.0) else decal_y end as decal_y,
texte,
		case when len(type_siege)=2 then
			case when type_siege = 'L¤' 
				then 'isPoteau' 
				else
					case when type_siege='L£' then 'isTrait' + convert(varchar(1), @typePlan) else 'isTexte'
				end
			end 
		else
			case when SUBSTRING(type_siege,1,1)='£' then 'TexteLong' else '??' end
		end	
		as typeTEXTE,
		case when len(type_siege)=2 then
			case when type_siege = 'L¤' 
			then '°°' 
			when type_siege='L£' then
				texte
			else
				SUBSTRING(type_siege,2,1)
			end 
		else
			case when SUBSTRING(type_siege,1,1)='£' then 
				texte + '|' + type_siege else '' end 
		end		
		as valTEXTE


	 FROM reference_lieu_physique rlp, seance s
	 WHERE s.lieu_config_id=rlp.lieu_physique_id
	 AND seance_id= [sessionID]
	 AND type_siege<>'F' AND type_siege NOT LIKE 'S%' 
	 --and type_siege<>'L£'


	 ORDER BY iindex