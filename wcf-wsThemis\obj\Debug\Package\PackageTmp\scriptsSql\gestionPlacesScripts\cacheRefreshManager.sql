﻿
/* defini le temps restant avant le prochain refresh (programmé dans bornesUpdateCacheWeb ou par [defaultDelay] recu en parametre */
select top 1 
case 
	when date_borne<dateadd(SECOND, [defaultDelay], getdate()) 
	then datediff(second, getdate(), date_borne)
	else datediff(second, getdate(), dateadd(SECOND, [defaultDelay], getdate())) 
end as nextRefreshWillAppendInSeconds,
date_borne, dateadd(SECOND, [defaultDelay], getdate()) as defaultBorne from bornesUpdateCacheWeb
where date_borne >getdate() order by date_borne