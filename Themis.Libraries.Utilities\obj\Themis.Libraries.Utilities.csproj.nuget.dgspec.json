{"format": 1, "restore": {"D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj": {}}, "projects": {"D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj", "projectName": "Themis.Libraries.Utilities", "projectPath": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\WORK\\VIEUXPROJETS\\Themis.Libraries.Utilities\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"BCrypt.Net": {"target": "Package", "version": "[0.1.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}, "log4net": {"target": "Package", "version": "[3.1.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}