﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.6.33723.286
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "customerArea", "customerArea\customerArea.csproj", "{313F0399-321F-4631-8956-6F9217FC9919}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebTracing2010", "..\WebTracing\WebTracing2010.csproj", "{A2FACD66-71D3-489A-9BA0-BEB74AC55956}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "utilitaires2010", "..\utilitaires\utilitaires2010.csproj", "{43EB2F59-7B43-4D1E-A62C-16C06E39E756}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ws_BLL", "..\ws_BLL\ws_BLL.csproj", "{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Themis.Libraries.Utilities", "..\Themis.Libraries.Utilities\Themis.Libraries.Utilities.csproj", "{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Themis.Libraries.DTO", "..\Themis.Libraries.DTO\Themis.Libraries.DTO.csproj", "{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OpenEntity2010", "..\OpenEntity2010\OpenEntity2010.csproj", "{8F11805B-FC68-4EBC-BB35-D458FF8B9312}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ws_DTO", "..\ws_DTO\ws_DTO.csproj", "{817A794C-460D-4585-83C3-2C0B5041A8A9}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{313F0399-321F-4631-8956-6F9217FC9919}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{313F0399-321F-4631-8956-6F9217FC9919}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{313F0399-321F-4631-8956-6F9217FC9919}.Debug|x64.ActiveCfg = Debug|x64
		{313F0399-321F-4631-8956-6F9217FC9919}.Debug|x64.Build.0 = Debug|x64
		{313F0399-321F-4631-8956-6F9217FC9919}.Debug|x86.ActiveCfg = Debug|Any CPU
		{313F0399-321F-4631-8956-6F9217FC9919}.Debug|x86.Build.0 = Debug|Any CPU
		{313F0399-321F-4631-8956-6F9217FC9919}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{313F0399-321F-4631-8956-6F9217FC9919}.Release|Any CPU.Build.0 = Release|Any CPU
		{313F0399-321F-4631-8956-6F9217FC9919}.Release|x64.ActiveCfg = Release|x64
		{313F0399-321F-4631-8956-6F9217FC9919}.Release|x64.Build.0 = Release|x64
		{313F0399-321F-4631-8956-6F9217FC9919}.Release|x86.ActiveCfg = Release|Any CPU
		{313F0399-321F-4631-8956-6F9217FC9919}.Release|x86.Build.0 = Release|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Debug|x64.Build.0 = Debug|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Debug|x86.Build.0 = Debug|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Release|x64.ActiveCfg = Release|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Release|x64.Build.0 = Release|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Release|x86.ActiveCfg = Release|Any CPU
		{A2FACD66-71D3-489A-9BA0-BEB74AC55956}.Release|x86.Build.0 = Release|Any CPU
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Debug|x64.ActiveCfg = Debug|x64
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Debug|x64.Build.0 = Debug|x64
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Debug|x86.ActiveCfg = Debug|x86
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Debug|x86.Build.0 = Debug|x86
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Release|Any CPU.Build.0 = Release|Any CPU
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Release|x64.ActiveCfg = Release|x64
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Release|x64.Build.0 = Release|x64
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Release|x86.ActiveCfg = Release|x86
		{43EB2F59-7B43-4D1E-A62C-16C06E39E756}.Release|x86.Build.0 = Release|x86
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Debug|x64.ActiveCfg = Debug|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Debug|x64.Build.0 = Debug|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Debug|x86.ActiveCfg = Debug|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Debug|x86.Build.0 = Debug|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Release|Any CPU.Build.0 = Release|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Release|x64.ActiveCfg = Release|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Release|x64.Build.0 = Release|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Release|x86.ActiveCfg = Release|Any CPU
		{AC2C39AD-A2D3-4E6B-9234-C30C13199E80}.Release|x86.Build.0 = Release|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Debug|x64.Build.0 = Debug|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Debug|x86.Build.0 = Debug|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Release|x64.ActiveCfg = Release|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Release|x64.Build.0 = Release|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Release|x86.ActiveCfg = Release|Any CPU
		{1CB681B9-E27F-44EB-ACD2-CD56177F1FDD}.Release|x86.Build.0 = Release|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Debug|x64.Build.0 = Debug|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Debug|x86.Build.0 = Debug|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Release|Any CPU.Build.0 = Release|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Release|x64.ActiveCfg = Release|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Release|x64.Build.0 = Release|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Release|x86.ActiveCfg = Release|Any CPU
		{0F8FA9CE-1125-4096-90CB-50FF021A7CAD}.Release|x86.Build.0 = Release|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Debug|x64.Build.0 = Debug|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Debug|x86.Build.0 = Debug|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Release|Any CPU.Build.0 = Release|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Release|x64.ActiveCfg = Release|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Release|x64.Build.0 = Release|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Release|x86.ActiveCfg = Release|Any CPU
		{8F11805B-FC68-4EBC-BB35-D458FF8B9312}.Release|x86.Build.0 = Release|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Debug|x64.Build.0 = Debug|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Debug|x86.Build.0 = Debug|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Release|x64.ActiveCfg = Release|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Release|x64.Build.0 = Release|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Release|x86.ActiveCfg = Release|Any CPU
		{817A794C-460D-4585-83C3-2C0B5041A8A9}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0C9D6EAA-A9F3-49EC-90ED-5F06F60F7898}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 9
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-tfs:8080/tfs/appscollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = ..\\WebTracing\\WebTracing2010.csproj
		SccProjectName1 = ../../TmpLib/WebTracing2010
		SccLocalPath1 = ..\\WebTracing
		SccProjectUniqueName2 = customerArea\\customerArea.csproj
		SccProjectName2 = customerArea
		SccLocalPath2 = customerArea
		SccProjectUniqueName3 = ..\\utilitaires\\utilitaires2010.csproj
		SccProjectName3 = ../../TmpLib/utilitaires2010
		SccLocalPath3 = ..\\utilitaires
		SccProjectUniqueName4 = ..\\ws_BLL\\ws_BLL.csproj
		SccProjectName4 = ../../TmpLib/ws_BLL
		SccLocalPath4 = ..\\ws_BLL
		SccProjectUniqueName5 = ..\\Themis.Libraries.Utilities\\Themis.Libraries.Utilities.csproj
		SccProjectName5 = ../../Themis.Libraries.Utilities/Themis.Libraries.Utilities/Themis.Libraries.Utilities
		SccLocalPath5 = ..\\Themis.Libraries.Utilities
		SccProjectUniqueName6 = ..\\Themis.Libraries.DTO\\Themis.Libraries.DTO.csproj
		SccProjectName6 = ../../Themis.Libraires.DTO/Themis.Libraries.DTO/Themis.Libraries.DTO
		SccLocalPath6 = ..\\Themis.Libraries.DTO
		SccProjectUniqueName7 = ..\\OpenEntity2010\\OpenEntity2010.csproj
		SccProjectName7 = ../../RobWebNet/RodWebSites/WebApplicationGroupe/WebApplicationGroupe/OpenEntity
		SccLocalPath7 = ..\\OpenEntity2010
		SccProjectUniqueName8 = ..\\ws_DTO\\ws_DTO.csproj
		SccProjectName8 = ../../TmpLib/ws_DTO
		SccLocalPath8 = ..\\ws_DTO
	EndGlobalSection
EndGlobal
