﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pagemaster.Master" AutoEventWireup="true" CodeBehind="ResaPay.aspx.cs" Inherits="customerArea.ResaPay" debug="True" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">
<!-- commentaire haut -->
    <div id="commentsPageHaut">
        <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
    </div>
	<!--
	<pre><%= BasketDump %></pre>
-->
    <h2><span data-trad="title_pay_reservation">Payer ma réservation N°</span> <%= Basket.Commande_id.ToString("# ##0") %></h2>
    <%
    var ProductsSubTotalTTC = 0;
    var FeesSubTotalTTC = 0;
    var FeesDeliverySubTotalTTC = 0;
    var lstTauxTva = new List<dynamic>();
    var AmontOrderPaid = 0;
    %>
    <div id="orderDetailsListWrapper">
    <!-- recap content -->
    <div class="boxedBlock nopadding" >
    	<!-- //////////// EVENTS INDIV //////////// -->
    	<% foreach(var oneEvent in Basket.ListEventsUnitSales){ %>
        <!-- one event -->
        <div class="oneSummaryLine oneEvent" data-eventid="<%= oneEvent.EventId %>">
            <span class="eventName"><%= oneEvent.EventName %></span>
            <div class="sessionsWrapper">
            	<% foreach(var oneSession in oneEvent.ListSessions){ 
            		 var oneSummaryLineTotalTTC = 0;
            	%>
	                <!-- session start -->
	                <div class="oneSession" data-sessionid="<%= oneSession.SessionId %>" data-eventtitle="<%= oneEvent.EventName %>" data-eventstart="<%= oneSession.SessionStartDate %>" data-eventend="<%= oneSession.SessionStartDate %>" data-eventlieu="<%= oneSession.lieuName %>">
	                    <div class="row">
	                        <div class="col">
	                            <div class="collapsed" data-toggle="collapse" data-target="#collapseIndiv<%= oneEvent.EventId %>" aria-expanded="false" aria-controls="collapseIndiv<%= oneEvent.EventId %>">
	                                <% if(oneSession.IsShowSessionDate == true) { %>
                                    <div class="productSessionWrapper">
                                        <% if(oneSession.IsShowSessionHour == true){ %>
                                            <span class="productIcon"><i class="far fa-calendar-alt" aria-hidden="true"></i></span> <span class="productSession" data-datetoformat="<%= oneSession.SessionStartDate %>" data-datelocale="year,monthlong,day,weekday,hour,minutes"><%= oneSession.SessionStartDate %></span>
                                        <% } else {%>
                                            <span class="productIcon"><i class="far fa-calendar-alt" aria-hidden="true"></i></span> <span class="productSession" data-datetoformat="<%= oneSession.SessionStartDate %>" data-datelocale="year,monthlong,day,weekday"><%= oneSession.SessionStartDate %></span>
                                        <% } %>
                                    </div>
                                    <% } %>
	                                

                                    <div class="productLieuWrapper"><span class="productIcon"><i class="fas fa-map-marked-alt" aria-hidden="true"></i></span> <span class="productLieu"><%= oneSession.lieuName %></span></div>
	                            </div>
	                        </div>
	                        <%
	                        	var countSeat = 0;
                                foreach (var oneCateg in oneSession.ListCategories ) {
                                    foreach (var onePrice in oneCateg.ListPrices) {
                                    	countSeat += onePrice.ListSeats.Count;
                                        foreach (var oneSeat in onePrice.ListSeats) {
                                            oneSummaryLineTotalTTC+=oneSeat.MontantTTCinCent;
                                            ProductsSubTotalTTC+=oneSeat.MontantTTCinCent;
                                            AmontOrderPaid+=onePrice.AmountPaid;
                                        }
                                     }
                                }
                                var oneSummaryLineTotalHT = (int) (oneSummaryLineTotalTTC/(1+(oneSession.tva_taux/100)));
                                lstTauxTva.Add(new { taux = oneSession.tva_taux, montant = (oneSummaryLineTotalTTC-oneSummaryLineTotalHT) });
                            %>
	                        <div class="col-auto text-right">
	                            <div class="priceTotalWrapper">
	                                <span class="priceTotalHT"><span data-priceformat="<%= oneSummaryLineTotalHT %>"><%= oneSummaryLineTotalHT %></span> <span data-trad="lbl_without_taxes">HT</span></span>
	                                <span class="priceTotal" data-pricetocalcul="add" data-priceformat="<%= oneSummaryLineTotalTTC %>"><%= oneSummaryLineTotalTTC %></span>
	                            </div>
	                        </div>
	                        <div class="col-12">
	                            <div class="collapsed" data-toggle="collapse" data-target="#collapseIndiv<%= oneEvent.EventId %>" aria-expanded="false" aria-controls="collapseIndiv<%= oneEvent.EventId %>">
	                                <div class="collapeIconWrapper">
	                                    <span class="productDetailsCollapseBtn btn btn-outline-secondary"><span class="productIcon"><i class="fas fa-ticket-alt" aria-hidden="true"></i></span> <span data-trad="btn_my_tickets_details">Détails des tickets</span> <span class="productCount">(<%= countSeat %>)</span></span>
	                                </div>
	                            </div>
	                        </div>
	                    </div>
	                    <!-- details start -->
	                    <div class="productSeatsWrapper collapse" id="collapseIndiv<%= oneEvent.EventId %>">
	                        <div class="row">
	                            <div class="col">
	                            	<%
	                            	foreach (var oneCateg in oneSession.ListCategories ) {
                                        foreach (var onePrice in oneCateg.ListPrices) {
                                        	foreach (var oneSeat in onePrice.ListSeats) {
                                    %>
	                                <!--one seat start-->
	                                <div class="productDetailsWrapper">
	                                    <div class="productDetails row align-items-center">
	                                        <div class="col-12 col-lg text-center text-lg-left">
	                                            <span class="productSeatDetail">
	                                            	<%
	                                            	if(oneSeat.isPlacementLibre == true) {
	                                            	%>
	                                            		<span data-trad="lbl_free_placement">Placement libre</span>
                                                    <% }else { %>
                                                    	<span><span data-trad="lbl_rank">Rang</span> <%= oneSeat.Rank %>, <span data-trad="lbl_seat">Siège</span> <%=oneSeat.Seat %></span>
                                                    <%
                                                	}
                                                    %>
	                                            </span>
	                                            <span class="productSeatDetailZESC">
	                                            	<span><%=oneSeat.Zone_name%></span><span><%=oneSeat.Floor_name%></span>
                                                    	<span><%=oneSeat.Section_name%></span>
	                                            </span>
	                                        </div>
	                                        <div class="col-12 col-lg text-center text-lg-left">
	                                            <span class="productSeatDetailCoupleTarifCateg">
	                                                <span><%= onePrice.Price_name %></span><span><%=oneCateg.Category_name%></span>
	                                            </span>
	                                        </div>
	                                        <div class="col-12 col-lg-1 text-center text-lg-right">
	                                            <div class="priceUnitWrapper"><span class="priceUnit" data-priceformat="<%= oneSeat.MontantTTCinCent %>"><%= oneSeat.MontantTTCinCent %></span></div>
	                                        </div>
	                                        
	                                    </div>
	                                </div>
	                                <!-- one seat end -->
	                                <%
	                                		}
	                                	}
	                                }
	                                %>
	                            </div>
	                        </div>
	                    </div>
	                    <!-- details end -->
	                </div>
	                <!-- session end -->
                <% } %>
            </div>
            <!-- session wrapper end -->
        </div>
        <!-- one event end -->
        <% } %>

     
		
		
		   <!-- //////////// EVENTS REABO //////////// -->
    	<% foreach(var oneEvent in Basket.ListEventsAbo){ %>
        <!-- one event -->
        <div class="oneSummaryLine oneEvent" data-eventid="<%= oneEvent.EventId %>">
            <span class="eventName"><%= oneEvent.EventName %></span>
            <div class="sessionsWrapper">
            	<% foreach(var oneSession in oneEvent.ListSessions){ 
            		 var oneSummaryLineTotalTTC = 0;
            	%>
	                <!-- session start -->
	                <div class="oneSession" data-sessionid="<%= oneSession.SessionId %>" data-eventtitle="<%= oneEvent.EventName %>" data-eventstart="<%= oneSession.SessionStartDate %>" data-eventend="<%= oneSession.SessionStartDate %>" data-eventlieu="<%= oneSession.lieuName %>">
	                    <div class="row">
	                        <div class="col">
	                            <div class="collapsed" data-toggle="collapse" data-target="#collapseReabo<%= oneEvent.EventId %>" aria-expanded="false" aria-controls="collapseReabo<%= oneEvent.EventId %>">
	                                <% if(oneSession.IsShowSessionDate == true) { %>
                                    <div class="productSessionWrapper">
                                        <% if(oneSession.IsShowSessionHour == true){ %>
                                            <span class="productIcon"><i class="far fa-calendar-alt" aria-hidden="true"></i></span> <span class="productSession" data-datetoformat="<%= oneSession.SessionStartDate %>" data-datelocale="year,monthlong,day,weekday,hour,minutes"><%= oneSession.SessionStartDate %></span>
                                        <% } else {%>
                                            <span class="productIcon"><i class="far fa-calendar-alt" aria-hidden="true"></i></span> <span class="productSession" data-datetoformat="<%= oneSession.SessionStartDate %>" data-datelocale="year,monthlong,day,weekday"><%= oneSession.SessionStartDate %></span>
                                        <% } %>
                                    </div>
                                    <% } %>
	                                

                                    <div class="productLieuWrapper"><span class="productIcon"><i class="fas fa-map-marked-alt" aria-hidden="true"></i></span> <span class="productLieu"><%= oneSession.lieuName %></span></div>
	                            </div>
	                        </div>
	                        <%
	                        	var countSeat = 0;
                                foreach (var oneCateg in oneSession.ListCategories ) {
                                    foreach (var onePrice in oneCateg.ListPrices) {
                                    	countSeat += onePrice.ListAboSeats.Count;
                                        foreach (var oneSeat in onePrice.ListAboSeats) {
                                            oneSummaryLineTotalTTC+=oneSeat.MontantTTCinCent;
                                            ProductsSubTotalTTC+=oneSeat.MontantTTCinCent;
                                            AmontOrderPaid+=onePrice.AmountPaid;
                                        }
                                     }
                                }
                                var oneSummaryLineTotalHT = (int) (oneSummaryLineTotalTTC/(1+(oneSession.tva_taux/100)));
                                lstTauxTva.Add(new { taux = oneSession.tva_taux, montant = (oneSummaryLineTotalTTC-oneSummaryLineTotalHT) });
                            %>
	                        <div class="col-auto text-right">
	                            <div class="priceTotalWrapper">
	                                <span class="priceTotalHT"><span data-priceformat="<%= oneSummaryLineTotalHT %>"><%= oneSummaryLineTotalHT %></span> <span data-trad="lbl_without_taxes">HT</span></span>
	                                <span class="priceTotal" data-pricetocalcul="add" data-priceformat="<%= oneSummaryLineTotalTTC %>"><%= oneSummaryLineTotalTTC %></span>
	                            </div>
	                        </div>
	                        <div class="col-12">
	                            <div class="collapsed" data-toggle="collapse" data-target="#collapseReabo<%= oneEvent.EventId %>" aria-expanded="false" aria-controls="collapseReabo<%= oneEvent.EventId %>">
	                                <div class="collapeIconWrapper">
	                                    <span class="productDetailsCollapseBtn btn btn-outline-secondary"><span class="productIcon"><i class="fas fa-ticket-alt" aria-hidden="true"></i></span> <span data-trad="btn_my_tickets_details">Détails des tickets</span> <span class="productCount">(<%= countSeat %>)</span></span>
	                                </div>
	                            </div>
	                        </div>
	                    </div>
	                    <!-- details start -->
	                    <div class="productSeatsWrapper collapse" id="collapseReabo<%= oneEvent.EventId %>">
	                        <div class="row">
	                            <div class="col">
	                            	<%
	                            	foreach (var oneCateg in oneSession.ListCategories ) {
                                        foreach (var onePrice in oneCateg.ListPrices) {
                                        	foreach (var oneSeat in onePrice.ListAboSeats) {
                                    %>
	                                <!--one seat start-->
	                                <div class="productDetailsWrapper">
	                                    <div class="productDetails row align-items-center">
	                                        <div class="col-12 col-lg text-center text-lg-left">
	                                            <span class="productSeatDetail">
	                                            	<%
	                                            	if(oneSeat.isPlacementLibre == true) {
	                                            	%>
	                                            		<span data-trad="lbl_free_placement">Placement libre</span>
                                                    <% }else { %>
                                                    	<span><span data-trad="lbl_rank">Rang</span> <%= oneSeat.Rank %>, <span data-trad="lbl_seat">Siège</span> <%=oneSeat.Seat %></span>
                                                    <%
                                                	}
                                                    %>
	                                            </span>
	                                            <span class="productSeatDetailZESC">
	                                            	<span><%=oneSeat.Zone_name%></span><span><%=oneSeat.Floor_name%></span>
                                                    	<span><%=oneSeat.Section_name%></span>
	                                            </span>
	                                        </div>
	                                        <div class="col-12 col-lg text-center text-lg-left">
	                                            <span class="productSeatDetailCoupleTarifCateg">
	                                                <span><%= onePrice.Price_name %></span><span><%=oneCateg.Category_name%></span>
	                                            </span>
	                                        </div>
	                                        <div class="col-12 col-lg-1 text-center text-lg-right">
	                                            <div class="priceUnitWrapper"><span class="priceUnit" data-priceformat="<%= oneSeat.MontantTTCinCent %>"><%= oneSeat.MontantTTCinCent %></span></div>
	                                        </div>
	                                        
	                                    </div>
	                                </div>
	                                <!-- one seat end -->
	                                <%
	                                		}
	                                	}
	                                }
	                                %>
	                            </div>
	                        </div>
	                    </div>
	                    <!-- details end -->
	                </div>
	                <!-- session end -->
                <% } %>
            </div>
            <!-- session wrapper end -->
        </div>
        <!-- one event end -->
        <% } %>
		
		
		
	   <!-- //////////// EVENTS REABO //////////// -->
    	<% foreach(var oneEvent in Basket.ListEventsAboMulti){ %>
        <!-- one event -->
        <div class="oneSummaryLine oneEvent" data-eventid="<%= oneEvent.EventId %>">
            <span class="eventName"><%= oneEvent.EventName %></span>
            <div class="sessionsWrapper">
            	<% foreach(var oneSession in oneEvent.ListSessions){ 
            		 var oneSummaryLineTotalTTC = 0;
            	%>
	                <!-- session start -->
	                <div class="oneSession" data-sessionid="<%= oneSession.SessionId %>" data-eventtitle="<%= oneEvent.EventName %>" data-eventstart="<%= oneSession.SessionStartDate %>" data-eventend="<%= oneSession.SessionStartDate %>" data-eventlieu="<%= oneSession.lieuName %>">
	                    <div class="row">
	                        <div class="col">
	                            <div class="collapsed" data-toggle="collapse" data-target="#collapseReabo<%= oneEvent.EventId %>" aria-expanded="false" aria-controls="collapseReabo<%= oneEvent.EventId %>">
	                                <% if(oneSession.IsShowSessionDate == true) { %>
                                    <div class="productSessionWrapper">
                                        <% if(oneSession.IsShowSessionHour == true){ %>
                                            <span class="productIcon"><i class="far fa-calendar-alt" aria-hidden="true"></i></span> <span class="productSession" data-datetoformat="<%= oneSession.SessionStartDate %>" data-datelocale="year,monthlong,day,weekday,hour,minutes"><%= oneSession.SessionStartDate %></span>
                                        <% } else {%>
                                            <span class="productIcon"><i class="far fa-calendar-alt" aria-hidden="true"></i></span> <span class="productSession" data-datetoformat="<%= oneSession.SessionStartDate %>" data-datelocale="year,monthlong,day,weekday"><%= oneSession.SessionStartDate %></span>
                                        <% } %>
                                    </div>
                                    <% } %>
	                                

                                    <div class="productLieuWrapper"><span class="productIcon"><i class="fas fa-map-marked-alt" aria-hidden="true"></i></span> <span class="productLieu"><%= oneSession.lieuName %></span></div>
	                            </div>
	                        </div>
	                        <%
	                        	var countSeat = 0;
                                foreach (var oneCateg in oneSession.ListCategories ) {
                                    foreach (var onePrice in oneCateg.ListPrices) {
                                    	countSeat += onePrice.ListAboMultiSeats.Count;
                                        foreach (var oneSeat in onePrice.ListAboMultiSeats) {
                                            oneSummaryLineTotalTTC+=oneSeat.MontantTTCinCent;
                                            ProductsSubTotalTTC+=oneSeat.MontantTTCinCent;
                                            AmontOrderPaid+=onePrice.AmountPaid;
                                        }
                                     }
                                }
                                var oneSummaryLineTotalHT = (int) (oneSummaryLineTotalTTC/(1+(oneSession.tva_taux/100)));
                                lstTauxTva.Add(new { taux = oneSession.tva_taux, montant = (oneSummaryLineTotalTTC-oneSummaryLineTotalHT) });
                            %>
	                        <div class="col-auto text-right">
	                            <div class="priceTotalWrapper">
	                                <span class="priceTotalHT"><span data-priceformat="<%= oneSummaryLineTotalHT %>"><%= oneSummaryLineTotalHT %></span> <span data-trad="lbl_without_taxes">HT</span></span>
	                                <span class="priceTotal" data-pricetocalcul="add" data-priceformat="<%= oneSummaryLineTotalTTC %>"><%= oneSummaryLineTotalTTC %></span>
	                            </div>
	                        </div>
	                        <div class="col-12">
	                            <div class="collapsed" data-toggle="collapse" data-target="#collapseReabo<%= oneEvent.EventId %>" aria-expanded="false" aria-controls="collapseReabo<%= oneEvent.EventId %>">
	                                <div class="collapeIconWrapper">
	                                    <span class="productDetailsCollapseBtn btn btn-outline-secondary"><span class="productIcon"><i class="fas fa-ticket-alt" aria-hidden="true"></i></span> <span data-trad="btn_my_tickets_details">Détails des tickets</span> <span class="productCount">(<%= countSeat %>)</span></span>
	                                </div>
	                            </div>
	                        </div>
	                    </div>
	                    <!-- details start -->
	                    <div class="productSeatsWrapper collapse" id="collapseReabo<%= oneEvent.EventId %>">
	                        <div class="row">
	                            <div class="col">
	                            	<%
	                            	foreach (var oneCateg in oneSession.ListCategories ) {
                                        foreach (var onePrice in oneCateg.ListPrices) {
                                        	foreach (var oneSeat in onePrice.ListAboMultiSeats) {
                                    %>
	                                <!--one seat start-->
	                                <div class="productDetailsWrapper">
	                                    <div class="productDetails row align-items-center">
	                                        <div class="col-12 col-lg text-center text-lg-left">
	                                            <span class="productSeatDetail">
	                                            	<%
	                                            	if(oneSeat.isPlacementLibre == true) {
	                                            	%>
	                                            		<span data-trad="lbl_free_placement">Placement libre</span>
                                                    <% }else { %>
                                                    	<span><span data-trad="lbl_rank">Rang</span> <%= oneSeat.Rank %>, <span data-trad="lbl_seat">Siège</span> <%=oneSeat.Seat %></span>
                                                    <%
                                                	}
                                                    %>
	                                            </span>
	                                            <span class="productSeatDetailZESC">
	                                            	<span><%=oneSeat.Zone_name%></span><span><%=oneSeat.Floor_name%></span>
                                                    	<span><%=oneSeat.Section_name%></span>
	                                            </span>
	                                        </div>
	                                        <div class="col-12 col-lg text-center text-lg-left">
	                                            <span class="productSeatDetailCoupleTarifCateg">
	                                                <span><%= onePrice.Price_name %></span><span><%=oneCateg.Category_name%></span>
	                                            </span>
	                                        </div>
	                                        <div class="col-12 col-lg-1 text-center text-lg-right">
	                                            <div class="priceUnitWrapper"><span class="priceUnit" data-priceformat="<%= oneSeat.MontantTTCinCent %>"><%= oneSeat.MontantTTCinCent %></span></div>
	                                        </div>
	                                        
	                                    </div>
	                                </div>
	                                <!-- one seat end -->
	                                <%
	                                		}
	                                	}
	                                }
	                                %>
	                            </div>
	                        </div>
	                    </div>
	                    <!-- details end -->
	                </div>
	                <!-- session end -->
                <% } %>
            </div>
            <!-- session wrapper end -->
        </div>
        <!-- one event end -->
        <% } %>		
		
		
		
		    <!-- one dossier produit -->
        <% foreach (var oneDProduct in Basket.ListProduitsWT)
            {

               // var oneSession = oneDProduct.Product.Product_name;

                 ProductsSubTotalTTC+=oneDProduct.TotalAmountTTCinCent;


        %>
                 <div class="oneSummaryLine oneProduct" data-eventid="<%= oneDProduct.ProduitId %>">
                    <span class="productName"><%= oneDProduct.Product_name %></span>
                    <div class="sessionsWrapper">

                        <div class="row">
	                        <div class="col">
                                </div>
                        </div>

                        <div class="col-auto text-right">
	                            <div class="priceTotalWrapper"><% =oneDProduct.Nombre %> *
	                               <span class="priceTotal" data-pricetocalcul="add" data-priceformat="<%= oneDProduct.MontantTTCinCent %>"><%= oneDProduct.MontantTTCinCent %></span>
	                            </div>
	                        </div>
	                        <div class="col-12">
	                            
	                        </div>


                    </div>
                </div>

        <%
            }
        %>
		
		
        <!-- total cmd start -->
        
        <div class="oneSummaryLine  backgroundWrapper bg-primary-12">
            <div class="row align-items-center">
                <div class="col">
                    <div data-trad="lbl_total_order">Total de la commande TTC</div>
                </div>
                <div class="col-4 col-md-3 text-right">
                    <div class="priceTotal" data-priceformat="<%=(ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC)%>"><%=(ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC)%></div>
                </div>
            </div>
        </div>
    </div>
    <div class="boxedBlock nopadding" >
    	<!-- MO start -->
        <div class="oneSummaryLine">
        	<div class="ddlOneBlockObtention">
				<label for="ddlObtentionAll"><span data-trad="lbl_mode_obtention_all">Choisissez un mode d'obtention</span> <span class="text-danger">*</span></label>
				<select id="ddlObtentionAll" class="form-control" data-firstoption="lbl_mode_obtention_all">
					<option idmo="0" data-trad="lbl_mode_obtention_all">Choisissez un mode d'obtention</option>
					<% foreach(var oneMo in LstObtainingMode){ %>
						<option idmo="<%= oneMo.produit_id %>" amnt="<%= oneMo.totalAmount %>" data-tvaname="<%= oneMo.tva_name%>" data-tvataux="<%= oneMo.tva_taux%>"><%= oneMo.productName%></option>
					<% } %>
				</select>
			</div>
        </div>
        <div id="summaryLineMO" class="oneSummaryLine summaryLineMO backgroundWrapper bg-primary-20" style="display:none;">
            <div class="row align-items-center">
                <div class="col">
                    <div id="MoLabel"></div> 
                </div>
                <div class="col-4 col-md-3 text-right">
                    <div id="MoAmount" class="priceTotal" data-priceformat=""></div>
                </div>
            </div>
        </div>
        <!-- MO end -->

        <% if(AmontOrderPaid > 0) { %>
        <div class="oneSummaryLine  backgroundWrapper bg-primary-20">
            <div class="row align-items-center">
                <div class="col">
                    <div data-trad="lbl_already_paid">Déjà payé</div>
                </div>
                <div class="col-4 col-md-3 text-right">
                    <div class="priceTotal">-<span data-priceformat="<%=AmontOrderPaid%>"><%=AmontOrderPaid %></span></div>
                </div>
            </div>
        </div>
        <% } %>
        
         <div class="oneSummaryLine summaryLineTotalOrder backgroundBigWrapper bg-primary-80">
            <div class="row align-items-center">
                <div class="col">
                    <div data-trad="lbl_total_to_pay">Total à payer</div>
                </div>
                <div class="col-4 col-md-3 text-right">
                    <div class="priceTotal" data-priceformat="<%=(ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC - AmontOrderPaid)%>"><%=(ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC - AmontOrderPaid)%></div>
                </div>
            </div>
        </div>
    <!-- total cmd end-->
    <div class="bg-primary-5" id="totalTVABigWrapper">
            <div class="row collapse" id="collapseDetailsTVA">
                <div class="col" id="detailsTVAWrapper">
                    <%
                    var TauxTvaSubTotalTTC = 0;
                    var lstTauxTvaGrouped = (from oneTaux in lstTauxTva group oneTaux by oneTaux.taux into tauxGroup select new { taux = Convert.ToDecimal(string.Format("{0:0.#####}", tauxGroup.Key)), montant = tauxGroup.Sum(x => x.montant)}).ToList();
                    
                    foreach (var oneTaux in lstTauxTvaGrouped ) {
	                    if(oneTaux.taux > 0) {
	                    %>
	                    <div class="oneSummaryLine" data-tvataux="<%= oneTaux.taux %>" >
	                        <div class="row">
	                            <div class="col">
	                                <div><%= oneTaux.taux %>%</div>
	                            </div>
	                            <div class="col-4 col-md-3 text-right">
	                                <div class="priceTotal" data-priceformat="<%= oneTaux.montant %>"><%= oneTaux.montant %></div>
	                            </div>
	                        </div>
	                    </div>
	                    <% 
	                    TauxTvaSubTotalTTC += @oneTaux.montant;
	                    }
                    }
                    %>
                </div>
            </div>
            <% if (TauxTvaSubTotalTTC > 0) { %>
            <div class="oneSummaryLine totalTVAWrapper">
                <div class="row">
                    <div class="col">
                        <div data-toggle="collapse" data-target="#collapseDetailsTVA" aria-expanded="false" aria-controls="collapseDetailsTVA"><i class="collapseArrow collapseArrowUp fas fa-chevron-down"></i> <span data-trad="lbl_including_taxes">dont TVA</span></div>
                    </div>
                    <div class="col-4 col-md-3 text-right">
                        <div class="priceTotal" data-priceformat="<%= TauxTvaSubTotalTTC %>"><%= TauxTvaSubTotalTTC %></div>
                    </div>
                </div>
            </div>
            <% } %>
        </div>
  
    <!-- recap content end -->
</div>
</div>
	<!-- btns start -->
    <div id="callToActionWrapper" class="bigBlockWrapper">
    	<div class="row">
    		<div class="col mb-3" id="checkboxExtraFormWrapper">
    			<div class="conditionventes checkbox">
					<input type="checkbox" name="" id="checkBoxconditionventes" required>
					<label for="checkBoxconditionventes"><span id="lbl_conditions_vente" data-fileurl='<%=CGVFilePath %>'>En cochant, j'accepte les <a class="seeConditionsDeVentes" href="<%=CGVFilePath %>">conditions de vente</a></span><span class="text-danger">*</span></label>
				</div>
				<!-- extra checkbox, input, textarea -->
				<%= ResaPayExtraForm %>
				<div><small class="text-danger" data-trad="lbl_resapay_mandatory_fields">* Merci de bien vouloir choisir un mode d'obtention puis de remplir ou cocher tous les champs obligatoires</small></div>
    		</div>
    	</div>
        <div class="row">
            <div class="col">
                <a href="ResaList.aspx?idstructure=<%= Basket.StructureId %>" target="_self" class="btn btn-secondary" data-trad="btn_back_to_reservation_list">Revenir à la liste des réservations</a>
            </div>
            <div class="col text-right">
                <button class="btn btn-primary disabled" id="payOrder" data-trad="btn_confirm_pay_reservation" disabled>Valider et payer</button>
            </div>
        </div>
    </div>
    <!-- btns end -->
    <!-- commentaires bas -->
    <div id="divCommentaireBas" class="small">
        <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
    </div>
    <% @Import Namespace = "Newtonsoft.Json" %>
    <%
    dynamic lstTauxTvaG = JsonConvert.SerializeObject(lstTauxTvaGrouped);
    %>
    <script type="text/javascript">
    	var basketId = "<%= Basket.BasketId %>";
    	var structureId = "<%= Basket.StructureId %>";
    	var langCode = "<%= LangCode %>";
    	var formuleId = "<%= FormuleId %>";
    	var lstTauxTvaGroupedOriginal = <%= lstTauxTvaG %>;
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ExtraScripts" runat="server">


</asp:Content>
