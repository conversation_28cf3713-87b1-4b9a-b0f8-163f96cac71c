﻿using customerArea.App_Code;
using Ionic.Zip;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using utilitaires2010;
using ws_bll.WT;
using ws_DTO;
using ws_DTO.objets_liaisons;

namespace customerArea
{
    public partial class Attachments : basePage
    {
        private List<string> lstExtensions = new List<string>();

        public long MaxSizeAttachment { get; set; } = 2000;
        protected void Page_Load(object sender, EventArgs e)
        {

            utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
            mySSC = mySSC.GetDictionaryFromCache(IdStructure);

            if (!Page.IsPostBack)
            {
                if (!UserIsAuthentified)
                {
                    string pageIdentif = "Login.aspx";

                    string qs = Page.Request.Url.Query.Replace("resetI=1", "wrst=1");
                    string fileLogin = pageIdentif + qs;

                    Response.Redirect(fileLogin, true);
                }

                //WebPartManager1.StaticConnections.Clear();
                GestionTraceManager.WriteLog(IdStructure, "page_load Attachments...", TypeLog.LoadPage);
                string fileP = Page.Request.FilePath;
                fileP = fileP.Substring(fileP.LastIndexOf('/') + 1, fileP.LastIndexOf(".") - fileP.LastIndexOf('/') - 1);

                Ihm myIhm = new Ihm();

                int resulEvent = 0;
                if (Request.QueryString["eventId"] != "" && Request.QueryString["eventId"] != null && int.TryParse(Request.QueryString["eventId"], out resulEvent))
                {
                    System.Web.HttpContext.Current.Session["eventId"] = resulEvent;
                }



                string idpa = "0";
                if (Session["ProfilAcheteurId"] != null)
                {
                    idpa = (string)Session["ProfilAcheteurId"];
                }

                int structureId = 0;
                if (Request.QueryString["idstructure"] != "" && Request.QueryString["idstructure"] != null && int.TryParse(Request.QueryString["idstructure"], out structureId))
                {
                    log.Debug(IdStructure, " Waitlist page load structureId : " + structureId);
                    System.Web.HttpContext.Current.Session["idstructure"] = structureId;
                }
                else if (GetStructureId() > 0)
                {
                    structureId = GetStructureId();
                }
                else
                {

                     int.TryParse(System.Web.HttpContext.Current.Session["idstructure"]?.ToString(), out structureId);
                }

                if (mySSC["SUPPORTINGDOCUMENTSSIZEATTACHMENTS"] != null)
                {
                    //taille du répertoire 
                    MaxSizeAttachment = long.Parse(mySSC["SUPPORTINGDOCUMENTSSIZEATTACHMENTS"].ToString());

                }




                string plateformCode = "";
                if (Request.QueryString["plateformCode"] != "" && Request.QueryString["plateformCode"] != null)
                {
                    plateformCode = Request.QueryString["plateformCode"].ToString();
                    System.Web.HttpContext.Current.Session["plateformCode"] = Request.QueryString["plateformCode"].ToString();
                }

                string lang = customerArea.App_Code.Initialisations.GetUserLanguage();

                List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
                        {
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
                        };


                dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, structureId, resulEvent, int.Parse(idpa), plateformCode, lang);


                if (!customerArea.App_Code.Initialisations.IsAuthorizedPage(globalPlateform, "myattachments"))
                {

                    GestionTraceManager.WriteLogError(IdStructure, "L'affichage de la page myattachments est false dans le fichier appsettings.json");
                    Response.Redirect("error.aspx");
                }


                #region literaux pour insertion commentaires, tags, etc

                litForCommentaireBas.Text = GetLiteralCommentaireEnclosed(fileP, "Bas", resulEvent);
                litForCommentaireHaut.Text = GetLiteralCommentaireEnclosed(fileP, "Haut", resulEvent);
                #endregion


                #region fichiers javascripts par page/structures
                // envoie le fichier javascript propres à la page 
                System.Text.StringBuilder sb_insere_javascriptsfiles = new System.Text.StringBuilder();
                //basePage bp = new basePage();
                //Urls url = new Urls();

                Urls url = new Urls();
                //ScriptManager.RegisterStartupScript(this.Page, typeof(Page), string.Format("StartupInclude"), sb_insere_javascriptsfiles.ToString(), false);
                sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile(fileP, resulEvent, true));
                //   sb_insere_javascriptsfiles.AppendLine(url.ScriptJavaScriptFile("commons", resulEvent, true));

                ScriptManager.RegisterStartupScript(Page, typeof(Page), "Master", sb_insere_javascriptsfiles.ToString(), false);
                #endregion
            }


            /* string IdStructure = System.Web.HttpContext.Current.Session["idstructure"].ToString();
             int idStructureForTry;
             int.TryParse(Session["idstructure"].ToString(), out idStructureForTry);
             */
            int.TryParse(Session["SVarUserIdentityID"].ToString(), out int idIdentiteForTry);

            string physicalPathOfAttachments = App_Code.Initialisations.GetKeyAppSettings("physicalPathOfAttachments");
            physicalPathOfAttachments = physicalPathOfAttachments.Replace("[idstructureSur4zeros]", IdStructure.ToString("0000")).Replace("[customerId]", idIdentiteForTry.ToString());

          
            try
            {

                if (Request.Files.Count > 0)  //Compare File Count    
                {
                    foreach (string s in Request.Files)
                    {
                        HttpPostedFile file = Request.Files[s];
                        int fileSizeInBytes = file.ContentLength;

                        if (!Directory.Exists(physicalPathOfAttachments))
                        {
                            System.IO.Directory.CreateDirectory(physicalPathOfAttachments);
                        }

                        string authorizedExtensions = App_Code.Initialisations.GetKeyAppSettings("extensionsOfAttachments");
                        lstExtensions = authorizedExtensions.Split(',').ToList();

                        //vérifie si l'extension est défini dans la liste 
                        if (lstExtensions.Contains(Path.GetExtension(file.FileName).ToLower()))
                        {
                            //si le config.ini contient ce paramètrage
                            if (mySSC["SUPPORTINGDOCUMENTSSIZEATTACHMENTS"] != null)
                            {
                                //taille du répertoire 
                                long lengthOfcurrentDirectorie = GetDirectorySize(physicalPathOfAttachments);
                                long sizeattachmentmax = long.Parse(mySSC["SUPPORTINGDOCUMENTSSIZEATTACHMENTS"].ToString());

                                long totalLength = file.ContentLength + lengthOfcurrentDirectorie;

                                if (totalLength <= sizeattachmentmax)
                                {
                                    // IMPORTANT! Make sure to validate uploaded file contents, size, etc. to prevent scripts being uploaded into your web app directory
                                    string savedFileName = Path.Combine(physicalPathOfAttachments + file.FileName);
                                    file.SaveAs(savedFileName);
                                }
                                else
                                {
                                    log.Debug(IdStructure, "le total des documents dépasse le total du config.ini " + totalLength + " " + sizeattachmentmax);
                                    GestionTraceManager.WriteLogError(IdStructure, "le total des documents dépasse le total du config.ini ");

                                    Response.ClearHeaders();
                                    Response.ClearContent();
                                    Response.StatusCode = 600;
                                    // Response.Status = "le total des documents dépasse " + sizeattachmentmax;
                                    Response.StatusDescription = "Internal Error";

                                    Response.ContentType = "text/plain";
                                    Response.Write("Error");
                                }
                            }
                            else
                            {
                                log.Debug(IdStructure, "SUPPORTINGDOCUMENTSSIZEATTACHMENTS n'est pas défini dans le config.ini ");
                                GestionTraceManager.WriteLogError(IdStructure, "SUPPORTINGDOCUMENTSSIZEATTACHMENTS n'est pas défini dans le config.ini ");
                            }
                        }
                        else
                        {
                            log.Error(IdStructure, "extension bad !!! " + file.FileName);
                            GestionTraceManager.WriteLogError(IdStructure, "extension bad !!! " + file.FileName);
                        }
                    }


                    //  Response.ContentType = "text/plain";
                    // Response.Write("Success");


                    //   string json = JsonConvert.SerializeObject(new { Message = "Missing Something" });

                    // return Json(new { Message = "Missing Something", JsonRequestBehavior.AllowGet });

                }
            }
            catch (Exception ex)
            {
                Response.ContentType = "text/plain";
                Response.Write(ex.Message);
            }

        }



        private static long GetDirectorySize(string folderPath)
        {
            DirectoryInfo di = new DirectoryInfo(folderPath);
            return di.EnumerateFiles("*.*", SearchOption.AllDirectories).Sum(fi => fi.Length);
        }

        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string VerifImage()
        {
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
            {
                foreach (string s in System.Web.HttpContext.Current.Request.Files)
                {
                    HttpPostedFile file = System.Web.HttpContext.Current.Request.Files[s];
                    int fileSizeInBytes = file.ContentLength;
                }
            }
            return "danger:msg_error_lost_session";
        }



        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string DeleteImage(string fileName)
        {
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
            {
                int identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                int idStructure = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());

                string physicalPathOfAttachments = App_Code.Initialisations.GetKeyAppSettings("physicalPathOfAttachments");
                physicalPathOfAttachments = physicalPathOfAttachments.Replace("[idstructureSur4zeros]", idStructure.ToString("0000")).Replace("[customerId]", identiteId.ToString());

                if (File.Exists(physicalPathOfAttachments + fileName))
                {
                    File.Delete(physicalPathOfAttachments + fileName);
                    return "success:msg_success_delete_file";
                }
                return "danger:msg_error_file_not_exist";
            }
            return "danger:msg_error_lost_session";
        }

        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static List<InfoCompEntity> LoadLogsAttachments()
        {
            List<InfoCompEntity> lstLogs = new List<InfoCompEntity>();
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
            {
                int identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                int idStructure = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());

                int logInfoCompId = GetLogInfoCompId(idStructure);
                if (logInfoCompId > -1)
                {
                    wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                    List<int> lstInfoComps = new List<int>
                    {
                        logInfoCompId
                    };

                    lstLogs = wcfThemis.GetListInfoCompOnIdentite(idStructure.ToString("0000"), identiteId, lstInfoComps.ToArray()).ToList();
                    lstLogs = lstLogs.Where(l => l.IsActive == true).ToList();
                }

                return lstLogs;
            }

            return lstLogs;
        }
      
        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string DeleteImageFolder()
        {
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
            {
                int identiteId = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                int idStructure = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());

                string physicalPathOfAttachments = App_Code.Initialisations.GetKeyAppSettings("physicalPathOfAttachments");
                physicalPathOfAttachments = physicalPathOfAttachments.Replace("[idstructureSur4zeros]", idStructure.ToString("0000")).Replace("[customerId]", identiteId.ToString());

                if (Directory.Exists(physicalPathOfAttachments))
                {
                    try
                    {
                        Directory.Delete(physicalPathOfAttachments, true);
                    }
                    catch (IOException)
                    {
                        Thread.Sleep(0);
                        Directory.Delete(physicalPathOfAttachments, true);
                    }
                    return "success:msg_success_delete_folder";
                }
                //dossier de l'utilisateur n'existe pas
                return "danger:msg_error_folder_not_exist";
            }
            //session perdue
            return "danger:msg_error_lost_session";
        }

        /// <summary>
        /// Envoie les documents uploader a la structure par email défini dans le config.ini
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        [System.Web.Script.Services.ScriptMethod]
        public static string SendDocuments()
        {
            if (System.Web.HttpContext.Current.Session["SVarUserIdentityID"] != null && System.Web.HttpContext.Current.Session["IdStructure"] != null)
            {
                int idIdentite = int.Parse(System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());
                int idStructure = int.Parse(System.Web.HttpContext.Current.Session["IdStructure"].ToString());

                string physicalPathOfAttachments = App_Code.Initialisations.GetKeyAppSettings("physicalPathOfAttachments");
                physicalPathOfAttachments = physicalPathOfAttachments.Replace("[idstructureSur4zeros]", idStructure.ToString("0000")).Replace("[customerId]", idIdentite.ToString());

                if (!Directory.Exists(physicalPathOfAttachments))
                {
                    System.IO.Directory.CreateDirectory(physicalPathOfAttachments);
                }
                if (Directory.Exists(physicalPathOfAttachments))
                {
                    try
                    {
                        utilitaires2010.MyDictionary mySSC = new utilitaires2010.MyDictionary();
                        mySSC = mySSC.GetDictionaryFromCache(idStructure);

                        try
                        {
                            int numPostalTelEmail = int.Parse(mySSC["VARIABLESEMAIL"].ToString());
                            wcf_WsThemis.Iwcf_wsThemisClient wcfThemis = new wcf_WsThemis.Iwcf_wsThemisClient();

                            string lang = App_Code.Initialisations.GetUserLanguage();

                            List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
                            {
                                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false }
                            };

                            dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, idStructure, 0, 0, "", lang);

                            dynamic extensionArchiveAttachments = globalPlateform.attachments.extensionArchiveAttachments.Value;
                            if (globalPlateform.customer != null && globalPlateform.customer.attachments != null && globalPlateform.customer.attachments.extensionArchiveAttachments != null)
                            {
                                extensionArchiveAttachments = globalPlateform.customer.attachments.extensionArchiveAttachments.Value;
                            }

                            dynamic archiveAttachments = globalPlateform.attachments.archiveAttachments.Value;
                            if (globalPlateform.customer != null && globalPlateform.customer.attachments != null && globalPlateform.customer.attachments.archiveAttachments != null)
                            {
                                archiveAttachments = globalPlateform.customer.attachments.archiveAttachments.Value;
                            }

                            eMail em = new eMail();
                            bool isSended = false;

                            IdentiteEntity thisIdentite = wcfThemis.GetCustomerProfileOfEmailOrIdentiteId(
                               idStructure.ToString("0000"),
                               string.Empty,
                               string.Empty,
                               idIdentite.ToString(),
                               numPostalTelEmail,
                               "N"
                            );
                            List<string> lstFilesUploaded = Directory.GetFiles(physicalPathOfAttachments, "*.*").ToList();


                            int logInfoCompId = GetLogInfoCompId(idStructure);
                            if (logInfoCompId > -1)
                            {
                                List<InfoCompEntity> lstInfoComp = new List<InfoCompEntity>();
                                foreach (string thisFile in lstFilesUploaded)
                                {
                                    FileInfo oFileInfo = new FileInfo(thisFile);

                                    string fileName = "";
                                    if (oFileInfo.Name.Length > 15)
                                    {
                                        fileName = oFileInfo.Name.Substring(0, 15) + Path.GetExtension(oFileInfo.Name) + " " + FileSizeFormatter.FormatSize(oFileInfo.Length);
                                    }
                                    else
                                    {
                                        fileName = oFileInfo.Name + " " + FileSizeFormatter.FormatSize(oFileInfo.Length);
                                    }

                                    InfoCompEntity ic = new InfoCompEntity()
                                    {
                                        infocomp_id = logInfoCompId,
                                        ValeurParam1 = DateTime.Now.ToString("dd/MM/yyyy hh:mm"),
                                        ValeurParam3 = fileName,
                                        TypeInfoComp = 2, // 2 ==> Date
                                        ValeurTypeInfoComp = 2,
                                        IsActive = true
                                    };

                                    lstInfoComp.Add(ic);
                                }

                                bool isInfoCompInserted = wcfThemis.UpdateListInfoCompOnIdentite(idStructure.ToString("0000"), idIdentite, lstInfoComp.ToArray());
                            }

                            if (archiveAttachments)
                            {
                                string zipNamepath = physicalPathOfAttachments + "attachment_" + idStructure + "_" + idIdentite + extensionArchiveAttachments;
                                // ZIP ALL FILES IN THE FOLDER.
                                using (ZipFile zip = new ZipFile())
                                {
                                    zip.AddFiles(lstFilesUploaded, thisIdentite.Identite_id.ToString());
                                    zip.Save(zipNamepath);  
                                }

                                isSended = em.SendSupportingDocuments(idStructure, thisIdentite, 0, "MailSupportingDocumentsCustomer", new List<string>() { zipNamepath }, true);
                                isSended = em.SendSupportingDocuments(idStructure, thisIdentite, 0, "MailSupportingDocuments", new List<string>() { zipNamepath }, false);

                            }
                            else
                            {

                                //List<string> lstFilesToZipped = Directory.GetFiles(physicalPathOfAttachments, "*.*").ToList();

                                isSended = em.SendSupportingDocuments(idStructure, thisIdentite, 0, "MailSupportingDocumentsCustomer", lstFilesUploaded, true);
                                isSended = em.SendSupportingDocuments(idStructure, thisIdentite, 0, "MailSupportingDocuments", lstFilesUploaded, false);
                            }


                            if (isSended)
                            {
                                //on supprime tous les documents 
                                Directory.Delete(physicalPathOfAttachments, true);
                            }

                        }

                        catch (Exception ee)
                        {
                            GestionTraceManager.WriteLogError(idStructure, ee.Message);
                        }

                    }
                    catch (IOException ex)
                    {
                        GestionTraceManager.WriteLogError(idStructure, ex.Message);
                    }
                    return "success:msg_success_send_documents";
                }

                if (Directory.Exists(physicalPathOfAttachments))
                {
                    Directory.Delete(physicalPathOfAttachments);
                }
                    //dossier de l'utilisateur n'existe pas
                    return "danger:msg_error_folder_not_exist";
            }
            //session perdue
            return "danger:msg_error_lost_session";
        }

        private static int GetLogInfoCompId(int StructureId)
        {
            string lang = App_Code.Initialisations.GetUserLanguage();

            List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
            {
                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
            };

            dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, StructureId, 0, 0, "", lang);

            return (int)globalPlateform.attachments.logsInfoCompId.Value;

        }

    }
}