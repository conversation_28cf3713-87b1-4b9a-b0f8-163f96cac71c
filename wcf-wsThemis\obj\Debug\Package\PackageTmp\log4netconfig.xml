﻿<log4net>
  <!-- <PERSON> is set to be a ConsoleAppender -->
  <appender name="A1" type="log4net.Appender.ConsoleAppender">

    <!-- <PERSON> uses PatternLayout -->
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%-4timestamp [%thread] %-5level %logger %ndc - %message%newline" />
    </layout>
  </appender>
  <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
    <file value="c:\wcf-wsThemislog-file.txt" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="10" />
    <maximumFileSize value="1MB" />
    <staticLogFileName value="true" />
    <layout type="log4net.Layout.PatternLayout" >
      <conversionPattern value="%d{dd HH:mm:ss.fff} %-4timestamp [%thread] %-5level %logger %ndc ->> %message%newline" />
    </layout>
  </appender>
  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date{ABSOLUTE} 
    [%thread] %level %logger - %message%newline"/>
    </layout>
    <filter type="log4net.Filter.StringMatchFilter">
      <stringToMatch value="test" />
    </filter>
    <filter type="log4net.Filter.DenyAllFilter" />
  </appender>
  <!-- Set root logger level to DEBUG and its only appender to A1 -->
  <root>
    <level value="ALL" />
    <appender-ref ref="A1" />
    <appender-ref ref="RollingFileAppender" />
  </root>
</log4net>